#!/usr/bin/env python
import os
import argparse
from pathlib import Path
import subprocess

def get_args():
    parser = argparse.ArgumentParser(description="Run inference on all images in a directory")
    parser.add_argument("-i", "--input_dir", type=str, required=True, 
                        help="Directory containing images to process")
    parser.add_argument("-o", "--output_dir", type=str, required=True, 
                        help="Directory to save results")
    parser.add_argument("-c", "--config", type=str, 
                        default="config/bf_slim_loss_csp_15m_fixsatu_gamma_modval3loss_2xview.py",
                        help="Path to configuration file")
    parser.add_argument("-b", "--batch_size", type=int, default=2,
                        help="Batch size for inference")
    return parser.parse_args()

def main():
    args = get_args()
    
    # Create input path with image subdirectory structure expected by inference_huge_image.py
    input_path = Path(args.input_dir)
    image_dir = input_path
    
    
    # Create output directory if it doesn't exist
    output_path = Path(args.output_dir)
    if not output_path.exists():
        os.makedirs(output_path, exist_ok=True)
    
    # Run inference_huge_image.py
    cmd = [
        "python", "inference_huge_image.py",
        "-i", str(input_path),
        "-c", args.config,
        "-o", str(output_path),
        "-b", str(args.batch_size)
    ]
    
    print(f"Running command: {' '.join(cmd)}")
    subprocess.run(cmd)
    
    print(f"Inference completed. Results saved to {output_path}")

if __name__ == "__main__":
    main()