{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from torch.utils.data import DataLoader\n", "from torchgeo.datasets import Sentinel2,stack_samples,unbind_samples\n", "from torchgeo.samplers import RandomGeoSampler\n", "import matplotlib.pyplot as plt\n", "import sys\n", "import os\n", "from os import path\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["root='/mnt/mfs1/DBankData/Sentinel2.Data.C/51S/TR/S2B_MSIL1C_20211229T024119_N0301_R089_T51STR_20211229T035943.SAFE'"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "'collections.defaultdict' object has no attribute 'shape'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[21], line 7\u001b[0m\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m batch \u001b[38;5;129;01min\u001b[39;00m dataloader:\n\u001b[1;32m      6\u001b[0m     sample \u001b[38;5;241m=\u001b[39m unbind_samples(batch)[\u001b[38;5;241m0\u001b[39m]\n\u001b[0;32m----> 7\u001b[0m     \u001b[38;5;28mprint\u001b[39m(\u001b[43mbatch\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mshape\u001b[49m)\n\u001b[1;32m      8\u001b[0m     \u001b[38;5;28mprint\u001b[39m(sample\u001b[38;5;241m.\u001b[39mshape)\n\u001b[1;32m     10\u001b[0m     \u001b[38;5;66;03m# dataset.plot(sample)\u001b[39;00m\n\u001b[1;32m     11\u001b[0m     \u001b[38;5;66;03m# plt.axis(\"off\")\u001b[39;00m\n\u001b[1;32m     12\u001b[0m     \u001b[38;5;66;03m# plt.show()\u001b[39;00m\n", "\u001b[0;31mAttributeError\u001b[0m: 'collections.defaultdict' object has no attribute 'shape'"]}], "source": ["dataset = Sentinel2(root)\n", "sampler = RandomGeoSampler(dataset, size=4096, length=3)\n", "dataloader = DataLoader(dataset, sampler=sampler, collate_fn=stack_samples)\n", "\n", "for batch in dataloader:\n", "    sample = unbind_samples(batch)[0]\n", "    print(batch.shape)\n", "    print(sample.shape)\n", "\n", "    # dataset.plot(sample)\n", "    # plt.axis(\"off\")\n", "    # plt.show()"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"data": {"text/plain": ["[CRS.from_epsg(32651)]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["batch['crs']"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['crs', 'bbox', 'image'])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["batch.keys()"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["[BoundingBox(minx=243408.26500177383, maxx=284368.26500177383, miny=3549062.2511553764, maxy=3590022.2511553764, mint=1640745679.0, maxt=1640745679.999999)]"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["batch['bbox']"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["import datetime"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["datetime.datetime(2021, 12, 29, 2, 41, 19)"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["datetime.datetime.fromtimestamp(1640745679.0)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["sampler = RandomGeoSampler(ds, size=2000, length=2)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<torchgeo.datasets.sentinel.Sentinel2 at 0x7f2adfb3e1f0>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["ds"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'RandomGeoSampler' object is not an iterator", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "Cell \u001b[0;32mIn[6], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m ds\u001b[38;5;241m.\u001b[39mplot(\u001b[38;5;28;43mnext\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msampler\u001b[49m\u001b[43m)\u001b[49m)\n", "\u001b[0;31mTypeError\u001b[0m: 'RandomGeoSampler' object is not an iterator"]}], "source": ["ds.plot(next(sampler))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.1.0"}}, "nbformat": 4, "nbformat_minor": 2}