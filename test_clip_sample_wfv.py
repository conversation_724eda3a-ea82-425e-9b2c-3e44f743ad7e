import sys
import os
from os import path
sys.path.append(path.abspath('../series_cloud_research'))
import utils.base as base
import pathlib
import random
import pandas as pd
import rioxarray as riox
random.seed(12)
from tqdm import tqdm
# 1月单独做的时候用的seed=10
root = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfv/image')
oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/img')
p_size = 2040
edge_size = 24
half_tr = 8
flist = list(root.rglob('*.tif'))
for tname in tqdm(flist):
    xa  = riox.open_rasterio(tname)
    bn = tname.name
    # xa = xr[list(xr.keys())[0]]
    df = pd.DataFrame(data={'name':[],'yoff':[],'xoff':[]})
    # crs = xr.rio.crs
    n_try=0
    while True:
        n_try += 1
        if n_try > 100:
            break
        yoff = random.randint(edge_size, xa.shape[1]-1-p_size-edge_size)
        xoff = random.randint(edge_size, xa.shape[2]-1-p_size-edge_size)
        yoff = yoff//4*4
        xoff = xoff//4*4
        # print(yoff, xoff)
        # print(df)
        oname = oroot
        os.makedirs(oname.__str__(), exist_ok=True)
        oname = oname/f'{bn}-{int(xa.y[yoff]+half_tr)}-{int(xa.x[xoff]-half_tr)}.tif'
        if oname.exists():
            continue
        if len(df)>0:
            distance = ((df.xoff-xoff)**2+(df.yoff-yoff)**2)**0.5
            # print(distance)
            if distance.min() < 2700:
                continue
        patch = xa[:, yoff:yoff+p_size, xoff:xoff+p_size].compute()
        mask = (patch == 0)
        # 沿着所有维度计算掩码中 True 值的数量
        count_zeros = mask.sum()
        if count_zeros > 40:
            continue
        
        # patch.rio.to_raster(str(oname),driver='COG')
        df.loc[len(df)] = {'name':bn,'yoff':yoff,'xoff':xoff}
        if len(df)>=10:
            break
