from train_supervision import *
from functools import partial
import shutil
from os import path
from pathlib import Path

cid = {
    'C':0,
    'O':1,
    'T':2,
    'H':3,
    'S':4,
    'I':5
}
allns = ['C','O','T','H','S','I']

def sumA_ij(A, ins,jns):
    ii = [cid[n] for n in ins]
    jj = [cid[n] for n in jns]
    result = 0
    for i in ii:
        for j in jj:
            result += A[i][j]
    return result

def precision(A,ns):
    return sumA_ij(A, ns, ns)/sumA_ij(A,allns,ns)

def recall(A,ns):
    return sumA_ij(A, ns, ns)/sumA_ij(A,ns,allns)

def F1(A, pf, rf):
    return 2*pf(A)*rf(A)/(pf(A)+rf(A))

GUP = partial(precision,ns=['C','I','H'])
GUR = partial(recall,ns=['C','I','H'])
SUP = partial(precision,ns=['C','I'])
SUR = partial(recall,ns=['C','I'])
SCP = partial(precision,ns=['O','T'])
SCR = partial(recall,ns=['O','T'])
OP = partial(precision,ns=['O'])
OR = partial(recall,ns=['O'])
GUF1 = partial(F1,pf=GUP,rf=GUR)
SUF1 = partial(F1,pf=SUP,rf=SUR)
SCF1 = partial(F1,pf=SCP,rf=SCR)
OF1 = partial(F1,pf=OP,rf=OR)

class Gfcloud_Train(Supervision_Train):
    def __init__(self, config):
        super().__init__(config)
        self.metrics_train = Gfcloud_Evaluator(num_class=config.num_classes)
        self.metrics_val = Gfcloud_Evaluator(num_class=config.num_classes)

    def on_train_epoch_end(self):
        OA = np.nanmean(self.metrics_train.OA())
        F1 = np.nanmean(self.metrics_train.F1())
        iou_per_class = self.metrics_train.Intersection_over_Union()
        guf1 = self.metrics_train.GUF1()
        suf1 = self.metrics_train.SUF1()
        eval_value = {'F1': F1,
                      'OA': OA,
                      'GUF1':guf1,
                      'SUF1':suf1}
        print('train:', eval_value)
        iou_value = {}
        for class_name, iou in zip(self.config.classes, iou_per_class):
            iou_value[class_name] = iou
        print(iou_value,'\n')
        self.metrics_train.reset()
        log_dict = {'train_F1': F1, 'train_OA': OA, 'train_GUF1':guf1, 'train_SUF1':suf1}
        self.log_dict(log_dict, prog_bar=True)

    def on_validation_epoch_end(self):
        OA = np.nanmean(self.metrics_val.OA())
        F1 = np.nanmean(self.metrics_val.F1())
        iou_per_class = self.metrics_val.Intersection_over_Union()
        guf1 = self.metrics_val.GUF1()
        suf1 = self.metrics_val.SUF1()
        eval_value = {'F1': F1,
                      'OA': OA,
                      'GUF1':guf1,
                      'SUF1':suf1}
        print('val:', eval_value)
        iou_value = {}
        for class_name, iou in zip(self.config.classes, iou_per_class):
            iou_value[class_name] = iou
        print(iou_value,'\n')
        self.metrics_val.reset()
        log_dict = {'val_F1': F1, 'val_OA': OA, 'val_GUF1':guf1, 'val_SUF1':suf1}
        self.log_dict(log_dict, prog_bar=True)


class Gfcloud_Evaluator(Evaluator):
    def __init__(self, num_class):
        super().__init__(num_class)
    
    def GUF1(self):
        return GUF1(self.confusion_matrix)

    def SUF1(self):
        return SUF1(self.confusion_matrix)
    
    def SUP(self):
        return SUP(self.confusion_matrix)
    
    def SUR(self):
        return SUR(self.confusion_matrix)
    
    def SCF1(self):
        return SCF1(self.confusion_matrix)
    
    def OF1(self):
        return OF1(self.confusion_matrix)
    
    def SCP(self):
        return SCP(self.confusion_matrix)
    
    def SCR(self):
        return SCR(self.confusion_matrix)
    
    def OP(self):
        return OP(self.confusion_matrix)
    
    def OR(self):
        return OR(self.confusion_matrix)

# training
def main():
    args = get_args()
    config = py2cfg(args.config_path)
    config.train_dataset.load()
    config.val_dataset.load()
    seed_everything(42)
    
    checkpoint_callback = ModelCheckpoint(save_top_k=config.save_top_k, monitor=config.monitor,
                                          save_last=config.save_last, mode=config.monitor_mode,
                                          dirpath=config.weights_path,
                                          filename=config.weights_name)
    logger = CSVLogger('lightning_logs', name=config.log_name)

    model = Gfcloud_Train(config)
    if config.pretrained_ckpt_path:
        model = Gfcloud_Train.load_from_checkpoint(config.pretrained_ckpt_path, config=config)

    trainer = pl.Trainer(devices=config.gpus, max_epochs=config.max_epoch, accelerator='auto',
                         check_val_every_n_epoch=config.check_val_every_n_epoch,
                         callbacks=[checkpoint_callback], strategy='auto',
                         logger=logger)
    trainer.fit(model=model, ckpt_path=config.resume_ckpt_path)
    shutil.copy(args.config_path, Path(trainer.log_dir)/args.config_path.name)


if __name__ == "__main__":
   main()