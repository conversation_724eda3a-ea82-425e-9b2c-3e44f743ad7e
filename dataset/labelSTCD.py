import glob
import os
from collections.abc import Iterable, Sequence
from typing import Any, Callable, Optional, Union, cast

import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from rasterio.crs import CRS
from torch import Tensor

from torchgeo.datasets import RasterDataset
from torchgeo.datasets import BoundingBox

class labelSTCDs2(RasterDataset):
    class_dict = {
        'clear':      0,
        'cloud':     1,
        'thin cloud' : 2,
        'haze':       3,
        'shadow':     4,
        'snow':       5
    }
    short_names = ['C','O','T','H','S','I']

    invert_class_dict = {v:k for k,v in class_dict.items()}
    class_names = [invert_class_dict[i] for i in range(len(invert_class_dict))]
    filename_glob = "S2*.tif"
    filename_regex = r"""
        ^S.*_(?P<tile>\d{{2}}[A-Z]{{3}})
        _(?P<date>\d{{8}}T\d{{6}})
        \..*$
    """
    date_format = "%Y%m%dT%H%M%S"
    all_bands = ['clm']

    def __init__(
        self,
        paths: Union[str, Iterable[str]] = "data",
        crs: Optional[CRS] = None,
        res: float = 10,
        bands: Optional[Sequence[str]] = None,
        transforms: Optional[Callable[[dict[str, Any]], dict[str, Any]]] = None,
        cache: bool = True,
    ) -> None:
        """Initialize a new Dataset instance.

        Args:
            paths: one or more root directories to search or files to load
            crs: :term:`coordinate reference system (CRS)` to warp to
                (defaults to the CRS of the first file found)
            res: resolution of the dataset in units of CRS
                (defaults to the resolution of the first file found)
            bands: bands to return (defaults to all bands)
            transforms: a function/transform that takes an input sample
                and returns a transformed version
            cache: if True, cache file handle to speed up repeated sampling

        Raises:
            FileNotFoundError: if no files are found in ``paths``

        .. versionchanged:: 0.5
            *root* was renamed to *paths*
        """
        bands = bands or self.all_bands
        self.filename_glob = self.filename_glob.format(bands[0])
        self.filename_regex = self.filename_regex.format(res)

        super().__init__(paths, crs, res, bands, transforms, cache)