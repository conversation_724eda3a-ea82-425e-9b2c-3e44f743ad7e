import os
from os import path
from typing import Any
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
import pandas as pd
import rasterio as rio
from rasterio.enums import Resampling
import numpy as np
from skimage import exposure
import torch
from multiprocessing import Pool, cpu_count
from functools import partial
import random
import matplotlib.pyplot as plt
import torchvision.transforms as T
from matplotlib.colors import ListedColormap
from tqdm import tqdm
import concurrent.futures

MEAN_SUB10 = (3573.4307, 3376.8762, 3532.9165, 4070.9111)

STD_SUB10 = (2642.7964, 2486.646, 2605.6665, 2378.3767)

LOAD_MINMAX = 'minmax'
LOAD_TOA_NORMAL = 'toa'

class_dict = {
        'clear':      0,
        'cloud':     1,
        'thin cloud' : 2,
        'haze':       3,
        'shadow':     4,
        'snow':       5
    }
short_names = ['C','O','T','H','S','I']

invert_class_dict = {v:k for k,v in class_dict.items()}
CLASSES = [invert_class_dict[i] for i in range(len(invert_class_dict))]


def _fns2sample(p_im, p_label, p_ref=None, th=0.8, vmin=None, vmax=None, means=None, stds=None, mode=LOAD_MINMAX, res_scale=1):
    '''
    cs指标更敏感，usable data的precision较高。('cs', 'cs_cdf')
    :ref: Cloud Score +
    '''
    with rio.open(p_im) as src:
        dst_shape = (int(src.height//res_scale), int(src.width//res_scale))
        a_im = rio.open(p_im).read(out_shape=dst_shape,resampling=Resampling.bilinear)
    a_label = rio.open(p_label).read(1)
    a_ref = rio.open(p_ref).read(1)
    mask_ref = a_ref > th
    a_label[mask_ref&(a_label!=5)] = 0
    a_label[(a_ref < 0.15)&(a_label==5)] = 1
    
    if mode == LOAD_MINMAX:
        ima = minmax(a_im, vmin=vmin, vmax=vmax)
    elif mode == LOAD_TOA_NORMAL:
        ima = normalize(a_im, means, stds)
    data = {
        'img_id': path.splitext(path.basename(p_im))[0],
        'im': ima,
        'label': a_label[np.newaxis, :,:],
        'min': vmin,
        'max': vmax
    }
    # print('hit')
    return data

def imp2sample(p_im:Path):
    p_label = p_im.replace('/img/','/stdl/')
    p_ref = p_im.replace('/img/','/csp/')
    return _fns2sample(p_im, p_label, p_ref)

def row2sample(row,th=0.8,mode=LOAD_MINMAX, means=None, stds=None, res_scale=1):
    assert mode in (LOAD_MINMAX, LOAD_TOA_NORMAL)
    i, item = row
    p_im = item.impath
    p_label = p_im.replace('/img/','/stdl/')
    p_ref = p_im.replace('/img/','/csp/')
    if mode == LOAD_MINMAX:
        i = 0
        vmin, vmax = [], []
        while True:
            try:
                vmin.append(item[f'min_{i}'])
                vmax.append(item[f'max_{i}'])
                i += 1
            except:
                break
        vmin = np.array(vmin)
        vmax = np.array(vmax)
        # l.append(_fns2sample(p_im, p_label, p_ref,vmin=vmin,vmax=vmax))
        return _fns2sample(p_im, p_label, p_ref, th=th, vmin=vmin,vmax=vmax, mode=mode, res_scale=res_scale)
    elif mode == LOAD_TOA_NORMAL:
        return _fns2sample(p_im, p_label, p_ref, th=th, mode=mode, means=means, stds=stds, res_scale=res_scale)

def minmax(im:np.ndarray,p=0.01, vmin=None, vmax=None):
    if (vmin is None) or (vmax is None):
        v_max = np.percentile(im, 100-p, [1, 2])
        v_min = np.percentile(im, p, [1, 2])
    else:
        v_max = vmax
        v_min = vmin
    v_max = v_max[:, np.newaxis,np.newaxis]
    v_min = v_min[:, np.newaxis,np.newaxis]
    # print(v_min,v_max)
    return np.clip((im-v_min)/(v_max-v_min), 0, 1)

def normalize(im:np.ndarray, means, stds):
    im = im.astype(np.float32)
    means = means[:,np.newaxis,np.newaxis]
    stds = stds[:,np.newaxis,np.newaxis]
    return (im - means)/stds

    
def random_clip_gamma(data, win_size=540, match_size=False, g=None, g_range=None, apply_gamma=True, label_scale=6, saturation_jit=False, base_satu=0.5):
    '''
    @g: 指定后可以固定，不再随机
    @label_scale: 6 or 4
    '''
    im = data['im']
    label = data['label']

    # img和label大小不同，需要确保符合倍数关系
    assert win_size % label_scale == 0
    win_size_label = win_size // label_scale

    max_yoff = im.shape[1] - win_size
    max_xoff = im.shape[2] - win_size

    y_off = random.randint(0, max_yoff)
    x_off = random.randint(0, max_xoff)

    y_off_label, x_off_label = y_off//label_scale, x_off//label_scale
    y_off, x_off = int(y_off_label*label_scale), int(x_off_label*label_scale)

    y_slice = slice(y_off, y_off+win_size)
    x_slice = slice(x_off, x_off+win_size)

    y_slice_label = slice(y_off_label, y_off_label+win_size_label)
    x_slice_label = slice(x_off_label, x_off_label+win_size_label)  
    im = im[:, y_slice, x_slice]

    if saturation_jit:
        assert data['min'] is not None
        scale = 10000
        jit_range = (0.9-base_satu)*scale
        new_max = np.minimum((random.random()*jit_range+base_satu)*scale, data['max'])
        # print(new_max)
        scale_d = np.clip(new_max-data['min'],1,10000)
        sj = (data['max']-data['min'])/scale_d
        im = np.clip(im * sj[:,np.newaxis,np.newaxis],0,1)

    if g_range is not None:
        g = random.random()*(g_range[1]-g_range[0])+g_range[0]
    elif g is None:
        g = 0.95 - random.random()*0.45
    if apply_gamma:
        im = exposure.adjust_gamma(im, gamma=g)

    label = torch.from_numpy(label[:, y_slice_label, x_slice_label])
    if match_size:
        f = T.Resize(im.shape[-2:], T.InterpolationMode.NEAREST)
        label = f(label)
    
    im = torch.from_numpy(im)
    return  im, label.squeeze(), data['img_id']

def minmax_dict_from_fname(impath, p=0.01):
    with rio.open(impath) as src:
        h, w  = src.shape
        h,w = h//4, w//4
        im = src.read(out_shape=(h,w))
        v_max = np.percentile(im, 100-p, [1, 2])
        v_min = np.percentile(im, p, [1, 2])
        result = {}
        for i,(b_min,b_max) in enumerate(zip(v_min,v_max)):
            result[f'min_{i}'] = b_min
            result[f'max_{i}'] = b_max
        return result

def sta_imlist(imlist,p=0.01) -> pd.DataFrame:
    print('stating:')
    with Pool(cpu_count()) as p:
        result = list(tqdm(p.imap(minmax_dict_from_fname,imlist)))
    df = pd.DataFrame(data=result)
    df['impath'] = imlist
    return df

class cloudsegs2(Dataset):
    means = np.array([3573.4307, 3376.8762, 3532.9165, 4070.9111])
    stds = np.array([2642.7964, 2486.646, 2605.6665, 2378.3767])
    apply_gamma = False
    base_label_scale = 6
    g_range = None

    def __init__(
            self,
            root, 
            split='train', 
            sample_n_df:int|None=None, 
            sample_size=480, 
            match_size=False, 
            g=0.7, 
            csp_th=0.8,
            use_sta_cache=False,
            is_rgb=False,
            res=10,
            load_mode=LOAD_MINMAX,
            means=None,
            stds=None,
            saturation_jit=False,
            saturation_fixed=False,
            base_satu=0.5,
            g_range=None) -> None:
        '''
        @g: val时g默认0.7, train时改为None，用时随机生成
        '''
        super().__init__()
        if (means != None) or (stds != None):
            raise NotImplementedError
        if load_mode == LOAD_MINMAX:
            self.means = None
            self.stds = None
            self.apply_gamma = True
        assert (saturation_jit & saturation_fixed) is False
        if saturation_fixed:
            self.apply_gamma=False # 兼容旧代码的默认行为

        if g_range is not None:
            if load_mode == LOAD_TOA_NORMAL:
                raise NotImplementedError
            self.g_range = g_range
            self.apply_gamma = True

        self.saturation_jit = saturation_jit
        self.saturation_fixed = saturation_fixed
        self.load_mode = load_mode
        res_scale = res/10
        self.res_scale = res_scale
        assert split in set(['train', 'val'])
        self.is_rgb = is_rgb
        if type(root) == str:
            root = Path(root)
        p_im = root/split/'img'
        self.sample_size = sample_size
        # p_label = root/split/'stdl'
        # p_ref = root/split/'csp'
        list_im = list(p_im.rglob('*.tif'))
        list_im = [str(i) for i in list_im]
        # test 
        # for p in list_im:
        #     if not path.exists(p.replace('/img/','/csp/')):
        #         print(p)
        #         os.remove(p)
        
        
        df_im = pd.DataFrame(data={'impath':list_im})
        if sample_n_df is not None:
            df_im = df_im.sample(n=sample_n_df, ignore_index=True)
        else: 
            sample_n_df = len(list_im)
        self.sample_n_df = sample_n_df

        if (root/'sta.csv').exists() and use_sta_cache:
            print('using cache sta.')
            df_sta = pd.read_csv(root/'sta.csv')
        else:
            df_sta = sta_imlist(df_im.impath)
            if not (root/'sta.csv').exists():
                df_sta.to_csv(root/'sta.csv', index=False)

        df_im = df_sta.loc[df_sta.impath.isin(df_im.impath)]
        assert len(df_im) == sample_n_df
        #随机一次
        df_im = df_im.sample(frac=1.0,ignore_index=True)

        
        if saturation_fixed:
            i = 0
            while f'min_{i}' in df_im.columns:
                df_im[f'min_{i}'] = 0
                df_im[f'max_{i}'] = base_satu*10000
                i += 1

        self.df_im = df_im
        self.csp_th = csp_th
        if split == 'val':
            g = g
        else:
            g = None

        self.preprocess = partial(
            random_clip_gamma, 
            win_size=sample_size, 
            match_size=match_size, 
            g=g, 
            g_range=self.g_range,
            apply_gamma=self.apply_gamma, 
            label_scale=int(self.base_label_scale//res_scale),
            saturation_jit=self.saturation_jit,
            base_satu=base_satu)
        self.data = []
        
    
    def load(self):
        print('loading: ')
        # 使用multiprocess一次性加载所有数据
        # with Pool(min(cpu_count(), self.sample_n_df)) as p:
        with concurrent.futures.ThreadPoolExecutor(os.cpu_count()) as executor:
            # self.data = list(tqdm(p.imap(row2sample, list(df_im.iterrows())), total=len(df_im)))
            # self.data = list(tqdm(p.map(row2sample, list(df_im.iterrows())), total=len(df_im)))
            for result in tqdm(executor.map(
                partial(
                    row2sample,th=self.csp_th,mode=self.load_mode,means=self.means,stds=self.stds, res_scale=self.res_scale), list(self.df_im.iterrows())), total=len(self.df_im)):
                self.data.append(result)
                # pass
            # p.close()
            # p.join()


    def __getitem__(self, index) -> Any:
        sample = self.data[index]
        im, label, img_id = self.preprocess(sample)
        end = 3 if self.is_rgb else 4
        s = slice(end)
        sample = {
            'img': im[s].float(),
            'img_id': img_id,
            'gt_semantic_seg': label.long()
        }
        return sample
    
    def __len__(self):
        return len(self.df_im)

if __name__ == '__main__':
    # a1 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/43SFB/S2B_MSIL1C_20191227T055239_N0208_R048_T43SFB_20191227T074531.tif'
    # a2 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/51STR/S2B_MSIL1C_20191230T024119_N0208_R089_T51STR_20191230T042547.tif'
    # print('test')
    # 336 480 960 
    ds = cloudsegs2('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg',
                    split='val',
                    sample_n_df=50, sample_size=960*2, match_size=False, 
                    # load_mode=LOAD_TOA_NORMAL,
                    csp_th=0.7,
                    use_sta_cache=True,
                    saturation_fixed=True,
                    # saturation_jit=True, 
                    base_satu=0.4,
                    )
    ds.load()
    # random.seed(9)
    # print(ds[0])
    # print(ds[0])
    # print(ds[0])
    # print(ds[0])

    fig, axes = plt.subplots(2,4,constrained_layout=True,subplot_kw={'xticks': [], 'yticks': []})
    cmap_classied = ListedColormap(["white", "#DC0000CC", "#E64B35CC", "#F39B7FCC","#3C5488CC","#4DBBD5CC","#FFF7AC"])
    axes = axes.flatten()
    for i in range(4):
        d = ds[i]
        print(d['img'].shape,d['gt_semantic_seg'].shape)
        axes[i].imshow(d['img'][[0,1,2],:,:].permute(1,2,0))
        # axes[i+4].imshow(d['gt_semantic_seg'].permute(1,2,0),cmap=cmap_classied,vmin=-0.5, vmax=6.5,interpolation='nearest')
        axes[i+4].imshow(d['gt_semantic_seg'],cmap=cmap_classied,vmin=-0.5, vmax=6.5,interpolation='nearest')
        fig.savefig('test15.png')

    # root = Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg')
    # list_im = list(root.rglob('*/img/*/*.tif'))
    # list_im = [str(i) for i in list_im]
    # df_sta = sta_imlist(list_im)
    # df_sta.to_csv(root/'sta.csv', index=False)
