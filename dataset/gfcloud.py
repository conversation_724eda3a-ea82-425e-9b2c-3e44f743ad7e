import os
from os import path
from typing import Any
from torch.utils.data import Dataset, DataLoader
from pathlib import Path
import pandas as pd
import rasterio as rio
import numpy as np
from skimage import exposure
import torch
from multiprocessing import Pool, cpu_count, Manager
from functools import partial
import random
import matplotlib.pyplot as plt
import torchvision.transforms as T
from matplotlib.colors import ListedColormap
from tqdm import tqdm
import concurrent.futures

class_dict = {
        'clear':      0,
        'cloud':     1,
        'shadow':     2,
        'nodata': 3
    }

invert_class_dict = {v:k for k,v in class_dict.items()}
CLASSES = [invert_class_dict[i] for i in range(len(invert_class_dict))]


def minmax_dict_from_fname(impath, p=0.5):
    with rio.open(impath) as src:
        h, w  = src.shape
        h,w = h//4, w//4
        im = src.read(out_shape=(h,w))
        v_min, v_max = [], []
        for i in range(im.shape[0]):
            im_band = im[i]
            v_max.append(np.percentile(im_band[im_band!=0], 100-p))
            v_min.append(np.percentile(im_band[im_band!=0], p))
        result = {}
        for i,(b_min,b_max) in enumerate(zip(v_min,v_max)):
            result[f'min_{i}'] = b_min
            result[f'max_{i}'] = b_max
        return result

def minmax(im:np.ndarray,p=0.5, vmin=None, vmax=None):
    if (vmin is None) or (vmax is None):
        v_max = np.percentile(im[im!=0], 100-p, [1, 2])
        v_min = np.percentile(im[im!=0], p, [1, 2])
    else:
        v_max = vmax
        v_min = vmin
    v_max = v_max[:, np.newaxis,np.newaxis]
    v_min = v_min[:, np.newaxis,np.newaxis]
    # print(v_min,v_max)
    return np.clip((im-v_min)/(v_max-v_min), 0, 1)

def sta_imlist(imlist,p=0.01) -> pd.DataFrame:
    print('stating:')
    with Pool(cpu_count()) as p:
        result = list(tqdm(p.imap(minmax_dict_from_fname,imlist)))
    df = pd.DataFrame(data=result)
    df['impath'] = imlist
    return df

def random_clip_gamma(data, win_size=540, match_size=False, g=None):
    '''
    @g: 指定后可以固定，不再随机
    '''
    im = data['im']
    label = data['label']

    # img和label大小不同，需要确保符合倍数关系
    assert win_size % 6 == 0

    max_yoff = im.shape[1] - win_size
    max_xoff = im.shape[2] - win_size

    y_off = random.randint(0, max_yoff)
    x_off = random.randint(0, max_xoff)

    y_slice = slice(y_off, y_off+win_size)
    x_slice = slice(x_off, x_off+win_size)


    if g is None:
        g = 0.95 - random.random()*0.45

    im = torch.from_numpy(exposure.adjust_gamma(im[:, y_slice, x_slice], gamma=g))
    label = torch.from_numpy(label[:, y_slice, x_slice])
    if match_size:
        f = T.Resize(im.shape[-2:], T.InterpolationMode.NEAREST)
        label = f(label)
    return  im, label.squeeze(), data['img_id']

def _fnpmssample(p_im, p_label, vmin=None, vmax=None):
    '''

    '''
    a_im = rio.open(p_im).read()
    mask = a_im.max(axis=0)==0
    a_label = rio.open(p_label).read(1)
    a_label_map = np.zeros_like(a_label,dtype=np.uint8)
    a_label_map[a_label>60] = 2
    a_label_map[a_label>200] = 1
    a_label_map[mask] = 3
    
    data = {
        'img_id': path.splitext(path.basename(p_im))[0],
        'im': minmax(a_im, vmin=vmin, vmax=vmax)[[2,1,0,3],:,:],
        'label': a_label_map[np.newaxis, :,:]
    }

    return data

def row2sample(row):
    i, item = row
    p_im = item.impath
    p_label = p_im.replace('/image/','/label/')
    p_label = p_label.replace('.tif','_mask.tif')
    i = 0
    vmin, vmax = [], []
    while True:
        try:
            vmin.append(item[f'min_{i}'])
            vmax.append(item[f'max_{i}'])
            i += 1
        except:
            break
    vmin = np.array(vmin)
    vmax = np.array(vmax)
    # l.append(_fns2sample(p_im, p_label, p_ref,vmin=vmin,vmax=vmax))
    return _fnpmssample(p_im, p_label, vmin=vmin,vmax=vmax)

class cloudsegpms(Dataset):
    def __init__(
            self,
            root, 
            split='train', 
            sample_n_df:int|None=None, 
            sample_size=480, 
            match_size=False, 
            g=0.7, 
            use_sta_cache=False,
            is_rgb=False) -> None:
        '''
        @g: val时g默认0.7, train时改为None，用时随机生成
        '''
        
        assert split in set(['train', 'val'])
        self.is_rgb = is_rgb
        if type(root) == str:
            root = Path(root)
        p_im = root/'image'
        self.sample_size = sample_size
        list_im = list(p_im.rglob('*.tif'))
        list_im = [str(i) for i in list_im]
        
        
        df_im = pd.DataFrame(data={'impath':list_im})
        if sample_n_df is not None:
            df_im = df_im.sample(n=sample_n_df, ignore_index=True)
        else: 
            sample_n_df = len(list_im)
        self.sample_n_df = sample_n_df

        if (root/'sta.csv').exists() and use_sta_cache:
            print('using cache sta.')
            df_sta = pd.read_csv(root/'sta.csv')
        else:
            df_sta = sta_imlist(df_im.impath)
            if not (root/'sta.csv').exists():
                df_sta.to_csv(root/'sta.csv', index=False)

        df_im = df_sta.loc[df_sta.impath.isin(df_im.impath)]
        assert len(df_im) == sample_n_df
        #随机一次
        df_im = df_im.sample(frac=1.0,ignore_index=True)
        
        self.df_im = df_im

        if split == 'val':
            g = g
        else:
            g = None

        self.preprocess = partial(random_clip_gamma, win_size=sample_size, match_size=match_size, g=g)
        self.data = []
        super().__init__()
    
    def load(self):
        print('loading: ')
        # 使用multiprocess一次性加载所有数据
        # with Pool(min(cpu_count(), self.sample_n_df)) as p:
        with concurrent.futures.ThreadPoolExecutor(os.cpu_count()) as executor:
            # self.data = list(tqdm(p.imap(row2sample, list(df_im.iterrows())), total=len(df_im)))
            # self.data = list(tqdm(p.map(row2sample, list(df_im.iterrows())), total=len(df_im)))
            for result in tqdm(executor.map(row2sample, list(self.df_im.iterrows())), total=len(self.df_im)):
                self.data.append(result)
                # pass
            # p.close()
            # p.join()


    def __getitem__(self, index) -> Any:
        sample = self.data[index]
        im, label, img_id = self.preprocess(sample)
        end = 3 if self.is_rgb else 4
        s = slice(end)
        sample = {
            'img': im[s].float(),
            'img_id': img_id,
            'gt_semantic_seg': label.long()
        }
        return sample
    
    def __len__(self):
        return len(self.df_im)

if __name__ == '__main__':
    # a1 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/43SFB/S2B_MSIL1C_20191227T055239_N0208_R048_T43SFB_20191227T074531.tif'
    # a2 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/51STR/S2B_MSIL1C_20191230T024119_N0208_R089_T51STR_20191230T042547.tif'
    # print('test')
    # 336 480 960 
    ds = cloudsegpms('/mnt/Netapp/yinry/CloudDetection/Dataset_gfpms',
                    split='val',
                    sample_n_df=None, sample_size=960, match_size=True, use_sta_cache=True)
    # random.seed(9)
    # print(ds[0])
    # print(ds[0])
    # print(ds[0])
    # print(ds[0])
    ds.load()

    fig, axes = plt.subplots(2,4,constrained_layout=True,subplot_kw={'xticks': [], 'yticks': []})
    cmap_classied = ListedColormap(["white", "#DC0000CC","#3C5488CC", "#FFF7AC"])
    axes = axes.flatten()
    for i in range(4):
        d = ds[i]
        print(d['img_id'])
        print(d['img'].shape,d['gt_semantic_seg'].shape)
        axes[i].imshow(d['img'][[0,1,2],:,:].permute(1,2,0))
        axes[i+4].imshow(d['gt_semantic_seg'],cmap=cmap_classied,vmin=-0.5, vmax=3.5,interpolation='nearest')
        fig.savefig('test15.png')

    # root = Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg')
    # list_im = list(root.rglob('*/img/*/*.tif'))
    # list_im = [str(i) for i in list_im]
    # df_sta = sta_imlist(list_im)
    # df_sta.to_csv(root/'sta.csv', index=False)
