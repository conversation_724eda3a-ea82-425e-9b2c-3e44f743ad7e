# from .iou_loss import IOU
from .iou_multi_loss import IO<PERSON><PERSON>oss
import torch.nn as nn


# iou_loss = IOU(size_average=True)

# def _boundaryNetloss(pred, target):
#     bce_out = bce_loss(pred, target)
#     iou_out = iou_loss(pred, target)

#     loss = bce_out + iou_out
    
#     return loss

class BoundaryNetsLoss(nn.Module):

    def __init__(self, n_class, *args, **kwargs) -> None:
        super().__init__(*args, **kwargs)
        self.iouLoss = IOULoss(n_class)
        self.bce_loss = nn.CrossEntropyLoss()
        
    def _boundaryNetloss(self, pred, target):
        bce_out = self.bce_loss(pred, target)
        iou_out = self.iouLoss(pred, target, softmax=True)

        loss = bce_out + iou_out
        
        return loss
        
    def forward(self, logits, labels):
        if type(logits) == tuple:
            # d0, d1, d2, d3, d4, d5, d6, d7 = logits
            d1, d2, d3, d4, d5, d6, d7 = logits
            # loss0 = _boundaryNetloss(d0, labels)
            loss1 = self._boundaryNetloss(d1, labels)
            loss2 = self._boundaryNetloss(d2, labels)
            loss3 = self._boundaryNetloss(d3, labels)
            loss4 = self._boundaryNetloss(d4, labels)
            loss5 = self._boundaryNetloss(d5, labels)
            loss6 = self._boundaryNetloss(d6, labels)
            loss7 = self._boundaryNetloss(d7, labels)
            # loss = loss0 + loss1 + loss2 + loss3 + loss4 + loss5 + loss6 + loss7
            loss = loss1 + loss2 + loss3 + loss4 + loss5 + loss6 + loss7
            return loss
        
        else:
            loss1 = self._boundaryNetloss(logits, labels)
            return loss1