import torch
import torch.nn as nn

class IOULoss(nn.Module):
    def __init__(self, n_classes):
        super(IOULoss, self).__init__()
        self.n_classes = n_classes

    def _one_hot_encoder(self, input_tensor):
        tensor_list = []
        for i in range(self.n_classes):
            temp_prob = input_tensor == i  # * torch.ones_like(input_tensor)
            tensor_list.append(temp_prob.unsqueeze(1))
        output_tensor = torch.cat(tensor_list, dim=1)
        return output_tensor.float()

    def _IOU_loss(self, score, target):
        target = target.float()
        smooth = 1e-5
        intersect = torch.sum(score * target)
        y_sum = torch.sum(target)
        z_sum = torch.sum(score)
        loss = (intersect + smooth) / (z_sum + y_sum -intersect + smooth)
        loss = 1 - loss
        return loss

    def forward(self, inputs, target, weight=None, softmax=False):
        if softmax:
            inputs = torch.softmax(inputs, dim=1)
        target = self._one_hot_encoder(target)
        if weight is None:
            weight = [1] * self.n_classes
        assert inputs.size() == target.size(), 'predict {} & target {} shape do not match'.format(inputs.size(), target.size())
        # class_wise_dice = []
        loss = 0.0
        for i in range(0, self.n_classes):
            IOU = self._IOU_loss(inputs[:, i], target[:, i])
            # class_wise_dice.append(1.0 - IOU.item())
            loss += IOU * weight[i]
        return loss / self.n_classes

# def _iou(pred, target, size_average = True):

#     b = pred.shape[0]
#     IoU = 0.0
#     for i in range(0,b):
#         #compute the IoU of the foreground
#         Iand1 = torch.sum(target[i,:,:,:]*pred[i,:,:,:])
#         Ior1 = torch.sum(target[i,:,:,:]) + torch.sum(pred[i,:,:,:])-Iand1
#         IoU1 = Iand1/Ior1

#         #IoU loss is (1-IoU1)
#         IoU = IoU + (1-IoU1)

#     return IoU/b

# class IOU(torch.nn.Module):
#     def __init__(self, size_average = True):
#         super(IOU, self).__init__()
#         self.size_average = size_average

#     def forward(self, pred, target):

#         return _iou(pred, target, self.size_average)