import torch
import torch.nn as nn
import torch.nn.functional as F

import torch.nn.functional as F

from .resnet_model import *
from .DifferentableBoundaryNet import DifferentableBoundaryNet
from .ScalableBoundaryNet import ScalableBoundaryNet

class BoundaryNets(nn.Module):
    def __init__(self,n_channels,n_classes=1):
        super().__init__()
        self.scalable_net = ScalableBoundaryNet(n_channels,n_classes)
        # self.diff_net = DifferentableBoundaryNet(n_classes)

    def forward(self,x):
        d1, d2, d3, d4, d_ff1, d_ff2, d_mul = self.scalable_net(x)
        # d_diff = self.diff_net(d1) 
        # return F.sigmoid(d1), <PERSON><PERSON>sigmoid(d2), <PERSON><PERSON>sigmoid(d3), <PERSON><PERSON>sigmoid(d4), <PERSON><PERSON>sigmoid(d_ff1), <PERSON><PERSON>sigmoid(d_ff2), <PERSON><PERSON>sigmoid(d_mul)
        if self.training:
            return d1, d2, d3, d4, d_ff1, d_ff2, d_mul
        else:
            return d1
