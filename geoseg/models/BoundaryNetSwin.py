import torch
import torch.nn as nn
from torchvision import models
import torch.nn.functional as F

from .resnet_model import *
from .sync_batchnorm import SynchronizedBatchNorm2d
from .MultiScaleFusion_ASPP import ASPP, eca_layer, PAM_Module
from .swinTransformer import SwinTransformerV2

class DiffNet(nn.Module):
    def __init__(self,in_ch,inc_ch):
        super(DiffNet, self).__init__()

        self.conv0 = nn.Conv2d(in_ch,inc_ch,3,padding=1)

        self.conv1 = nn.Conv2d(inc_ch,64,3,padding=1)
        self.bn1 = SynchronizedBatchNorm2d(64)
        self.relu1 = nn.ReLU(inplace=True)

        self.pool1 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.conv2 = nn.Conv2d(64,64,3,padding=1)
        self.bn2 = SynchronizedBatchNorm2d(64)
        self.relu2 = nn.ReLU(inplace=True)

        self.pool2 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.conv3 = nn.Conv2d(64,64,3,padding=1)
        self.bn3 = SynchronizedBatchNorm2d(64)
        self.relu3 = nn.ReLU(inplace=True)

        self.pool3 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.conv4 = nn.Conv2d(64,64,3,padding=1)
        self.bn4 = SynchronizedBatchNorm2d(64)
        self.relu4 = nn.ReLU(inplace=True)

        self.pool4 = nn.MaxPool2d(2,2,ceil_mode=True)


        self.conv5 = nn.Conv2d(64,64,3,padding=1)
        self.bn5 = SynchronizedBatchNorm2d(64)
        self.relu5 = nn.ReLU(inplace=True)


        self.conv_d4 = nn.Conv2d(128,64,3,padding=1)
        self.bn_d4 = SynchronizedBatchNorm2d(64)
        self.relu_d4 = nn.ReLU(inplace=True)

        self.conv_d3 = nn.Conv2d(128,64,3,padding=1)
        self.bn_d3 = SynchronizedBatchNorm2d(64)
        self.relu_d3 = nn.ReLU(inplace=True)

        self.conv_d2 = nn.Conv2d(128,64,3,padding=1)
        self.bn_d2 = SynchronizedBatchNorm2d(64)
        self.relu_d2 = nn.ReLU(inplace=True)

        self.conv_d1 = nn.Conv2d(128,64,3,padding=1)
        self.bn_d1 = SynchronizedBatchNorm2d(64)
        self.relu_d1 = nn.ReLU(inplace=True)

        self.conv_d0 = nn.Conv2d(64,1,3,padding=1)

        self.upscore2 = nn.Upsample(scale_factor=2, mode='bilinear')


    def forward(self,x):

        hx = x
        hx = self.conv0(hx)

        hx1 = self.relu1(self.bn1(self.conv1(hx)))
        hx = self.pool1(hx1)

        hx2 = self.relu2(self.bn2(self.conv2(hx)))
        hx = self.pool2(hx2)

        hx3 = self.relu3(self.bn3(self.conv3(hx)))
        hx = self.pool3(hx3)

        hx4 = self.relu4(self.bn4(self.conv4(hx)))
        hx = self.pool4(hx4)

        hx5 = self.relu5(self.bn5(self.conv5(hx)))

        hx = self.upscore2(hx5)

        d4 = self.relu_d4(self.bn_d4(self.conv_d4(torch.cat((hx,hx4),1))))
        hx = self.upscore2(d4)

        d3 = self.relu_d3(self.bn_d3(self.conv_d3(torch.cat((hx,hx3),1))))
        hx = self.upscore2(d3)

        d2 = self.relu_d2(self.bn_d2(self.conv_d2(torch.cat((hx,hx2),1))))
        hx = self.upscore2(d2)

        d1 = self.relu_d1(self.bn_d1(self.conv_d1(torch.cat((hx,hx1),1))))

        residual = self.conv_d0(d1)

        return x + residual
    
class BCA(nn.Module):
    def __init__(self, xin_channels, yin_channels, mid_channels, BatchNorm=nn.BatchNorm2d, scale=False):
        super(BCA, self).__init__()
        self.mid_channels = mid_channels
        self.f_self = nn.Sequential(
            nn.Conv2d(in_channels=xin_channels, out_channels=mid_channels,
                      kernel_size=1, stride=1, padding=0, bias=False),
            BatchNorm(mid_channels),
            # nn.Conv2d(in_channels=mid_channels, out_channels=mid_channels,
            #           kernel_size=1, stride=1, padding=0, bias=False),
            # BatchNorm(mid_channels),
        )
        self.f_x = nn.Sequential(
            nn.Conv2d(in_channels=xin_channels, out_channels=mid_channels,
                      kernel_size=1, stride=1, padding=0, bias=False),
            BatchNorm(mid_channels),
            # nn.Conv2d(in_channels=mid_channels, out_channels=mid_channels,
            #           kernel_size=1, stride=1, padding=0, bias=False),
            # BatchNorm(mid_channels),
        )
        self.f_y = nn.Sequential(
            nn.Conv2d(in_channels=yin_channels, out_channels=mid_channels,
                      kernel_size=1, stride=1, padding=0, bias=False),
            BatchNorm(mid_channels),
            # nn.Conv2d(in_channels=mid_channels, out_channels=mid_channels,
            #           kernel_size=1, stride=1, padding=0, bias=False),
            # BatchNorm(mid_channels),
        )
        self.f_up = nn.Sequential(
            nn.Conv2d(in_channels=mid_channels, out_channels=xin_channels,
                      kernel_size=1, stride=1, padding=0, bias=False),
            BatchNorm(xin_channels),
        )
        self.scale = scale
        self.ecalayer = eca_layer(512,3)

        nn.init.constant_(self.f_up[1].weight, 0)
        nn.init.constant_(self.f_up[1].bias, 0)

    def forward(self, x, y): 
        batch_size = x.size(0)
        fself = self.f_self(x).view(batch_size, self.mid_channels, -1)
        fself = fself.permute(0, 2, 1)
        fx = self.f_x(x).view(batch_size, self.mid_channels, -1)
        fx = fx.permute(0, 2, 1)
        fy = self.f_y(y).view(batch_size, self.mid_channels, -1)
        sim_map = torch.matmul(fx, fy)
        if self.scale:
            sim_map = (self.mid_channels ** -.5) * sim_map
        sim_map_div_C = F.softmax(sim_map, dim=-1)
        fout = torch.matmul(sim_map_div_C, fself)
        fout = fout.permute(0, 2, 1).contiguous()
        fout = fout.view(batch_size, self.mid_channels, *x.size()[2:])
        out = self.f_up(fout)
        channelattout = self.ecalayer(out)
        return out+channelattout
    

class FCUDown(nn.Module):
    """ CNN feature maps -> Transformer patch embeddings
    """

    def __init__(self, inplanes, outplanes, act_layer=nn.GELU,
                 norm_layer=nn.LayerNorm):
        super(FCUDown, self).__init__()
        # self.dw_stride = dw_stride

        self.conv_project = nn.Conv2d(inplanes, outplanes, kernel_size=1, stride=1, padding=0)
        # self.sample_pooling = nn.AvgPool2d(kernel_size=dw_stride, stride=dw_stride)

        self.ln = norm_layer(outplanes)
        self.act = act_layer()

    def forward(self, x):
        x = self.conv_project(x)  # [N, C, H, W]

        x = x.flatten(2).transpose(1, 2)
        x = self.ln(x)
        x = self.act(x)

        # x = torch.cat([x_t[:, 0][:, None, :], x], dim=1)
        return x
    
import numpy as np

class FCUUp(nn.Module):
    """ swin Transformer patch embeddings -> CNN feature maps
    """

    def __init__(self, inplanes, outplanes, up_stride, act_layer=nn.ReLU,
                 norm_layer=nn.BatchNorm2d):
        super(FCUUp, self).__init__()

        self.up_stride = up_stride
        self.conv_project = nn.Conv2d(inplanes, outplanes, kernel_size=1, stride=1, padding=0)
        self.bn = norm_layer(outplanes)
        self.act = act_layer()

    def forward(self, x):
        B,  L, C = x.shape
        h, w = int(np.sqrt(L)), int(np.sqrt(L))
        x_r = x.permute(0, 2, 1)
        x_r = x_r.contiguous().view(B, C, h, w)
        # [N, 197, 384] -> [N, 196, 384] -> [N, 384, 196] -> [N, 384, 14, 14]
        # x_r = x[:, 1:].transpose(1, 2).reshape(B, C, H, W)
        x_r = self.act(self.bn(self.conv_project(x_r)))

        return F.interpolate(x_r, size=(h * self.up_stride, w * self.up_stride))
        # return x_r
    
class convBlock(nn.Module):
    def __init__(self, in_channels, out_channels):
        super(convBlock, self).__init__()
        self.cnn = nn.Sequential(
            nn.Conv2d(in_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(),

            nn.Conv2d(out_channels, out_channels, kernel_size=3, padding=1),
            nn.BatchNorm2d(out_channels),
            nn.ReLU(),
        )

    def forward(self, x):
        x = self.cnn(x)
        return x


class BoundaryNets_swint(nn.Module):
    def __init__(self,n_channels,n_classes,imgsize):
        super(BoundaryNets_swint,self).__init__()

        ## -------------EncoderCNN--------------
        self.inconv0 = nn.Sequential(
            convBlock(4,64),
            convBlock(64,64)

        )  # 384

        self.inconv1= nn.Sequential(
            convBlock(64,64),
            convBlock(64,64),
            nn.MaxPool2d(2,2,ceil_mode=True)
        )  # 384->192

        
        self.encoder1 = nn.Sequential(
            BasicBlock(128,128),
            BasicBlock(128,128),
            nn.MaxPool2d(2,2,ceil_mode=True),
        ) #192->96

        self.encoder2 = nn.Sequential(
            BasicBlock(256,256),
            BasicBlock(256,256),
            
            nn.MaxPool2d(2,2,ceil_mode=True),
        ) #96->48

        self.encoder3 = nn.Sequential(
            BasicBlock(512,512),
            BasicBlock(512,512),
            
            nn.MaxPool2d(2,2,ceil_mode=True),
        ) #48->24

        self.encoder4 = nn.Sequential(
            BasicBlock(1024,1024),
            BasicBlockDe(1024,512),
            BasicBlock(512,512),
            nn.MaxPool2d(2,2,ceil_mode=True),
        ) #24->12

## -------------EncoderSwinT--------------
        self.swinunet = SwinTransformerV2(img_size=imgsize,
                                patch_size=4,
                                in_chans=n_channels,
                                num_classes=n_classes,
                                embed_dim=128,
                                depths=[2, 2, 2, 2],
                                num_heads=[4,8,16,32],#[3, 6, 12, 24],
                                window_size=24, #7
                                mlp_ratio=4,
                                qkv_bias=True,
                                qk_scale=None,
                                drop_rate=0.5,
                                drop_path_rate=0.1,
                                ape=False,
                                patch_norm=True,
                                use_checkpoint=False) # 128,256,512,512
        
        self.layer1 = self.swinunet.layers[0]
        self.layer2 = self.swinunet.layers[1]
        self.layer3 = self.swinunet.layers[2]
        self.layer4 = self.swinunet.layers[3]
        self.patch_embeding = self.swinunet.patch_embed
        self.pos_drop = self.swinunet.pos_drop

        #-----------------------exchange------------------
        self.tocnn_block1 = FCUUp(128, 64,2)
        self.tocnn_block2 = FCUUp(256, 128,2)
        self.tocnn_block3 = FCUUp(512, 256,2)
        self.tocnn_block4 = FCUUp(1024, 512,2)
        self.tocnn_block5 = FCUUp(1024, 512,1)

        self.toswint_block1 = FCUDown(128, 128)
        self.toswint_block2 = FCUDown(256, 256)
        self.toswint_block3 = FCUDown(512, 512)
        
        
        # ------------multi scale fusion---------------
        
        self.mulfu = ASPP(512,512,12)
        # self.ecalayer = eca_layer(512,3)
        self.att = BCA(512,512,256)
        # self.pcm = PAM_Module(512)
        
        ## -------------Decoder--------------
        self.decoder4 = nn.Sequential(
            nn.Conv2d(1536,1024,3,padding=1), 
            SynchronizedBatchNorm2d(1024),
            nn.ReLU(inplace=True),

            nn.Conv2d(1024,512,3,dilation=2, padding=2),###
            SynchronizedBatchNorm2d(512),
            nn.ReLU(inplace=True),

            nn.Conv2d(512,512,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(512),
            nn.ReLU(inplace=True),
        ) #24

        self.decoder3 = nn.Sequential(
            nn.Conv2d(1536,1024,3,padding=1) ,
            SynchronizedBatchNorm2d(1024),
            nn.ReLU(inplace=True),

            nn.Conv2d(1024,512,3,dilation=2, padding=2),###,
            SynchronizedBatchNorm2d(512),
            nn.ReLU(inplace=True),

            nn.Conv2d(512,256,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(256),
            nn.ReLU(inplace=True),
        ) #48
        
        self.decoder2 = nn.Sequential(
            nn.Conv2d(768,512,3,padding=1), 
            SynchronizedBatchNorm2d(512),
            nn.ReLU(inplace=True),

            nn.Conv2d(512,256,3,dilation=2, padding=2),###
            SynchronizedBatchNorm2d(256),
            nn.ReLU(inplace=True),

            nn.Conv2d(256,128,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(128),
            nn.ReLU(inplace=True),
        ) #96

        self.decoder1 = nn.Sequential(
            nn.Conv2d(384,256,3,padding=1), 
            SynchronizedBatchNorm2d(256),
            nn.ReLU(inplace=True),

            nn.Conv2d(256,128,3,dilation=2, padding=2),###
            SynchronizedBatchNorm2d(128),
            nn.ReLU(inplace=True),

            nn.Conv2d(128,64,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),
        ) # 192

        self.decoder01 = nn.Sequential(
            nn.Conv2d(192,128,3,padding=1), 
            SynchronizedBatchNorm2d(128),
            nn.ReLU(inplace=True),

            nn.Conv2d(128,64,3,dilation=2, padding=2),###
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),

            nn.Conv2d(64,64,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),
        ) # 384

        self.decoder0 = nn.Sequential(
            nn.Conv2d(128,64,3,padding=1), 
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),

            nn.Conv2d(64,64,3,dilation=2, padding=2),###
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),

            nn.Conv2d(64,64,3,dilation=2, padding=2),
            SynchronizedBatchNorm2d(64),
            nn.ReLU(inplace=True),
        )
        
        ## -------------Bilinear Upsampling--------------
        self.upscore6 = nn.Upsample(scale_factor=32,mode='bilinear')###
        self.upscore5 = nn.Upsample(scale_factor=16,mode='bilinear')
        self.upscore4 = nn.Upsample(scale_factor=8,mode='bilinear')
        self.upscore3 = nn.Upsample(scale_factor=4,mode='bilinear')
        self.upscore2 = nn.Upsample(scale_factor=2, mode='bilinear')

        # self.transupscore1 = nn.Upsample(scale_factor=8,mode='bilinear')
        # self.transupscore2 = nn.Upsample(scale_factor=8,mode='bilinear')
        # self.transupscore3 = nn.Upsample(scale_factor=8,mode='bilinear')
        # self.transupscore4 = nn.Upsample(scale_factor=4, mode='bilinear')

        ## -------------Side Output--------------
        self.outconvb = nn.Conv2d(512,1,3,padding=1)
        self.outconv6 = nn.Conv2d(512,1,3,padding=1)
        self.outconv5 = nn.Conv2d(256,1,3,padding=1)
        self.outconv4 = nn.Conv2d(128,1,3,padding=1)
        self.outconv3 = nn.Conv2d(64,1,3,padding=1)
        self.outconv2 = nn.Conv2d(64,1,3,padding=1)
        self.outconv1 = nn.Conv2d(64,1,3,padding=1)

        ## -------------Refine Module-------------
        self.diffnet = DiffNet(1,64)


    def forward(self,x):

        hx = x

        trans_x = self.patch_embeding(x)
        
        trans_x = self.pos_drop(trans_x)

        h0 = self.inconv0(hx) #384
        h01 = self.inconv1(h0) #192

        ## -------------Encoder-------------
        # x_downsample = self.swinunet(hx)
        

        swint_x1 = self.layer1(trans_x)
        swintocnn_x1 = self.tocnn_block1(swint_x1)
        h1 = self.encoder1(torch.cat([h01,swintocnn_x1],dim=1)) #96

        cnntoswint_x1 = self.toswint_block1(h1)
        swint_x2 = self.layer2(torch.cat([cnntoswint_x1,swint_x1],dim=2))
        swintocnn_x2 = self.tocnn_block2(swint_x2) #256
        h2 = self.encoder2(torch.cat([h1,swintocnn_x2],dim=1))
        
        cnntoswint_x2 = self.toswint_block2(h2)
        swint_x3 = self.layer3(torch.cat([cnntoswint_x2,swint_x2],dim=2))
        swintocnn_x3 = self.tocnn_block3(swint_x3)
        h3 = self.encoder3(torch.cat([swintocnn_x3,h2],dim=1))

        cnntoswint_x3 = self.toswint_block3(h3)
        swint_x4 = self.layer4(torch.cat([cnntoswint_x3,swint_x3],dim=2)) 
        swintocnn_x4 = self.tocnn_block4(swint_x4)
        h4 = self.encoder4(torch.cat([swintocnn_x4,h3],dim=1)) #48  28
        swintocnn_x5 = self.tocnn_block5(swint_x4)
        ## -------------Bridge-------------
        attfea = self.att(h4,swintocnn_x5)

        hbg = self.mulfu(attfea)

        ## -------------Decoder-------------
        
        hd4 = self.decoder4(torch.cat([h4,hbg,swintocnn_x5 ],dim=1))

        hx = self.upscore2(hd4) # 12 -> 24
 
        hd3 = self.decoder3(torch.cat([h3,hx, swintocnn_x4],dim=1))

        hx = self.upscore2(hd3) # 24 -> 48

        
        hd2 = self.decoder2(torch.cat([h2,hx, swintocnn_x3],dim=1))

        hx = self.upscore2(hd2) # 48 -> 96

        
        hd1 = self.decoder1(torch.cat([hx,h1, swintocnn_x2],dim=1))

        hx = self.upscore2(hd1) # 96 -> 192

        hd01 = self.decoder01(torch.cat([h01,hx,swintocnn_x1],dim=1))
        hx = self.upscore2(hd01) # 192 -> 384

        hd0 = self.decoder0(torch.cat([h0,hx],dim=1))

        ## -------------Side Output-------------
        db = self.outconvb(hbg)
        db = self.upscore6(db) 

        d6 = self.outconv6(hd4)
        d6 = self.upscore6(d6) 

        d5 = self.outconv5(hd3)
        d5 = self.upscore5(d5) 

        d4 = self.outconv4(hd2)
        d4 = self.upscore4(d4) 

        d3 = self.outconv3(hd1)
        d3 = self.upscore3(d3) 

        d2 = self.outconv2(hd01)
        d2 = self.upscore2(d2) 

        d1 = self.outconv1(hd0) 

        ## -------------difference boundary net-------------
        dout = self.diffnet(d1) # 384


        return F.sigmoid(dout), F.sigmoid(d1), F.sigmoid(d2), F.sigmoid(d3), F.sigmoid(d4), F.sigmoid(d5), F.sigmoid(d6), F.sigmoid(db)
