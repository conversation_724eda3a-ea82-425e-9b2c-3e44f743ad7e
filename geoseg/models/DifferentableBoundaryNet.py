"""
    Notes: Some implementations of this code are built based on nn-UNet/BASNet. 
"""
import torch
import torch.nn as nn
from .resnet_model import *

class DifferentableBoundaryNet(nn.Module):
    def __init__(self,in_ch):
        super().__init__()

        self.conv0 = nn.Conv2d(in_ch,64,3,padding=1)

        self.cbr1 = CBRModule(64,64)
        self.pool1 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.cbr2 = CBRModule(64,64)
        self.pool2 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.cbr3 = CBRModule(64,64)
        self.pool3 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.cbr4 = CBRModule(64,64)
        self.pool4 = nn.MaxPool2d(2,2,ceil_mode=True)

        self.cbr5 = CBRModule(64,64)

        self.cbr_d4 = CBRModule(128,64)
        self.cbr_d3 = CBRModule(128,64)
        self.cbr_d2 = CBRModule(128,64)
        self.cbr_d1 = CBRModule(128,64)

        self.conv_out = nn.Conv2d(64,1,3,padding=1)
        self.upx2 = nn.Upsample(scale_factor=2, mode='bilinear')

    def forward(self,x):

        out = x
        out = self.conv0(out)

        h1 = self.cbr1(out)
        out = self.pool1(h1)

        h2 = self.cbr2(out)
        out = self.pool2(h2)

        h3 = self.cbr3(out)
        out = self.pool3(h3)

        h4 = self.cbr4(out)
        out = self.pool1(h4)

        h5 = self.cbr5(out)
        out = self.upx2(h5)

        d4 = self.cbr_d4(torch.cat((out,h4),1))
        out = self.upx2(d4)

        d3 = self.cbr_d3(torch.cat((out,h3),1))
        out = self.upx2(d3)

        d2 = self.cbr_d2(torch.cat((out,h2),1))
        out = self.upx2(d2)

        d1 = self.cbr_d1(torch.cat((out,h1),1))

        out = self.conv_out(d1)

        return out