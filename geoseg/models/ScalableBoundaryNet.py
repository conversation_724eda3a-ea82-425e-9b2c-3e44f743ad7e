import torch
import torch.nn as nn
from torchvision import models
import torch.nn.functional as F

import torch.nn.functional as F

from .resnet_model import *
from .MultiScaleFusion import MultiScaleFusion

class ScalableBoundaryNet(nn.Module):
    def __init__(self,n_channels,n_classes=1):
        super().__init__()
        # Encoder
        # self.resnet = models.resnet34(pretrained=True)
        self.resnet = models.resnet18(pretrained=True)
        self.in_cbr = CBRModule(n_channels, 64)
        # use resnet34 as backnone
        self.encoder1 = self.resnet.layer1 #384
        self.encoder2 = self.resnet.layer2 #192
        self.encoder3 = self.resnet.layer3 #96
        self.encoder4 = self.resnet.layer4 #48

        self.pool4 = nn.MaxPool2d(2,2,ceil_mode=True)

        # multi scale fusion module
        # feature expansion block
        self.feature_expansion_1 = nn.Sequential(
            BasicBlock(512,512),
            BasicBlock(512,512),
            BasicBlock(512,512) #24
        )
        self.pool_feb = nn.MaxPool2d(2,2,ceil_mode=True)
        self.feature_expansion_2 = nn.Sequential(
            BasicBlock(512,512,dilation=2),
            BasicBlock(512,512,dilation=2),
            BasicBlock(512,512,dilation=2) #24
        )

        # multi scale fusion block
        self.multi_scale_fusion = MultiScaleFusion(dim_in=512,
                         dim_out=512,
                         rate=1,
                         bn_mom=0.0003)
        self.out_msf = nn.Conv2d(512,n_classes,3,padding=1)
        self.up_msf = nn.Upsample(scale_factor=32,mode='bilinear')

        # feature fusion block
        self.feature_fusion_1 = nn.Sequential(
            CBRModule(1024, 512, 3, 1, 2, 2, True),
            CBRModule(512, 512, 3, 1, 2, 2, True),
            CBRModule(512, 512, 3, 1, 2, 2, True)
        )
        # deep fusion
        self.out_ff1 = nn.Conv2d(512,n_classes,3,padding=1)
        self.up_ff1 = nn.Upsample(scale_factor=32,mode='bilinear')

        self.feature_fusion_2 = nn.Sequential(
            CBRModule(1024, 512),
            CBRModule(512, 512),
            CBRModule(512, 512)
        )
        # deep fusion
        self.out_ff2 = nn.Conv2d(512,n_classes,3,padding=1)
        self.up_ff2 = nn.Upsample(scale_factor=16,mode='bilinear')
        

        # for Decoder
        self.decoder4 = nn.Sequential(
            CBRModule(1024, 512),
            CBRModule(512, 512),
            CBRModule(512, 256)
        )
        # Deep supervision
        self.outconv4 = nn.Conv2d(256,n_classes,3,padding=1)
        self.up4 = nn.Upsample(scale_factor=8,mode='bilinear')

        self.decoder3 = nn.Sequential(
            CBRModule(512,256),
            CBRModule(256,256),
            CBRModule(256,128)
        )
        # Deep supervision
        self.outconv3 = nn.Conv2d(128,n_classes,3,padding=1)
        self.up3 = nn.Upsample(scale_factor=4,mode='bilinear')

        self.decoder2 = nn.Sequential(
            CBRModule(256,128),
            CBRModule(128,128),
            CBRModule(128,64)
        )
        # Deep supervision
        self.outconv2 = nn.Conv2d(64,n_classes,3,padding=1)
        self.up2 = nn.Upsample(scale_factor=2, mode='bilinear')

        self.decoder1 = nn.Sequential(
            CBRModule(128,64),
            CBRModule(64,64),
            CBRModule(64,32)
        )
        # Deep supervision
        self.outconv1 = nn.Conv2d(32,n_classes,3,padding=1)

        # base x2
        self.up_decoder = nn.Upsample(scale_factor=2, mode='bilinear')


    def forward(self,x):
        # encoder
        out = x
        out = self.in_cbr(out)
        h1 = self.encoder1(out) #384
        h2 = self.encoder2(h1) #192
        h3 = self.encoder3(h2) #96
        h4 = self.encoder4(h3) #48
        out = self.pool4(h4) #24

        # multi-scale-fusion
        h_fe1 = self.feature_expansion_1(out)
        out = self.pool_feb(h_fe1) #12
        h_fe2 = self.feature_expansion_2(out)
        # multi-scale-fusion
        # multi-scale-fusion
        h_multi_scale = self.multi_scale_fusion(h_fe2)

        h_ff1 = self.feature_fusion_1(torch.cat((h_multi_scale,h_fe2),1))
        out = self.up_decoder(h_ff1) # 12 -> 24
        h_ff2 = self.feature_fusion_2(torch.cat((out,h_fe1),1))
        out = self.up_decoder(h_ff2) # 24 -> 48

        # decoder
        hd4 = self.decoder4(torch.cat((out,h4),1))
        out = self.up_decoder(hd4) # 48 -> 96

        hd3 = self.decoder3(torch.cat((out,h3),1))
        out = self.up_decoder(hd3) # 96 -> 192

        hd2 = self.decoder2(torch.cat((out,h2),1))
        out = self.up_decoder(hd2) # 192 -> 384

        hd1 = self.decoder1(torch.cat((out,h1),1))

        # Deep supervision
        d_mul = self.up_msf(self.out_msf(h_multi_scale))
        d_ff1 = self.up_ff1(self.out_ff1(h_ff1))
        d_ff2 = self.up_ff2(self.out_ff2(h_ff2))
        d4 = self.up4(self.outconv4(hd4))
        d3 = self.up3(self.outconv3(hd3))
        d2 = self.up2(self.outconv2(hd2))
        d1 = self.outconv1(hd1) 

        return d1, d2, d3, d4, d_ff1, d_ff2, d_mul