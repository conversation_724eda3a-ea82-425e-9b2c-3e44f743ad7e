import sys
from os import path
sys.path.append(path.abspath(path.join(path.dirname(__file__),'..')))
from torch.utils.data import DataLoader
from geoseg.losses import *
from dataset.s2seg import *
# from geoseg.models.BoundaryNets import BoundaryNets
from geoseg.models.BoundaryNetSwin import BoundaryNets_swint
from catalyst.contrib.optimizers import Lookahead
from catalyst import utils
import torch.optim as optim
from pprint import pprint
from torchstat import stat

# training hparam
max_epoch = 45
ignore_index = len(CLASSES)
train_batch_size = 2
val_batch_size = 1
lr = 1e-05
# weight_decay = 0.01
# backbone_lr = 6e-5
# backbone_weight_decay = 0.01
num_classes = len(CLASSES)
classes = CLASSES

# test_time_aug = 'd4'
output_mask_dir, output_mask_rgb_dir = None, None
weights_name = "boundaryNet-s2cloud"
weights_path = "model_weights/s2cloud/{}".format(weights_name)
test_weights_name = "boundaryNet-s2cloud"
log_name = 's2cloud/{}'.format(weights_name)
monitor = 'val_F1'
monitor_mode = 'max'
save_top_k = 1
save_last = True
check_val_every_n_epoch = 1
pretrained_ckpt_path = None # the path for the pretrained model weight
gpus = 'auto'  # default or gpu ids:[0] or gpu nums: 2, more setting can refer to pytorch_lightning
resume_ckpt_path = None  # whether continue training with the checkpoint, default None

sample_size = 384

#  define the network
net = BoundaryNets_swint(4,num_classes,sample_size)

# define the loss
loss = BoundaryNetsLoss(num_classes)
use_aux_loss = True
# define the dataloader

train_dataset = cloudsegs2(root='/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg', split='train',
                               sample_n_df=100, sample_size=sample_size, match_size=True, is_rgb=True, use_sta_cache=True)

val_dataset = cloudsegs2(root='/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg', split='val',
                               sample_n_df=20, sample_size=sample_size, match_size=True, is_rgb=True, use_sta_cache=True)

test_dataset = cloudsegs2(root='/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg', split='val',
                               sample_n_df=50, sample_size=sample_size, match_size=True, is_rgb=True, use_sta_cache=True)


train_loader = DataLoader(dataset=train_dataset,
                          batch_size=train_batch_size,
                          num_workers=4,
                          pin_memory=True,
                          shuffle=True,
                          drop_last=True)

val_loader = DataLoader(dataset=val_dataset,
                        batch_size=val_batch_size,
                        num_workers=4,
                        shuffle=False,
                        pin_memory=True,
                        drop_last=False)

# define the optimizer
# layerwise_params = {"backbone.*": dict(lr=backbone_lr, weight_decay=backbone_weight_decay)}
# net_params = utils.torch.process_model_params(net, layerwise_params=layerwise_params)
net_params = net.parameters()
# net_params = net.named_parameters()
learn_rate = lr
optimizer = optim.Adam(net_params, lr=learn_rate, betas=(0.9, 0.999), eps=1e-08, weight_decay=0)
lr_scheduler = lr_scheduler = torch.optim.lr_scheduler.CosineAnnealingWarmRestarts(optimizer, T_0=15, T_mult=2)

if __name__ == '__main__':
    stat(net,(4,384,384))