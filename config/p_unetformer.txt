                          module name  input shape output shape      params memory(MB)              MAdd             Flops  MemRead(B)  MemWrite(B) duration[%]     MemR+W(B)
0                      backbone.conv1    3 960 960   64 480 480      9408.0      56.25   4,320,460,800.0   2,167,603,200.0  11096832.0   58982400.0       8.72%  7.007923e+07
1                        backbone.bn1   64 480 480   64 480 480       128.0      56.25      58,982,400.0      29,491,200.0  58982912.0   58982400.0       2.37%  1.179653e+08
2                       backbone.act1   64 480 480   64 480 480         0.0      56.25      14,745,600.0      14,745,600.0  58982400.0   58982400.0       0.15%  1.179648e+08
3                    backbone.maxpool   64 480 480   64 240 240         0.0      14.06      29,491,200.0      14,745,600.0  58982400.0   14745600.0       3.60%  7.372800e+07
4             backbone.layer1.0.conv1   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       4.35%  2.963866e+07
5               backbone.layer1.0.bn1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.61%  2.949171e+07
6        backbone.layer1.0.drop_block   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
7              backbone.layer1.0.act1   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.05%  2.949120e+07
8                backbone.layer1.0.aa   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
9             backbone.layer1.0.conv2   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       2.40%  2.963866e+07
10              backbone.layer1.0.bn2   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.60%  2.949171e+07
11             backbone.layer1.0.act2   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.03%  2.949120e+07
12            backbone.layer1.1.conv1   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       2.38%  2.963866e+07
13              backbone.layer1.1.bn1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.17%  2.949171e+07
14       backbone.layer1.1.drop_block   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
15             backbone.layer1.1.act1   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.04%  2.949120e+07
16               backbone.layer1.1.aa   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
17            backbone.layer1.1.conv2   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       2.40%  2.963866e+07
18              backbone.layer1.1.bn2   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.60%  2.949171e+07
19             backbone.layer1.1.act2   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.03%  2.949120e+07
20            backbone.layer2.0.conv1   64 240 240  128 120 120     73728.0       7.03   2,121,523,200.0   1,061,683,200.0  15040512.0    7372800.0       1.65%  2.241331e+07
21              backbone.layer2.0.bn1  128 120 120  128 120 120       256.0       7.03       7,372,800.0       3,686,400.0   7373824.0    7372800.0       0.09%  1.474662e+07
22       backbone.layer2.0.drop_block  128 120 120  128 120 120         0.0       7.03               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
23             backbone.layer2.0.act1  128 120 120  128 120 120         0.0       7.03       1,843,200.0       1,843,200.0   7372800.0    7372800.0       0.06%  1.474560e+07
24               backbone.layer2.0.aa  128 120 120  128 120 120         0.0       7.03               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
25            backbone.layer2.0.conv2  128 120 120  128 120 120    147456.0       7.03   4,244,889,600.0   2,123,366,400.0   7962624.0    7372800.0       1.64%  1.533542e+07
26              backbone.layer2.0.bn2  128 120 120  128 120 120       256.0       7.03       7,372,800.0       3,686,400.0   7373824.0    7372800.0       0.08%  1.474662e+07
27             backbone.layer2.0.act2  128 120 120  128 120 120         0.0       7.03       1,843,200.0       1,843,200.0   7372800.0    7372800.0       0.02%  1.474560e+07
28     backbone.layer2.0.downsample.0   64 240 240  128 120 120      8192.0       7.03     234,086,400.0     117,964,800.0  14778368.0    7372800.0       1.32%  2.215117e+07
29     backbone.layer2.0.downsample.1  128 120 120  128 120 120       256.0       7.03       7,372,800.0       3,686,400.0   7373824.0    7372800.0       0.09%  1.474662e+07
30            backbone.layer2.1.conv1  128 120 120  128 120 120    147456.0       7.03   4,244,889,600.0   2,123,366,400.0   7962624.0    7372800.0       1.56%  1.533542e+07
31              backbone.layer2.1.bn1  128 120 120  128 120 120       256.0       7.03       7,372,800.0       3,686,400.0   7373824.0    7372800.0       0.33%  1.474662e+07
32       backbone.layer2.1.drop_block  128 120 120  128 120 120         0.0       7.03               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
33             backbone.layer2.1.act1  128 120 120  128 120 120         0.0       7.03       1,843,200.0       1,843,200.0   7372800.0    7372800.0       0.05%  1.474560e+07
34               backbone.layer2.1.aa  128 120 120  128 120 120         0.0       7.03               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
35            backbone.layer2.1.conv2  128 120 120  128 120 120    147456.0       7.03   4,244,889,600.0   2,123,366,400.0   7962624.0    7372800.0       1.73%  1.533542e+07
36              backbone.layer2.1.bn2  128 120 120  128 120 120       256.0       7.03       7,372,800.0       3,686,400.0   7373824.0    7372800.0       0.32%  1.474662e+07
37             backbone.layer2.1.act2  128 120 120  128 120 120         0.0       7.03       1,843,200.0       1,843,200.0   7372800.0    7372800.0       0.02%  1.474560e+07
38            backbone.layer3.0.conv1  128 120 120  256  60  60    294912.0       3.52   2,122,444,800.0   1,061,683,200.0   8552448.0    3686400.0       1.87%  1.223885e+07
39              backbone.layer3.0.bn1  256  60  60  256  60  60       512.0       3.52       3,686,400.0       1,843,200.0   3688448.0    3686400.0       0.06%  7.374848e+06
40       backbone.layer3.0.drop_block  256  60  60  256  60  60         0.0       3.52               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
41             backbone.layer3.0.act1  256  60  60  256  60  60         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.04%  7.372800e+06
42               backbone.layer3.0.aa  256  60  60  256  60  60         0.0       3.52               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
43            backbone.layer3.0.conv2  256  60  60  256  60  60    589824.0       3.52   4,245,811,200.0   2,123,366,400.0   6045696.0    3686400.0       2.80%  9.732096e+06
44              backbone.layer3.0.bn2  256  60  60  256  60  60       512.0       3.52       3,686,400.0       1,843,200.0   3688448.0    3686400.0       0.06%  7.374848e+06
45             backbone.layer3.0.act2  256  60  60  256  60  60         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.02%  7.372800e+06
46     backbone.layer3.0.downsample.0  128 120 120  256  60  60     32768.0       3.52     235,008,000.0     117,964,800.0   7503872.0    3686400.0       1.04%  1.119027e+07
47     backbone.layer3.0.downsample.1  256  60  60  256  60  60       512.0       3.52       3,686,400.0       1,843,200.0   3688448.0    3686400.0       0.07%  7.374848e+06
48            backbone.layer3.1.conv1  256  60  60  256  60  60    589824.0       3.52   4,245,811,200.0   2,123,366,400.0   6045696.0    3686400.0       1.53%  9.732096e+06
49              backbone.layer3.1.bn1  256  60  60  256  60  60       512.0       3.52       3,686,400.0       1,843,200.0   3688448.0    3686400.0       0.06%  7.374848e+06
50       backbone.layer3.1.drop_block  256  60  60  256  60  60         0.0       3.52               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
51             backbone.layer3.1.act1  256  60  60  256  60  60         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.04%  7.372800e+06
52               backbone.layer3.1.aa  256  60  60  256  60  60         0.0       3.52               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
53            backbone.layer3.1.conv2  256  60  60  256  60  60    589824.0       3.52   4,245,811,200.0   2,123,366,400.0   6045696.0    3686400.0       1.59%  9.732096e+06
54              backbone.layer3.1.bn2  256  60  60  256  60  60       512.0       3.52       3,686,400.0       1,843,200.0   3688448.0    3686400.0       0.06%  7.374848e+06
55             backbone.layer3.1.act2  256  60  60  256  60  60         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.03%  7.372800e+06
56            backbone.layer4.0.conv1  256  60  60  512  30  30   1179648.0       1.76   2,122,905,600.0   1,061,683,200.0   8404992.0    1843200.0       1.58%  1.024819e+07
57              backbone.layer4.0.bn1  512  30  30  512  30  30      1024.0       1.76       1,843,200.0         921,600.0   1847296.0    1843200.0       0.06%  3.690496e+06
58       backbone.layer4.0.drop_block  512  30  30  512  30  30         0.0       1.76               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
59             backbone.layer4.0.act1  512  30  30  512  30  30         0.0       1.76         460,800.0         460,800.0   1843200.0    1843200.0       0.03%  3.686400e+06
60               backbone.layer4.0.aa  512  30  30  512  30  30         0.0       1.76               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
61            backbone.layer4.0.conv2  512  30  30  512  30  30   2359296.0       1.76   4,246,272,000.0   2,123,366,400.0  11280384.0    1843200.0       1.90%  1.312358e+07
62              backbone.layer4.0.bn2  512  30  30  512  30  30      1024.0       1.76       1,843,200.0         921,600.0   1847296.0    1843200.0       0.05%  3.690496e+06
63             backbone.layer4.0.act2  512  30  30  512  30  30         0.0       1.76         460,800.0         460,800.0   1843200.0    1843200.0       0.02%  3.686400e+06
64     backbone.layer4.0.downsample.0  256  60  60  512  30  30    131072.0       1.76     235,468,800.0     117,964,800.0   4210688.0    1843200.0       1.38%  6.053888e+06
65     backbone.layer4.0.downsample.1  512  30  30  512  30  30      1024.0       1.76       1,843,200.0         921,600.0   1847296.0    1843200.0       0.06%  3.690496e+06
66            backbone.layer4.1.conv1  512  30  30  512  30  30   2359296.0       1.76   4,246,272,000.0   2,123,366,400.0  11280384.0    1843200.0       1.52%  1.312358e+07
67              backbone.layer4.1.bn1  512  30  30  512  30  30      1024.0       1.76       1,843,200.0         921,600.0   1847296.0    1843200.0       0.05%  3.690496e+06
68       backbone.layer4.1.drop_block  512  30  30  512  30  30         0.0       1.76               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
69             backbone.layer4.1.act1  512  30  30  512  30  30         0.0       1.76         460,800.0         460,800.0   1843200.0    1843200.0       0.03%  3.686400e+06
70               backbone.layer4.1.aa  512  30  30  512  30  30         0.0       1.76               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
71            backbone.layer4.1.conv2  512  30  30  512  30  30   2359296.0       1.76   4,246,272,000.0   2,123,366,400.0  11280384.0    1843200.0       1.51%  1.312358e+07
72              backbone.layer4.1.bn2  512  30  30  512  30  30      1024.0       1.76       1,843,200.0         921,600.0   1847296.0    1843200.0       0.05%  3.690496e+06
73             backbone.layer4.1.act2  512  30  30  512  30  30         0.0       1.76         460,800.0         460,800.0   1843200.0    1843200.0       0.02%  3.686400e+06
74                 decoder.pre_conv.0  512  30  30   64  30  30     32768.0       0.22      58,924,800.0      29,491,200.0   1974272.0     230400.0       0.47%  2.204672e+06
75                 decoder.pre_conv.1   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.04%  4.613120e+05
76                   decoder.b4.norm1   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.03%  4.613120e+05
77              decoder.b4.attn.qkv.0   64  32  32  192  32  32     12288.0       0.75      24,969,216.0      12,582,912.0    311296.0     786432.0       1.04%  1.097728e+06
78           decoder.b4.attn.local1.0   64  30  30   64  30  30     36864.0       0.22      66,297,600.0      33,177,600.0    377856.0     230400.0       0.75%  6.082560e+05
79           decoder.b4.attn.local1.1   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.08%  4.613120e+05
80           decoder.b4.attn.local2.0   64  30  30   64  30  30      4096.0       0.22       7,315,200.0       3,686,400.0    246784.0     230400.0       0.42%  4.771840e+05
81           decoder.b4.attn.local2.1   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.05%  4.613120e+05
82             decoder.b4.attn.proj.0   64  31  31   64  30  30      4096.0       0.22       7,315,200.0       3,686,400.0    262400.0     230400.0       1.12%  4.928000e+05
83             decoder.b4.attn.proj.1   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.04%  4.613120e+05
84             decoder.b4.attn.proj.2   64  30  30   64  30  30      4096.0       0.22       7,315,200.0       3,686,400.0    246784.0     230400.0       0.52%  4.771840e+05
85             decoder.b4.attn.attn_x   64  31  30   64  30  30         0.0       0.22         460,800.0          59,520.0    238080.0     230400.0       0.11%  4.684800e+05
86             decoder.b4.attn.attn_y   64  30  31   64  30  30         0.0       0.22         460,800.0          59,520.0    238080.0     230400.0       0.08%  4.684800e+05
87               decoder.b4.drop_path   64  30  30   64  30  30         0.0       0.22               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
88                 decoder.b4.mlp.fc1   64  30  30  256  30  30     16640.0       0.88      29,491,200.0      14,976,000.0    296960.0     921600.0       2.63%  1.218560e+06
89                 decoder.b4.mlp.act  256  30  30  256  30  30         0.0       0.88         230,400.0         230,400.0    921600.0     921600.0       0.07%  1.843200e+06
90                 decoder.b4.mlp.fc2  256  30  30   64  30  30     16448.0       0.22      29,491,200.0      14,803,200.0    987392.0     230400.0       0.49%  1.217792e+06
91                decoder.b4.mlp.drop   64  30  30   64  30  30         0.0       0.22               0.0               0.0         0.0          0.0       0.01%  0.000000e+00
92                   decoder.b4.norm2   64  30  30   64  30  30       128.0       0.22         230,400.0         115,200.0    230912.0     230400.0       0.04%  4.613120e+05
93                   decoder.b3.norm1   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.04%  1.843712e+06
94              decoder.b3.attn.qkv.0   64  64  64  192  64  64     12288.0       3.00      99,876,864.0      50,331,648.0   1097728.0    3145728.0       0.45%  4.243456e+06
95           decoder.b3.attn.local1.0   64  60  60   64  60  60     36864.0       0.88     265,190,400.0     132,710,400.0   1069056.0     921600.0       0.22%  1.990656e+06
96           decoder.b3.attn.local1.1   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.07%  1.843712e+06
97           decoder.b3.attn.local2.0   64  60  60   64  60  60      4096.0       0.88      29,260,800.0      14,745,600.0    937984.0     921600.0       0.38%  1.859584e+06
98           decoder.b3.attn.local2.1   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.04%  1.843712e+06
99             decoder.b3.attn.proj.0   64  61  61   64  60  60      4096.0       0.88      29,260,800.0      14,745,600.0    968960.0     921600.0       0.36%  1.890560e+06
100            decoder.b3.attn.proj.1   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.05%  1.843712e+06
101            decoder.b3.attn.proj.2   64  60  60   64  60  60      4096.0       0.88      29,260,800.0      14,745,600.0    937984.0     921600.0       0.17%  1.859584e+06
102            decoder.b3.attn.attn_x   64  61  60   64  60  60         0.0       0.88       1,843,200.0         234,240.0    936960.0     921600.0       0.15%  1.858560e+06
103            decoder.b3.attn.attn_y   64  60  61   64  60  60         0.0       0.88       1,843,200.0         234,240.0    936960.0     921600.0       0.23%  1.858560e+06
104              decoder.b3.drop_path   64  60  60   64  60  60         0.0       0.88               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
105                decoder.b3.mlp.fc1   64  60  60  256  60  60     16640.0       3.52     117,964,800.0      59,904,000.0    988160.0    3686400.0       0.50%  4.674560e+06
106                decoder.b3.mlp.act  256  60  60  256  60  60         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.21%  7.372800e+06
107                decoder.b3.mlp.fc2  256  60  60   64  60  60     16448.0       0.88     117,964,800.0      59,212,800.0   3752192.0     921600.0       0.64%  4.673792e+06
108               decoder.b3.mlp.drop   64  60  60   64  60  60         0.0       0.88               0.0               0.0         0.0          0.0       0.01%  0.000000e+00
109                  decoder.b3.norm2   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.05%  1.843712e+06
110             decoder.p3.pre_conv.0  256  60  60   64  60  60     16384.0       0.88     117,734,400.0      58,982,400.0   3751936.0     921600.0       0.50%  4.673536e+06
111            decoder.p3.post_conv.0   64  60  60   64  60  60     36864.0       0.88     265,190,400.0     132,710,400.0   1069056.0     921600.0       0.64%  1.990656e+06
112            decoder.p3.post_conv.1   64  60  60   64  60  60       128.0       0.88         921,600.0         460,800.0    922112.0     921600.0       0.05%  1.843712e+06
113            decoder.p3.post_conv.2   64  60  60   64  60  60         0.0       0.88         230,400.0         230,400.0    921600.0     921600.0       0.04%  1.843200e+06
114                  decoder.b2.norm1   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.23%  7.373312e+06
115             decoder.b2.attn.qkv.0   64 120 120  192 120 120     12288.0      10.55     351,129,600.0     176,947,200.0   3735552.0   11059200.0       1.41%  1.479475e+07
116          decoder.b2.attn.local1.0   64 120 120   64 120 120     36864.0       3.52   1,060,761,600.0     530,841,600.0   3833856.0    3686400.0       0.98%  7.520256e+06
117          decoder.b2.attn.local1.1   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.23%  7.373312e+06
118          decoder.b2.attn.local2.0   64 120 120   64 120 120      4096.0       3.52     117,043,200.0      58,982,400.0   3702784.0    3686400.0       1.14%  7.389184e+06
119          decoder.b2.attn.local2.1   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.23%  7.373312e+06
120            decoder.b2.attn.proj.0   64 121 121   64 120 120      4096.0       3.52     117,043,200.0      58,982,400.0   3764480.0    3686400.0       0.76%  7.450880e+06
121            decoder.b2.attn.proj.1   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.06%  7.373312e+06
122            decoder.b2.attn.proj.2   64 120 120   64 120 120      4096.0       3.52     117,043,200.0      58,982,400.0   3702784.0    3686400.0       0.85%  7.389184e+06
123            decoder.b2.attn.attn_x   64 121 120   64 120 120         0.0       3.52       7,372,800.0         929,280.0   3717120.0    3686400.0       0.55%  7.403520e+06
124            decoder.b2.attn.attn_y   64 120 121   64 120 120         0.0       3.52       7,372,800.0         929,280.0   3717120.0    3686400.0       0.34%  7.403520e+06
125              decoder.b2.drop_path   64 120 120   64 120 120         0.0       3.52               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
126                decoder.b2.mlp.fc1   64 120 120  256 120 120     16640.0      14.06     471,859,200.0     239,616,000.0   3752960.0   14745600.0       1.63%  1.849856e+07
127                decoder.b2.mlp.act  256 120 120  256 120 120         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.55%  2.949120e+07
128                decoder.b2.mlp.fc2  256 120 120   64 120 120     16448.0       3.52     471,859,200.0     236,851,200.0  14811392.0    3686400.0       1.10%  1.849779e+07
129               decoder.b2.mlp.drop   64 120 120   64 120 120         0.0       3.52               0.0               0.0         0.0          0.0       0.01%  0.000000e+00
130                  decoder.b2.norm2   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.07%  7.373312e+06
131             decoder.p2.pre_conv.0  128 120 120   64 120 120      8192.0       3.52     235,008,000.0     117,964,800.0   7405568.0    3686400.0       1.03%  1.109197e+07
132            decoder.p2.post_conv.0   64 120 120   64 120 120     36864.0       3.52   1,060,761,600.0     530,841,600.0   3833856.0    3686400.0       1.16%  7.520256e+06
133            decoder.p2.post_conv.1   64 120 120   64 120 120       128.0       3.52       3,686,400.0       1,843,200.0   3686912.0    3686400.0       0.26%  7.373312e+06
134            decoder.p2.post_conv.2   64 120 120   64 120 120         0.0       3.52         921,600.0         921,600.0   3686400.0    3686400.0       0.21%  7.372800e+06
135                       decoder.up4    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
136                       decoder.up3    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
137           decoder.aux_head.conv.0    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
138           decoder.aux_head.conv.1    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
139           decoder.aux_head.conv.2    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
140             decoder.aux_head.drop    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
141       decoder.aux_head.conv_out.0    0   0   0    0   0   0         0.0       0.00               0.0               0.0         0.0          0.0       0.00%  0.000000e+00
142             decoder.p1.pre_conv.0   64 240 240   64 240 240      4096.0      14.06     468,172,800.0     235,929,600.0  14761984.0   14745600.0       1.94%  2.950758e+07
143            decoder.p1.post_conv.0   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       2.32%  2.963866e+07
144            decoder.p1.post_conv.1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.56%  2.949171e+07
145            decoder.p1.post_conv.2   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.56%  2.949120e+07
146                   decoder.p1.pa.0   64 240 240   64 240 240       640.0      14.06      66,355,200.0      36,864,000.0  14748160.0   14745600.0       1.81%  2.949376e+07
147                   decoder.p1.pa.1   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.58%  0.000000e+00
148                   decoder.p1.ca.0   64 240 240   64   1   1         0.0       0.00               0.0               0.0         0.0          0.0       0.12%  0.000000e+00
149                 decoder.p1.ca.1.0   64   1   1    4   1   1       256.0       0.00             508.0             256.0      1280.0         16.0       0.09%  1.296000e+03
150                   decoder.p1.ca.2    4   1   1    4   1   1         0.0       0.00               4.0               4.0        16.0         16.0       0.02%  3.200000e+01
151                 decoder.p1.ca.3.0    4   1   1   64   1   1       256.0       0.00             448.0             256.0      1040.0        256.0       0.02%  1.296000e+03
152                   decoder.p1.ca.4   64   1   1   64   1   1         0.0       0.00               0.0               0.0         0.0          0.0       0.01%  0.000000e+00
153             decoder.p1.shortcut.0   64 240 240   64 240 240      4096.0      14.06     468,172,800.0     235,929,600.0  14761984.0   14745600.0       1.75%  2.950758e+07
154             decoder.p1.shortcut.1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.55%  2.949171e+07
155                 decoder.p1.proj.0   64 240 240   64 240 240       576.0      14.06      62,668,800.0      33,177,600.0  14747904.0   14745600.0       1.75%  2.949350e+07
156                 decoder.p1.proj.1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.56%  2.949171e+07
157                 decoder.p1.proj.2   64 240 240   64 240 240      4096.0      14.06     468,172,800.0     235,929,600.0  14761984.0   14745600.0       1.73%  2.950758e+07
158                    decoder.p1.act   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.54%  2.949120e+07
159     decoder.segmentation_head.0.0   64 240 240   64 240 240     36864.0      14.06   4,243,046,400.0   2,123,366,400.0  14893056.0   14745600.0       2.30%  2.963866e+07
160     decoder.segmentation_head.0.1   64 240 240   64 240 240       128.0      14.06      14,745,600.0       7,372,800.0  14746112.0   14745600.0       0.56%  2.949171e+07
161     decoder.segmentation_head.0.2   64 240 240   64 240 240         0.0      14.06       3,686,400.0       3,686,400.0  14745600.0   14745600.0       0.54%  2.949120e+07
162       decoder.segmentation_head.1   64 240 240   64 240 240         0.0      14.06               0.0               0.0         0.0          0.0       0.03%  0.000000e+00
163     decoder.segmentation_head.2.0   64 240 240    6 240 240       384.0       1.32      43,891,200.0      22,118,400.0  14747136.0    1382400.0       1.04%  1.612954e+07
total                                                            11682112.0     989.44  82,322,639,040.0  41,233,060,356.0  14747136.0    1382400.0     100.00%  1.855989e+09
=============================================================================================================================================================================
Total params: 11,682,112
-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Total memory: 989.44MB
Total MAdd: 82.32GMAdd
Total Flops: 41.23GFlops
Total MemR+W: 1.73GB