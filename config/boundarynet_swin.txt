                                     module name   input shape  output shape       params memory(MB)               MAdd              Flops  MemRead(B)  MemWrite(B) duration[%]     MemR+W(B)
0                                inconv0.0.cnn.0     4 384 384    64 384 384       2368.0      36.00      679,477,248.0      349,175,808.0   2368768.0   37748736.0       1.89%  4.011750e+07
1                                inconv0.0.cnn.1    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.67%  7.549798e+07
2                                inconv0.0.cnn.2    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.65%  7.549747e+07
3                                inconv0.0.cnn.3    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       3.32%  7.564518e+07
4                                inconv0.0.cnn.4    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.65%  7.549798e+07
5                                inconv0.0.cnn.5    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.61%  7.549747e+07
6                                inconv0.1.cnn.0    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.75%  7.564518e+07
7                                inconv0.1.cnn.1    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.48%  7.549798e+07
8                                inconv0.1.cnn.2    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.46%  7.549747e+07
9                                inconv0.1.cnn.3    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.40%  7.564518e+07
10                               inconv0.1.cnn.4    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.44%  7.549798e+07
11                               inconv0.1.cnn.5    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.41%  7.549747e+07
12                               inconv1.0.cnn.0    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.40%  7.564518e+07
13                               inconv1.0.cnn.1    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.43%  7.549798e+07
14                               inconv1.0.cnn.2    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.41%  7.549747e+07
15                               inconv1.0.cnn.3    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.37%  7.564518e+07
16                               inconv1.0.cnn.4    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.43%  7.549798e+07
17                               inconv1.0.cnn.5    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.41%  7.549747e+07
18                               inconv1.1.cnn.0    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.35%  7.564518e+07
19                               inconv1.1.cnn.1    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.43%  7.549798e+07
20                               inconv1.1.cnn.2    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.42%  7.549747e+07
21                               inconv1.1.cnn.3    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.36%  7.564518e+07
22                               inconv1.1.cnn.4    64 384 384    64 384 384        128.0      36.00       37,748,736.0       18,874,368.0  37749248.0   37748736.0       0.43%  7.549798e+07
23                               inconv1.1.cnn.5    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.42%  7.549747e+07
24                                     inconv1.2    64 384 384    64 192 192          0.0       9.00        7,077,888.0        9,437,184.0  37748736.0    9437184.0       0.42%  4.718592e+07
25                              encoder1.0.conv1   128 192 192   128 192 192     147456.0      18.00   10,866,917,376.0    5,435,817,984.0  19464192.0   18874368.0       1.00%  3.833856e+07
26                                encoder1.0.bn1   128 192 192   128 192 192        256.0      18.00                0.0                0.0         0.0          0.0       0.06%  0.000000e+00
27                               encoder1.0.relu   128 192 192   128 192 192          0.0      18.00        4,718,592.0        4,718,592.0  18874368.0   18874368.0       0.01%  3.774874e+07
28                              encoder1.0.conv2   128 192 192   128 192 192     147456.0      18.00   10,866,917,376.0    5,435,817,984.0  19464192.0   18874368.0       0.90%  3.833856e+07
29                                encoder1.0.bn2   128 192 192   128 192 192        256.0      18.00                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
30                              encoder1.1.conv1   128 192 192   128 192 192     147456.0      18.00   10,866,917,376.0    5,435,817,984.0  19464192.0   18874368.0       0.88%  3.833856e+07
31                                encoder1.1.bn1   128 192 192   128 192 192        256.0      18.00                0.0                0.0         0.0          0.0       0.23%  0.000000e+00
32                               encoder1.1.relu   128 192 192   128 192 192          0.0      18.00        4,718,592.0        4,718,592.0  18874368.0   18874368.0       0.01%  3.774874e+07
33                              encoder1.1.conv2   128 192 192   128 192 192     147456.0      18.00   10,866,917,376.0    5,435,817,984.0  19464192.0   18874368.0       0.87%  3.833856e+07
34                                encoder1.1.bn2   128 192 192   128 192 192        256.0      18.00                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
35                                    encoder1.2   128 192 192   128  96  96          0.0       4.50        3,538,944.0        4,718,592.0  18874368.0    4718592.0       0.14%  2.359296e+07
36                              encoder2.0.conv1   256  96  96   256  96  96     589824.0       9.00   10,869,276,672.0    5,435,817,984.0  11796480.0    9437184.0       0.71%  2.123366e+07
37                                encoder2.0.bn1   256  96  96   256  96  96        512.0       9.00                0.0                0.0         0.0          0.0       0.13%  0.000000e+00
38                               encoder2.0.relu   256  96  96   256  96  96          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
39                              encoder2.0.conv2   256  96  96   256  96  96     589824.0       9.00   10,869,276,672.0    5,435,817,984.0  11796480.0    9437184.0       0.63%  2.123366e+07
40                                encoder2.0.bn2   256  96  96   256  96  96        512.0       9.00                0.0                0.0         0.0          0.0       0.13%  0.000000e+00
41                              encoder2.1.conv1   256  96  96   256  96  96     589824.0       9.00   10,869,276,672.0    5,435,817,984.0  11796480.0    9437184.0       0.61%  2.123366e+07
42                                encoder2.1.bn1   256  96  96   256  96  96        512.0       9.00                0.0                0.0         0.0          0.0       0.12%  0.000000e+00
43                               encoder2.1.relu   256  96  96   256  96  96          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
44                              encoder2.1.conv2   256  96  96   256  96  96     589824.0       9.00   10,869,276,672.0    5,435,817,984.0  11796480.0    9437184.0       0.61%  2.123366e+07
45                                encoder2.1.bn2   256  96  96   256  96  96        512.0       9.00                0.0                0.0         0.0          0.0       0.12%  0.000000e+00
46                                    encoder2.2   256  96  96   256  48  48          0.0       2.25        1,769,472.0        2,359,296.0   9437184.0    2359296.0       0.07%  1.179648e+07
47                              encoder3.0.conv1   512  48  48   512  48  48    2359296.0       4.50   10,870,456,320.0    5,435,817,984.0  14155776.0    4718592.0       0.51%  1.887437e+07
48                                encoder3.0.bn1   512  48  48   512  48  48       1024.0       4.50                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
49                               encoder3.0.relu   512  48  48   512  48  48          0.0       4.50        1,179,648.0        1,179,648.0   4718592.0    4718592.0       0.01%  9.437184e+06
50                              encoder3.0.conv2   512  48  48   512  48  48    2359296.0       4.50   10,870,456,320.0    5,435,817,984.0  14155776.0    4718592.0       0.41%  1.887437e+07
51                                encoder3.0.bn2   512  48  48   512  48  48       1024.0       4.50                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
52                              encoder3.1.conv1   512  48  48   512  48  48    2359296.0       4.50   10,870,456,320.0    5,435,817,984.0  14155776.0    4718592.0       0.41%  1.887437e+07
53                                encoder3.1.bn1   512  48  48   512  48  48       1024.0       4.50                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
54                               encoder3.1.relu   512  48  48   512  48  48          0.0       4.50        1,179,648.0        1,179,648.0   4718592.0    4718592.0       0.01%  9.437184e+06
55                              encoder3.1.conv2   512  48  48   512  48  48    2359296.0       4.50   10,870,456,320.0    5,435,817,984.0  14155776.0    4718592.0       0.40%  1.887437e+07
56                                encoder3.1.bn2   512  48  48   512  48  48       1024.0       4.50                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
57                                    encoder3.2   512  48  48   512  24  24          0.0       1.12          884,736.0        1,179,648.0   4718592.0    1179648.0       0.05%  5.898240e+06
58                              encoder4.0.conv1  1024  24  24  1024  24  24    9437184.0       2.25   10,871,046,144.0    5,435,817,984.0  40108032.0    2359296.0       1.53%  4.246733e+07
59                                encoder4.0.bn1  1024  24  24  1024  24  24       2048.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
60                               encoder4.0.relu  1024  24  24  1024  24  24          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
61                              encoder4.0.conv2  1024  24  24  1024  24  24    9437184.0       2.25   10,871,046,144.0    5,435,817,984.0  40108032.0    2359296.0       1.39%  4.246733e+07
62                                encoder4.0.bn2  1024  24  24  1024  24  24       2048.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
63                            encoder4.1.convRes  1024  24  24   512  24  24    4718592.0       1.12    5,435,523,072.0    2,717,908,992.0  21233664.0    1179648.0       0.47%  2.241331e+07
64                              encoder4.1.bnRes   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
65                            encoder4.1.reluRes   512  24  24   512  24  24          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.01%  2.359296e+06
66                              encoder4.1.conv1  1024  24  24   512  24  24    4718592.0       1.12    5,435,523,072.0    2,717,908,992.0  21233664.0    1179648.0       0.40%  2.241331e+07
67                                encoder4.1.bn1   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
68                               encoder4.1.relu   512  24  24   512  24  24          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.01%  2.359296e+06
69                              encoder4.1.conv2   512  24  24   512  24  24    2359296.0       1.12    2,717,614,080.0    1,358,954,496.0  10616832.0    1179648.0       0.28%  1.179648e+07
70                                encoder4.1.bn2   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
71                              encoder4.2.conv1   512  24  24   512  24  24    2359296.0       1.12    2,717,614,080.0    1,358,954,496.0  10616832.0    1179648.0       0.23%  1.179648e+07
72                                encoder4.2.bn1   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
73                               encoder4.2.relu   512  24  24   512  24  24          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.01%  2.359296e+06
74                              encoder4.2.conv2   512  24  24   512  24  24    2359296.0       1.12    2,717,614,080.0    1,358,954,496.0  10616832.0    1179648.0       0.24%  1.179648e+07
75                                encoder4.2.bn2   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
76                                    encoder4.3   512  24  24   512  12  12          0.0       0.28          221,184.0          294,912.0   1179648.0     294912.0       0.02%  1.474560e+06
77                     swinunet.patch_embed.proj     4 384 384   128  96  96       8320.0       4.50      150,994,944.0       76,677,120.0   2392576.0    4718592.0       0.48%  7.111168e+06
78                     swinunet.patch_embed.norm      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.21%  0.000000e+00
79                             swinunet.pos_drop      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
80              swinunet.layers.0.blocks.0.norm1      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.07%  0.000000e+00
81     swinunet.layers.0.blocks.0.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.04%  4.547848e+06
82     swinunet.layers.0.blocks.0.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
83     swinunet.layers.0.blocks.0.attn.cpb_mlp.2    47  47 512    47  47   4       2048.0       0.03            4,371.0            2,209.0   4532224.0      35344.0       0.06%  4.567568e+06
84           swinunet.layers.0.blocks.0.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
85     swinunet.layers.0.blocks.0.attn.attn_drop     4 576 576     4 576 576          0.0       5.06                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
86          swinunet.layers.0.blocks.0.attn.proj       576 128       576 128      16512.0       0.28          662,976.0        5,308,416.0   5775360.0    4718592.0       0.11%  1.049395e+07
87     swinunet.layers.0.blocks.0.attn.proj_drop       576 128       576 128          0.0       0.28                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
88       swinunet.layers.0.blocks.0.attn.softmax     4 576 576     4 576 576          0.0       5.06        3,981,311.0                0.0         0.0          0.0       0.94%  0.000000e+00
89          swinunet.layers.0.blocks.0.drop_path      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
90              swinunet.layers.0.blocks.0.norm2      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.07%  0.000000e+00
91            swinunet.layers.0.blocks.0.mlp.fc1      9216 128      9216 512      66048.0      18.00      169,860,096.0       84,934,656.0   4982784.0   18874368.0       0.27%  2.385715e+07
92            swinunet.layers.0.blocks.0.mlp.act      9216 512      9216 512          0.0      18.00                0.0                0.0         0.0          0.0       0.31%  0.000000e+00
93            swinunet.layers.0.blocks.0.mlp.fc2      9216 512      9216 128      65664.0       4.50      169,860,096.0       84,934,656.0  19137024.0    4718592.0       0.17%  2.385562e+07
94           swinunet.layers.0.blocks.0.mlp.drop      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
95              swinunet.layers.0.blocks.1.norm1      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.07%  0.000000e+00
96     swinunet.layers.0.blocks.1.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.03%  4.547848e+06
97     swinunet.layers.0.blocks.1.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
98     swinunet.layers.0.blocks.1.attn.cpb_mlp.2    47  47 512    47  47   4       2048.0       0.03            4,371.0            2,209.0   4532224.0      35344.0       0.02%  4.567568e+06
99           swinunet.layers.0.blocks.1.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
100    swinunet.layers.0.blocks.1.attn.attn_drop     4 576 576     4 576 576          0.0       5.06                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
101         swinunet.layers.0.blocks.1.attn.proj       576 128       576 128      16512.0       0.28          662,976.0        5,308,416.0   5775360.0    4718592.0       0.10%  1.049395e+07
102    swinunet.layers.0.blocks.1.attn.proj_drop       576 128       576 128          0.0       0.28                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
103      swinunet.layers.0.blocks.1.attn.softmax     4 576 576     4 576 576          0.0       5.06        3,981,311.0                0.0         0.0          0.0       0.96%  0.000000e+00
104         swinunet.layers.0.blocks.1.drop_path      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
105             swinunet.layers.0.blocks.1.norm2      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.07%  0.000000e+00
106           swinunet.layers.0.blocks.1.mlp.fc1      9216 128      9216 512      66048.0      18.00      169,860,096.0       84,934,656.0   4982784.0   18874368.0       0.28%  2.385715e+07
107           swinunet.layers.0.blocks.1.mlp.act      9216 512      9216 512          0.0      18.00                0.0                0.0         0.0          0.0       0.23%  0.000000e+00
108           swinunet.layers.0.blocks.1.mlp.fc2      9216 512      9216 128      65664.0       4.50      169,860,096.0       84,934,656.0  19137024.0    4718592.0       0.17%  2.385562e+07
109          swinunet.layers.0.blocks.1.mlp.drop      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
110             swinunet.layers.1.blocks.0.norm1      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
111    swinunet.layers.1.blocks.0.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.03%  4.547848e+06
112    swinunet.layers.1.blocks.0.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
113    swinunet.layers.1.blocks.0.attn.cpb_mlp.2    47  47 512    47  47   8       4096.0       0.07            4,371.0            2,209.0   4540416.0      70688.0       0.02%  4.611104e+06
114          swinunet.layers.1.blocks.0.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
115    swinunet.layers.1.blocks.0.attn.attn_drop     8 576 576     8 576 576          0.0      10.12                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
116         swinunet.layers.1.blocks.0.attn.proj       576 256       576 256      65792.0       0.56          662,976.0        1,327,104.0   3411968.0    2359296.0       0.04%  5.771264e+06
117    swinunet.layers.1.blocks.0.attn.proj_drop       576 256       576 256          0.0       0.56                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
118      swinunet.layers.1.blocks.0.attn.softmax     8 576 576     8 576 576          0.0      10.12        7,962,623.0                0.0         0.0          0.0       0.46%  0.000000e+00
119         swinunet.layers.1.blocks.0.drop_path      2304 256      2304 256          0.0       2.25                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
120             swinunet.layers.1.blocks.0.norm2      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
121           swinunet.layers.1.blocks.0.mlp.fc1      2304 256     2304 1024     263168.0       9.00       10,614,528.0        5,308,416.0   3411968.0    9437184.0       0.11%  1.284915e+07
122           swinunet.layers.1.blocks.0.mlp.act     2304 1024     2304 1024          0.0       9.00                0.0                0.0         0.0          0.0       0.08%  0.000000e+00
123           swinunet.layers.1.blocks.0.mlp.fc2     2304 1024      2304 256     262400.0       2.25       10,614,528.0        5,308,416.0  10486784.0    2359296.0       0.09%  1.284608e+07
124          swinunet.layers.1.blocks.0.mlp.drop      2304 256      2304 256          0.0       2.25                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
125             swinunet.layers.1.blocks.1.norm1      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
126    swinunet.layers.1.blocks.1.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.03%  4.547848e+06
127    swinunet.layers.1.blocks.1.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
128    swinunet.layers.1.blocks.1.attn.cpb_mlp.2    47  47 512    47  47   8       4096.0       0.07            4,371.0            2,209.0   4540416.0      70688.0       0.02%  4.611104e+06
129          swinunet.layers.1.blocks.1.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
130    swinunet.layers.1.blocks.1.attn.attn_drop     8 576 576     8 576 576          0.0      10.12                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
131         swinunet.layers.1.blocks.1.attn.proj       576 256       576 256      65792.0       0.56          662,976.0        1,327,104.0   3411968.0    2359296.0       0.06%  5.771264e+06
132    swinunet.layers.1.blocks.1.attn.proj_drop       576 256       576 256          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
133      swinunet.layers.1.blocks.1.attn.softmax     8 576 576     8 576 576          0.0      10.12        7,962,623.0                0.0         0.0          0.0       0.47%  0.000000e+00
134         swinunet.layers.1.blocks.1.drop_path      2304 256      2304 256          0.0       2.25                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
135             swinunet.layers.1.blocks.1.norm2      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
136           swinunet.layers.1.blocks.1.mlp.fc1      2304 256     2304 1024     263168.0       9.00       10,614,528.0        5,308,416.0   3411968.0    9437184.0       0.09%  1.284915e+07
137           swinunet.layers.1.blocks.1.mlp.act     2304 1024     2304 1024          0.0       9.00                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
138           swinunet.layers.1.blocks.1.mlp.fc2     2304 1024      2304 256     262400.0       2.25       10,614,528.0        5,308,416.0  10486784.0    2359296.0       0.11%  1.284608e+07
139          swinunet.layers.1.blocks.1.mlp.drop      2304 256      2304 256          0.0       2.25                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
140       swinunet.layers.1.downsample.reduction     2304 1024      2304 256     262144.0       2.25       10,614,528.0        5,308,416.0  10485760.0    2359296.0       0.32%  1.284506e+07
141            swinunet.layers.1.downsample.norm      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
142             swinunet.layers.2.blocks.0.norm1       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
143    swinunet.layers.2.blocks.0.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.03%  4.547848e+06
144    swinunet.layers.2.blocks.0.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
145    swinunet.layers.2.blocks.0.attn.cpb_mlp.2    47  47 512    47  47  16       8192.0       0.13            4,371.0            2,209.0   4556800.0     141376.0       0.02%  4.698176e+06
146          swinunet.layers.2.blocks.0.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
147    swinunet.layers.2.blocks.0.attn.attn_drop    16 576 576    16 576 576          0.0      20.25                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
148         swinunet.layers.2.blocks.0.attn.proj       576 512       576 512     262656.0       1.12          662,976.0          331,776.0   2230272.0    1179648.0       0.05%  3.409920e+06
149    swinunet.layers.2.blocks.0.attn.proj_drop       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
150      swinunet.layers.2.blocks.0.attn.softmax    16 576 576    16 576 576          0.0      20.25       15,925,247.0                0.0         0.0          0.0       0.24%  0.000000e+00
151         swinunet.layers.2.blocks.0.drop_path       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
152             swinunet.layers.2.blocks.0.norm2       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
153           swinunet.layers.2.blocks.0.mlp.fc1       576 512      576 2048    1050624.0       4.50          662,976.0          331,776.0   5382144.0    4718592.0       0.10%  1.010074e+07
154           swinunet.layers.2.blocks.0.mlp.act      576 2048      576 2048          0.0       4.50                0.0                0.0         0.0          0.0       0.06%  0.000000e+00
155           swinunet.layers.2.blocks.0.mlp.fc2      576 2048       576 512    1049088.0       1.12          662,976.0          331,776.0   8914944.0    1179648.0       0.16%  1.009459e+07
156          swinunet.layers.2.blocks.0.mlp.drop       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
157             swinunet.layers.2.blocks.1.norm1       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
158    swinunet.layers.2.blocks.1.attn.cpb_mlp.0    47  47   2    47  47 512       1536.0       4.31            4,371.0            2,209.0     23816.0    4524032.0       0.03%  4.547848e+06
159    swinunet.layers.2.blocks.1.attn.cpb_mlp.1    47  47 512    47  47 512          0.0       4.31        1,131,008.0        1,131,008.0   4524032.0    4524032.0       0.02%  9.048064e+06
160    swinunet.layers.2.blocks.1.attn.cpb_mlp.2    47  47 512    47  47  16       8192.0       0.13            4,371.0            2,209.0   4556800.0     141376.0       0.02%  4.698176e+06
161          swinunet.layers.2.blocks.1.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
162    swinunet.layers.2.blocks.1.attn.attn_drop    16 576 576    16 576 576          0.0      20.25                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
163         swinunet.layers.2.blocks.1.attn.proj       576 512       576 512     262656.0       1.12          662,976.0          331,776.0   2230272.0    1179648.0       0.05%  3.409920e+06
164    swinunet.layers.2.blocks.1.attn.proj_drop       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
165      swinunet.layers.2.blocks.1.attn.softmax    16 576 576    16 576 576          0.0      20.25       15,925,247.0                0.0         0.0          0.0       0.23%  0.000000e+00
166         swinunet.layers.2.blocks.1.drop_path       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
167             swinunet.layers.2.blocks.1.norm2       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
168           swinunet.layers.2.blocks.1.mlp.fc1       576 512      576 2048    1050624.0       4.50          662,976.0          331,776.0   5382144.0    4718592.0       0.11%  1.010074e+07
169           swinunet.layers.2.blocks.1.mlp.act      576 2048      576 2048          0.0       4.50                0.0                0.0         0.0          0.0       0.03%  0.000000e+00
170           swinunet.layers.2.blocks.1.mlp.fc2      576 2048       576 512    1049088.0       1.12          662,976.0          331,776.0   8914944.0    1179648.0       0.16%  1.009459e+07
171          swinunet.layers.2.blocks.1.mlp.drop       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
172       swinunet.layers.2.downsample.reduction      576 2048       576 512    1048576.0       1.12          662,976.0          331,776.0   8912896.0    1179648.0       0.39%  1.009254e+07
173            swinunet.layers.2.downsample.norm       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
174             swinunet.layers.3.blocks.0.norm1      144 1024      144 1024       2048.0       0.56                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
175    swinunet.layers.3.blocks.0.attn.cpb_mlp.0    23  23   2    23  23 512       1536.0       1.03            1,035.0              529.0     10376.0    1083392.0       0.03%  1.093768e+06
176    swinunet.layers.3.blocks.0.attn.cpb_mlp.1    23  23 512    23  23 512          0.0       1.03          270,848.0          270,848.0   1083392.0    1083392.0       0.02%  2.166784e+06
177    swinunet.layers.3.blocks.0.attn.cpb_mlp.2    23  23 512    23  23  32      16384.0       0.06            1,035.0              529.0   1148928.0      67712.0       0.02%  1.216640e+06
178          swinunet.layers.3.blocks.0.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
179    swinunet.layers.3.blocks.0.attn.attn_drop    32 144 144    32 144 144          0.0       2.53                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
180         swinunet.layers.3.blocks.0.attn.proj      144 1024      144 1024    1049600.0       0.56           41,328.0           20,736.0   4788224.0     589824.0       0.11%  5.378048e+06
181    swinunet.layers.3.blocks.0.attn.proj_drop      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
182      swinunet.layers.3.blocks.0.attn.softmax    32 144 144    32 144 144          0.0       2.53        1,990,655.0                0.0         0.0          0.0       0.02%  0.000000e+00
183         swinunet.layers.3.blocks.0.drop_path      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
184             swinunet.layers.3.blocks.0.norm2      144 1024      144 1024       2048.0       0.56                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
185           swinunet.layers.3.blocks.0.mlp.fc1      144 1024      144 4096    4198400.0       2.25           41,328.0           20,736.0  17383424.0    2359296.0       0.34%  1.974272e+07
186           swinunet.layers.3.blocks.0.mlp.act      144 4096      144 4096          0.0       2.25                0.0                0.0         0.0          0.0       0.06%  0.000000e+00
187           swinunet.layers.3.blocks.0.mlp.fc2      144 4096      144 1024    4195328.0       0.56           41,328.0           20,736.0  19140608.0     589824.0       0.11%  1.973043e+07
188          swinunet.layers.3.blocks.0.mlp.drop      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
189             swinunet.layers.3.blocks.1.norm1      144 1024      144 1024       2048.0       0.56                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
190    swinunet.layers.3.blocks.1.attn.cpb_mlp.0    23  23   2    23  23 512       1536.0       1.03            1,035.0              529.0     10376.0    1083392.0       0.03%  1.093768e+06
191    swinunet.layers.3.blocks.1.attn.cpb_mlp.1    23  23 512    23  23 512          0.0       1.03          270,848.0          270,848.0   1083392.0    1083392.0       0.01%  2.166784e+06
192    swinunet.layers.3.blocks.1.attn.cpb_mlp.2    23  23 512    23  23  32      16384.0       0.06            1,035.0              529.0   1148928.0      67712.0       0.02%  1.216640e+06
193          swinunet.layers.3.blocks.1.attn.qkv     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
194    swinunet.layers.3.blocks.1.attn.attn_drop    32 144 144    32 144 144          0.0       2.53                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
195         swinunet.layers.3.blocks.1.attn.proj      144 1024      144 1024    1049600.0       0.56           41,328.0           20,736.0   4788224.0     589824.0       0.10%  5.378048e+06
196    swinunet.layers.3.blocks.1.attn.proj_drop      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
197      swinunet.layers.3.blocks.1.attn.softmax    32 144 144    32 144 144          0.0       2.53        1,990,655.0                0.0         0.0          0.0       0.02%  0.000000e+00
198         swinunet.layers.3.blocks.1.drop_path      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
199             swinunet.layers.3.blocks.1.norm2      144 1024      144 1024       2048.0       0.56                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
200           swinunet.layers.3.blocks.1.mlp.fc1      144 1024      144 4096    4198400.0       2.25           41,328.0           20,736.0  17383424.0    2359296.0       0.20%  1.974272e+07
201           swinunet.layers.3.blocks.1.mlp.act      144 4096      144 4096          0.0       2.25                0.0                0.0         0.0          0.0       0.03%  0.000000e+00
202           swinunet.layers.3.blocks.1.mlp.fc2      144 4096      144 1024    4195328.0       0.56           41,328.0           20,736.0  19140608.0     589824.0       0.12%  1.973043e+07
203          swinunet.layers.3.blocks.1.mlp.drop      144 1024      144 1024          0.0       0.56                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
204       swinunet.layers.3.downsample.reduction      144 4096      144 1024    4194304.0       0.56           41,328.0           20,736.0  19136512.0     589824.0       0.29%  1.972634e+07
205            swinunet.layers.3.downsample.norm      144 1024      144 1024       2048.0       0.56                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
206                                swinunet.norm     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
207                             swinunet.avgpool     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
208                                swinunet.head     0   0   0     0   0   0          0.0       0.00                0.0                0.0         0.0          0.0       0.00%  0.000000e+00
209                    tocnn_block1.conv_project   128  96  96    64  96  96       8256.0       2.25      150,994,944.0       76,087,296.0   4751616.0    2359296.0       0.30%  7.110912e+06
210                              tocnn_block1.bn    64  96  96    64  96  96        128.0       2.25        2,359,296.0        1,179,648.0   2359808.0    2359296.0       0.02%  4.719104e+06
211                             tocnn_block1.act    64  96  96    64  96  96          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.03%  4.718592e+06
212                    tocnn_block2.conv_project   256  48  48   128  48  48      32896.0       1.12      150,994,944.0       75,792,384.0   2490880.0    1179648.0       0.18%  3.670528e+06
213                              tocnn_block2.bn   128  48  48   128  48  48        256.0       1.12        1,179,648.0          589,824.0   1180672.0    1179648.0       0.02%  2.360320e+06
214                             tocnn_block2.act   128  48  48   128  48  48          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.02%  2.359296e+06
215                    tocnn_block3.conv_project   512  24  24   256  24  24     131328.0       0.56      150,994,944.0       75,644,928.0   1704960.0     589824.0       0.24%  2.294784e+06
216                              tocnn_block3.bn   256  24  24   256  24  24        512.0       0.56          589,824.0          294,912.0    591872.0     589824.0       0.04%  1.181696e+06
217                             tocnn_block3.act   256  24  24   256  24  24          0.0       0.56          147,456.0          147,456.0    589824.0     589824.0       0.03%  1.179648e+06
218                    tocnn_block4.conv_project  1024  12  12   512  12  12     524800.0       0.28      150,994,944.0       75,571,200.0   2689024.0     294912.0       0.18%  2.983936e+06
219                              tocnn_block4.bn   512  12  12   512  12  12       1024.0       0.28          294,912.0          147,456.0    299008.0     294912.0       0.02%  5.939200e+05
220                             tocnn_block4.act   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
221                    tocnn_block5.conv_project  1024  12  12   512  12  12     524800.0       0.28      150,994,944.0       75,571,200.0   2689024.0     294912.0       0.08%  2.983936e+06
222                              tocnn_block5.bn   512  12  12   512  12  12       1024.0       0.28          294,912.0          147,456.0    299008.0     294912.0       0.02%  5.939200e+05
223                             tocnn_block5.act   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
224                  toswint_block1.conv_project   128  96  96   128  96  96      16512.0       4.50      301,989,888.0      152,174,592.0   4784640.0    4718592.0       0.17%  9.503232e+06
225                            toswint_block1.ln      9216 128      9216 128        256.0       4.50                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
226                           toswint_block1.act      9216 128      9216 128          0.0       4.50                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
227                  toswint_block2.conv_project   256  48  48   256  48  48      65792.0       2.25      301,989,888.0      151,584,768.0   2622464.0    2359296.0       0.18%  4.981760e+06
228                            toswint_block2.ln      2304 256      2304 256        512.0       2.25                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
229                           toswint_block2.act      2304 256      2304 256          0.0       2.25                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
230                  toswint_block3.conv_project   512  24  24   512  24  24     262656.0       1.12      301,989,888.0      151,289,856.0   2230272.0    1179648.0       0.18%  3.409920e+06
231                            toswint_block3.ln       576 512       576 512       1024.0       1.12                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
232                           toswint_block3.act       576 512       576 512          0.0       1.12                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
233                                mulfu.conv1.0   512  12  12   512  12  12     262144.0       0.28       75,423,744.0       37,748,736.0   1343488.0     294912.0       0.12%  1.638400e+06
234                                mulfu.conv1.1   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
235                                mulfu.conv2.0   512  12  12   512  12  12    2359296.0       0.28      679,403,520.0      339,738,624.0   9732096.0     294912.0       0.15%  1.002701e+07
236                                mulfu.conv2.1   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
237                                mulfu.conv3.0   512  12  12   512  12  12    2359296.0       0.28      679,403,520.0      339,738,624.0   9732096.0     294912.0       0.13%  1.002701e+07
238                                mulfu.conv3.1   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
239                                mulfu.conv4.0   512  12  12   512  12  12    2359296.0       0.28      679,403,520.0      339,738,624.0   9732096.0     294912.0       8.09%  1.002701e+07
240                                mulfu.conv4.1   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
241                    mulfu.globalavg_pooling.0   512  12  12   512   1   1          0.0       0.00                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
242                    mulfu.globalavg_pooling.1   512   1   1   512   1   1     262144.0       0.00          523,776.0          262,144.0   1050624.0       2048.0       0.02%  1.052672e+06
243                    mulfu.globalavg_pooling.2   512   1   1   512   1   1          0.0       0.00              512.0              512.0      2048.0       2048.0       0.01%  4.096000e+03
244                    mulfu.globalmax_pooling.0   512  12  12   512   1   1          0.0       0.00           73,216.0           73,728.0    294912.0       2048.0       0.01%  2.969600e+05
245                    mulfu.globalmax_pooling.1   512   1   1   512   1   1     262144.0       0.00          523,776.0          262,144.0   1050624.0       2048.0       0.02%  1.052672e+06
246                    mulfu.globalmax_pooling.2   512   1   1   512   1   1          0.0       0.00              512.0              512.0      2048.0       2048.0       0.01%  4.096000e+03
247                             mulfu.conv_cat.0  3072  12  12   512  12  12    1573376.0       0.28      452,984,832.0      226,566,144.0   8062976.0     294912.0       0.21%  8.357888e+06
248                             mulfu.conv_cat.1   512  12  12   512  12  12       1024.0       0.28          294,912.0          147,456.0    299008.0     294912.0       0.02%  5.939200e+05
249                             mulfu.conv_cat.2   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
250                                 att.f_self.0   512  12  12   256  12  12     131072.0       0.14       37,711,872.0       18,874,368.0    819200.0     147456.0       0.15%  9.666560e+05
251                                 att.f_self.1   256  12  12   256  12  12        512.0       0.14          147,456.0           73,728.0    149504.0     147456.0       0.02%  2.969600e+05
252                                    att.f_x.0   512  12  12   256  12  12     131072.0       0.14       37,711,872.0       18,874,368.0    819200.0     147456.0       0.08%  9.666560e+05
253                                    att.f_x.1   256  12  12   256  12  12        512.0       0.14          147,456.0           73,728.0    149504.0     147456.0       0.01%  2.969600e+05
254                                    att.f_y.0   512  12  12   256  12  12     131072.0       0.14       37,711,872.0       18,874,368.0    819200.0     147456.0       0.08%  9.666560e+05
255                                    att.f_y.1   256  12  12   256  12  12        512.0       0.14          147,456.0           73,728.0    149504.0     147456.0       0.01%  2.969600e+05
256                                   att.f_up.0   256  12  12   512  12  12     131072.0       0.28       37,675,008.0       18,874,368.0    671744.0     294912.0       0.12%  9.666560e+05
257                                   att.f_up.1   512  12  12   512  12  12       1024.0       0.28          294,912.0          147,456.0    299008.0     294912.0       0.02%  5.939200e+05
258                        att.ecalayer.avg_pool   512  12  12   512   1   1          0.0       0.00                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
259                            att.ecalayer.conv         1 512         1 512          3.0       0.00                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
260                         att.ecalayer.sigmoid   512   1   1   512   1   1          0.0       0.00                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
261                                   decoder4.0  1536  12  12  1024  12  12   14156800.0       0.56    4,076,863,488.0    2,038,579,200.0  57511936.0     589824.0       1.65%  5.810176e+07
262                                   decoder4.1  1024  12  12  1024  12  12       2048.0       0.56                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
263                                   decoder4.2  1024  12  12  1024  12  12          0.0       0.56          147,456.0          147,456.0    589824.0     589824.0       0.01%  1.179648e+06
264                                   decoder4.3  1024  12  12   512  12  12    4719104.0       0.28    1,358,954,496.0      679,550,976.0  19466240.0     294912.0       0.25%  1.976115e+07
265                                   decoder4.4   512  12  12   512  12  12       1024.0       0.28                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
266                                   decoder4.5   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
267                                   decoder4.6   512  12  12   512  12  12    2359808.0       0.28      679,477,248.0      339,812,352.0   9734144.0     294912.0       0.16%  1.002906e+07
268                                   decoder4.7   512  12  12   512  12  12       1024.0       0.28                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
269                                   decoder4.8   512  12  12   512  12  12          0.0       0.28           73,728.0           73,728.0    294912.0     294912.0       0.01%  5.898240e+05
270                                   decoder3.0  1536  24  24  1024  24  24   14156800.0       2.25   16,307,453,952.0    8,154,316,800.0  60166144.0    2359296.0       2.08%  6.252544e+07
271                                   decoder3.1  1024  24  24  1024  24  24       2048.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
272                                   decoder3.2  1024  24  24  1024  24  24          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
273                                   decoder3.3  1024  24  24   512  24  24    4719104.0       1.12    5,435,817,984.0    2,718,203,904.0  21235712.0    1179648.0       0.42%  2.241536e+07
274                                   decoder3.4   512  24  24   512  24  24       1024.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
275                                   decoder3.5   512  24  24   512  24  24          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.01%  2.359296e+06
276                                   decoder3.6   512  24  24   256  24  24    1179904.0       0.56    1,358,954,496.0      679,624,704.0   5899264.0     589824.0       0.20%  6.489088e+06
277                                   decoder3.7   256  24  24   256  24  24        512.0       0.56                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
278                                   decoder3.8   256  24  24   256  24  24          0.0       0.56          147,456.0          147,456.0    589824.0     589824.0       0.01%  1.179648e+06
279                                   decoder2.0   768  48  48   512  48  48    3539456.0       4.50   16,307,453,952.0    8,154,906,624.0  21235712.0    4718592.0       0.67%  2.595430e+07
280                                   decoder2.1   512  48  48   512  48  48       1024.0       4.50                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
281                                   decoder2.2   512  48  48   512  48  48          0.0       4.50        1,179,648.0        1,179,648.0   4718592.0    4718592.0       0.02%  9.437184e+06
282                                   decoder2.3   512  48  48   256  48  48    1179904.0       2.25    5,435,817,984.0    2,718,498,816.0   9438208.0    2359296.0       0.36%  1.179750e+07
283                                   decoder2.4   256  48  48   256  48  48        512.0       2.25                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
284                                   decoder2.5   256  48  48   256  48  48          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
285                                   decoder2.6   256  48  48   128  48  48     295040.0       1.12    1,358,954,496.0      679,772,160.0   3539456.0    1179648.0       0.25%  4.719104e+06
286                                   decoder2.7   128  48  48   128  48  48        256.0       1.12                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
287                                   decoder2.8   128  48  48   128  48  48          0.0       1.12          294,912.0          294,912.0   1179648.0    1179648.0       0.01%  2.359296e+06
288                                   decoder1.0   384  96  96   256  96  96     884992.0       9.00   16,307,453,952.0    8,156,086,272.0  17695744.0    9437184.0       0.78%  2.713293e+07
289                                   decoder1.1   256  96  96   256  96  96        512.0       9.00                0.0                0.0         0.0          0.0       0.05%  0.000000e+00
290                                   decoder1.2   256  96  96   256  96  96          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
291                                   decoder1.3   256  96  96   128  96  96     295040.0       4.50    5,435,817,984.0    2,719,088,640.0  10617344.0    4718592.0       0.32%  1.533594e+07
292                                   decoder1.4   128  96  96   128  96  96        256.0       4.50                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
293                                   decoder1.5   128  96  96   128  96  96          0.0       4.50        1,179,648.0        1,179,648.0   4718592.0    4718592.0       0.02%  9.437184e+06
294                                   decoder1.6   128  96  96    64  96  96      73792.0       2.25    1,358,954,496.0      680,067,072.0   5013760.0    2359296.0       0.29%  7.373056e+06
295                                   decoder1.7    64  96  96    64  96  96        128.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
296                                   decoder1.8    64  96  96    64  96  96          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
297                                  decoder01.0   192 192 192   128 192 192     221312.0      18.00   16,307,453,952.0    8,158,445,568.0  29196800.0   18874368.0       1.68%  4.807117e+07
298                                  decoder01.1   128 192 192   128 192 192        256.0      18.00                0.0                0.0         0.0          0.0       0.25%  0.000000e+00
299                                  decoder01.2   128 192 192   128 192 192          0.0      18.00        4,718,592.0        4,718,592.0  18874368.0   18874368.0       0.01%  3.774874e+07
300                                  decoder01.3   128 192 192    64 192 192      73792.0       9.00    5,435,817,984.0    2,720,268,288.0  19169536.0    9437184.0       0.54%  2.860672e+07
301                                  decoder01.4    64 192 192    64 192 192        128.0       9.00                0.0                0.0         0.0          0.0       0.03%  0.000000e+00
302                                  decoder01.5    64 192 192    64 192 192          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
303                                  decoder01.6    64 192 192    64 192 192      36928.0       9.00    2,717,908,992.0    1,361,313,792.0   9584896.0    9437184.0       0.42%  1.902208e+07
304                                  decoder01.7    64 192 192    64 192 192        128.0       9.00                0.0                0.0         0.0          0.0       0.04%  0.000000e+00
305                                  decoder01.8    64 192 192    64 192 192          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
306                                   decoder0.0   128 384 384    64 384 384      73792.0      36.00   21,743,271,936.0   10,881,073,152.0  75792640.0   37748736.0       3.83%  1.135414e+08
307                                   decoder0.1    64 384 384    64 384 384        128.0      36.00                0.0                0.0         0.0          0.0       0.44%  0.000000e+00
308                                   decoder0.2    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.02%  7.549747e+07
309                                   decoder0.3    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.51%  7.564518e+07
310                                   decoder0.4    64 384 384    64 384 384        128.0      36.00                0.0                0.0         0.0          0.0       0.43%  0.000000e+00
311                                   decoder0.5    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.02%  7.549747e+07
312                                   decoder0.6    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.47%  7.564518e+07
313                                   decoder0.7    64 384 384    64 384 384        128.0      36.00                0.0                0.0         0.0          0.0       0.43%  0.000000e+00
314                                   decoder0.8    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.02%  7.549747e+07
315                                     upscore6     1  12  12     1 384 384          0.0       0.56                0.0          147,456.0         0.0          0.0       0.06%  0.000000e+00
316                                     upscore5     1  24  24     1 384 384          0.0       0.56                0.0          147,456.0         0.0          0.0       0.06%  0.000000e+00
317                                     upscore4     1  48  48     1 384 384          0.0       0.56                0.0          147,456.0         0.0          0.0       0.06%  0.000000e+00
318                                     upscore3     1  96  96     1 384 384          0.0       0.56                0.0          147,456.0         0.0          0.0       0.06%  0.000000e+00
319                                     upscore2     1 192 192     1 384 384          0.0       0.56                0.0          147,456.0         0.0          0.0       0.06%  0.000000e+00
320                                     outconvb   512  12  12     1  12  12       4609.0       0.00        1,327,104.0          663,696.0    313348.0        576.0       0.12%  3.139240e+05
321                                     outconv6   512  12  12     1  12  12       4609.0       0.00        1,327,104.0          663,696.0    313348.0        576.0       0.06%  3.139240e+05
322                                     outconv5   256  24  24     1  24  24       2305.0       0.00        2,654,208.0        1,327,680.0    599044.0       2304.0       0.15%  6.013480e+05
323                                     outconv4   128  48  48     1  48  48       1153.0       0.01        5,308,416.0        2,656,512.0   1184260.0       9216.0       0.18%  1.193476e+06
324                                     outconv3    64  96  96     1  96  96        577.0       0.04       10,616,832.0        5,317,632.0   2361604.0      36864.0       0.27%  2.398468e+06
325                                     outconv2    64 192 192     1 192 192        577.0       0.14       42,467,328.0       21,270,528.0   9439492.0     147456.0       0.28%  9.586948e+06
326                                     outconv1    64 384 384     1 384 384        577.0       0.56      169,869,312.0       85,082,112.0  37751044.0     589824.0       1.13%  3.834087e+07
327                                diffnet.conv0     1 384 384    64 384 384        640.0      36.00      169,869,312.0       94,371,840.0    592384.0   37748736.0       1.31%  3.834112e+07
328                                diffnet.conv1    64 384 384    64 384 384      36928.0      36.00   10,871,635,968.0    5,445,255,168.0  37896448.0   37748736.0       2.45%  7.564518e+07
329                                  diffnet.bn1    64 384 384    64 384 384        128.0      36.00                0.0                0.0         0.0          0.0       0.43%  0.000000e+00
330                                diffnet.relu1    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.02%  7.549747e+07
331                                diffnet.pool1    64 384 384    64 192 192          0.0       9.00        7,077,888.0        9,437,184.0  37748736.0    9437184.0       0.29%  4.718592e+07
332                                diffnet.conv2    64 192 192    64 192 192      36928.0       9.00    2,717,908,992.0    1,361,313,792.0   9584896.0    9437184.0       0.49%  1.902208e+07
333                                  diffnet.bn2    64 192 192    64 192 192        128.0       9.00                0.0                0.0         0.0          0.0       0.12%  0.000000e+00
334                                diffnet.relu2    64 192 192    64 192 192          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
335                                diffnet.pool2    64 192 192    64  96  96          0.0       2.25        1,769,472.0        2,359,296.0   9437184.0    2359296.0       0.07%  1.179648e+07
336                                diffnet.conv3    64  96  96    64  96  96      36928.0       2.25      679,477,248.0      340,328,448.0   2507008.0    2359296.0       0.22%  4.866304e+06
337                                  diffnet.bn3    64  96  96    64  96  96        128.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
338                                diffnet.relu3    64  96  96    64  96  96          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
339                                diffnet.pool3    64  96  96    64  48  48          0.0       0.56          442,368.0          589,824.0   2359296.0     589824.0       0.04%  2.949120e+06
340                                diffnet.conv4    64  48  48    64  48  48      36928.0       0.56      169,869,312.0       85,082,112.0    737536.0     589824.0       0.16%  1.327360e+06
341                                  diffnet.bn4    64  48  48    64  48  48        128.0       0.56                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
342                                diffnet.relu4    64  48  48    64  48  48          0.0       0.56          147,456.0          147,456.0    589824.0     589824.0       0.01%  1.179648e+06
343                                diffnet.pool4    64  48  48    64  24  24          0.0       0.14          110,592.0          147,456.0    589824.0     147456.0       0.02%  7.372800e+05
344                                diffnet.conv5    64  24  24    64  24  24      36928.0       0.14       42,467,328.0       21,270,528.0    295168.0     147456.0       0.11%  4.426240e+05
345                                  diffnet.bn5    64  24  24    64  24  24        128.0       0.14                0.0                0.0         0.0          0.0       0.01%  0.000000e+00
346                                diffnet.relu5    64  24  24    64  24  24          0.0       0.14           36,864.0           36,864.0    147456.0     147456.0       0.01%  2.949120e+05
347                              diffnet.conv_d4   128  48  48    64  48  48      73792.0       0.56      339,738,624.0      170,016,768.0   1474816.0     589824.0       0.16%  2.064640e+06
348                                diffnet.bn_d4    64  48  48    64  48  48        128.0       0.56                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
349                              diffnet.relu_d4    64  48  48    64  48  48          0.0       0.56          147,456.0          147,456.0    589824.0     589824.0       0.01%  1.179648e+06
350                              diffnet.conv_d3   128  96  96    64  96  96      73792.0       2.25    1,358,954,496.0      680,067,072.0   5013760.0    2359296.0       0.24%  7.373056e+06
351                                diffnet.bn_d3    64  96  96    64  96  96        128.0       2.25                0.0                0.0         0.0          0.0       0.02%  0.000000e+00
352                              diffnet.relu_d3    64  96  96    64  96  96          0.0       2.25          589,824.0          589,824.0   2359296.0    2359296.0       0.01%  4.718592e+06
353                              diffnet.conv_d2   128 192 192    64 192 192      73792.0       9.00    5,435,817,984.0    2,720,268,288.0  19169536.0    9437184.0       0.55%  2.860672e+07
354                                diffnet.bn_d2    64 192 192    64 192 192        128.0       9.00                0.0                0.0         0.0          0.0       0.03%  0.000000e+00
355                              diffnet.relu_d2    64 192 192    64 192 192          0.0       9.00        2,359,296.0        2,359,296.0   9437184.0    9437184.0       0.01%  1.887437e+07
356                              diffnet.conv_d1   128 384 384    64 384 384      73792.0      36.00   21,743,271,936.0   10,881,073,152.0  75792640.0   37748736.0       3.79%  1.135414e+08
357                                diffnet.bn_d1    64 384 384    64 384 384        128.0      36.00                0.0                0.0         0.0          0.0       0.44%  0.000000e+00
358                              diffnet.relu_d1    64 384 384    64 384 384          0.0      36.00        9,437,184.0        9,437,184.0  37748736.0   37748736.0       0.02%  7.549747e+07
359                              diffnet.conv_d0    64 384 384     1 384 384        577.0       0.56      169,869,312.0       85,082,112.0  37751044.0     589824.0       1.13%  3.834087e+07
360                             diffnet.upscore2    64 192 192    64 384 384          0.0      36.00                0.0          147,456.0         0.0          0.0       0.43%  0.000000e+00
total                                                                         138835467.0    2616.37  441,109,393,240.0  220,821,408,560.0         0.0          0.0     100.00%  4.722068e+09
=============================================================================================================================================================================================
Total params: 138,835,467
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
Total memory: 2616.37MB
Total MAdd: 441.11GMAdd
Total Flops: 220.82GFlops
Total MemR+W: 4.4GB