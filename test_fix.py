#!/usr/bin/env python
"""
Test script to verify the fix for the shape mismatch issue
"""
import torch
import torch.nn as nn
import numpy as np

def test_interpolation_fix():
    """Test the interpolation fix for shape mismatch"""
    
    # Simulate the problematic scenario
    config_sample_size = 1280
    model_output_size = 320
    num_classes = 6
    
    # Create mock model output (what we actually get)
    raw_prediction = torch.randn(num_classes, model_output_size, model_output_size)
    
    # Create mock output_probs array (what we expect to fill)
    output_probs = np.zeros(shape=(num_classes, config_sample_size, config_sample_size), dtype=np.float32)
    
    # Simulate the coordinates
    y_off, x_off = 0, 0
    y_slice = slice(y_off, y_off+config_sample_size)
    x_slice = slice(x_off, x_off+config_sample_size)
    
    print(f"Original prediction shape: {raw_prediction.shape}")
    print(f"Expected shape: ({num_classes}, {config_sample_size}, {config_sample_size})")
    print(f"Output probs shape: {output_probs.shape}")
    
    # Apply the fix
    pred = raw_prediction.cpu().numpy()
    if pred.shape[-1] != config_sample_size or pred.shape[-2] != config_sample_size:
        print("Shape mismatch detected, applying interpolation...")
        # Resize prediction to match sample_size using interpolation
        pred_tensor = torch.from_numpy(pred).unsqueeze(0)  # Add batch dimension
        pred_tensor = nn.functional.interpolate(
            pred_tensor, 
            size=(config_sample_size, config_sample_size), 
            mode='bilinear', 
            align_corners=False
        )
        pred = pred_tensor.squeeze(0).numpy()  # Remove batch dimension
        print(f"After interpolation shape: {pred.shape}")
    
    # Try to add to output_probs
    try:
        output_probs[:, y_slice, x_slice] += pred
        print("✅ Success! No shape mismatch error.")
        print(f"Final output_probs shape: {output_probs.shape}")
        return True
    except ValueError as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Testing the interpolation fix...")
    success = test_interpolation_fix()
    if success:
        print("\n🎉 The fix works correctly!")
    else:
        print("\n💥 The fix needs more work.")
