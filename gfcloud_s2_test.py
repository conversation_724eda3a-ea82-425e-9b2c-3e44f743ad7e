from matplotlib.colors import ListedColormap
# import ttach as tta
import multiprocessing.pool as mpp
import multiprocessing as mp
import time
from train_supervision_gfcloud import *
import argparse
from pathlib import Path
import cv2
import numpy as np
import torch
import matplotlib. pyplot as plt

from torch import nn
from torch.utils.data import DataLoader
from tqdm import tqdm

cmap_classied = ListedColormap(["white", "#DC0000CC", "#E64B35CC", "#F39B7FCC","#3C5488CC","#4DBBD5CC","#FFF7AC"])


def seed_everything(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = True


def label2rgb(mask):
    h, w = mask.shape[0], mask.shape[1]
    mask_rgb = np.zeros(shape=(h, w, 3), dtype=np.uint8)
    mask_convert = mask[np.newaxis, :, :]
    mask_rgb[np.all(mask_convert == 3, axis=0)] = [245, 175, 152]
    mask_rgb[np.all(mask_convert == 0, axis=0)] = [255, 255, 255]
    mask_rgb[np.all(mask_convert == 1, axis=0)] = [227, 51, 51]
    mask_rgb[np.all(mask_convert == 2, axis=0)] = [222, 104, 87]
    mask_rgb[np.all(mask_convert == 4, axis=0)] = [99, 118, 160]
    mask_rgb[np.all(mask_convert == 5, axis=0)] = [113, 200, 221]
    return mask_rgb


def img_writer(inp):
    (mask,  mask_id, rgb) = inp
    if rgb:
        mask_name_tif = mask_id + '.png'
        mask_tif = label2rgb(mask)
        cv2.imwrite(mask_name_tif, mask_tif)
    else:
        mask_png = mask.astype(np.uint8)
        mask_name_png = mask_id + '.png'
        cv2.imwrite(mask_name_png, mask_png)

def img_plot_writer(inp):
    (im, gt, mask,  mask_id, rgb) = inp
    plot_name = mask_id + '.png'
    # mask_tif = label2rgb(mask)
    # gt_tif = label2rgb(gt)
    fig, axes = plt.subplots(1, 3, constrained_layout=True,subplot_kw={'xticks': [], 'yticks': []},figsize=(6,2),dpi=300)
    axes = axes.flatten()
    axes[0].imshow(im.transpose(1,2,0))
    axes[1].imshow(gt, cmap=cmap_classied,vmin=-0.5, vmax=6.5,interpolation='nearest')
    axes[2].imshow(mask, cmap=cmap_classied,vmin=-0.5, vmax=6.5,interpolation='nearest')
    fig.savefig(plot_name)



def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("-c", "--config_path", type=Path, required=True, help="Path to  config")
    arg("-o", "--output_path", type=Path, help="Path where to save resulting masks.", required=True)
    # arg("-t", "--tta", help="Test time augmentation.", default=None, choices=[None, "d4", "lr"])
    arg("--rgb", help="whether output rgb images", action='store_true')
    return parser.parse_args()


def main():
    args = get_args()
    seed_everything(42)

    config = py2cfg(args.config_path)
    gamma = config.val_gamma
    oname = f'pred_result_{config.test_weights_name}_g{format(gamma, ".1f")}'
    output_path = path.join(args.output_path, oname)

    if not os.path.exists(output_path):
        os.makedirs(output_path)
    output_path = Path(output_path)
    model = Gfcloud_Train.load_from_checkpoint(
        os.path.join(config.weights_path, config.test_weights_name + '.ckpt'), config=config)
    model.cuda()
    model.eval()
    evaluator = Gfcloud_Evaluator(num_class=config.num_classes)
    evaluator.reset()

    test_dataset = config.test_dataset
    test_dataset.load()
    with torch.no_grad():
        test_loader = DataLoader(
            test_dataset,
            batch_size=8,
            num_workers=4,
            pin_memory=True,
            drop_last=False,
        )
        results = []
        for input in tqdm(test_loader):
            # raw_prediction NxCxHxW
            raw_predictions = model(input['img'].cuda())

            image_ids = input["img_id"]
            masks_true = input['gt_semantic_seg']

            raw_predictions = nn.Softmax(dim=1)(raw_predictions)
            predictions = raw_predictions.argmax(dim=1)

            for i in range(raw_predictions.shape[0]):
                mask = predictions[i].cpu().numpy()
                evaluator.add_batch(pre_image=mask, gt_image=masks_true[i].cpu().numpy())
                mask_name = image_ids[i]
                results.append((input['img'][i].numpy()[[0,1,2],:,:], masks_true[i], mask, str(output_path / mask_name), args.rgb))
    iou_per_class = evaluator.Intersection_over_Union()
    f1_per_class = evaluator.F1()
    OA = evaluator.OA()
    guf1 = evaluator.GUF1()
    suf1 = evaluator.SUF1()
    opre = evaluator.OP()
    orecall = evaluator.OR()
    of1 = evaluator.OF1()
    scp = evaluator.SCP()
    scr = evaluator.SCR()
    scf1 = evaluator.SCF1()
    for class_name, class_iou, class_f1 in zip(config.classes, iou_per_class, f1_per_class):
        print('F1_{}:{}, IOU_{}:{}'.format(class_name, class_f1, class_name, class_iou))
    print('F1:{}, OA:{}, GUF1:{}, SUF1:{}, OP:{}, OR:{}, OF1:{}, SCP:{}, SCR:{}, SCF1:{}'.format(
        np.nanmean(f1_per_class[:-1]), 
        OA,
        guf1,
        suf1,
        opre,
        orecall,
        of1,
        scp,
        scr,
        scf1
        ))

    t0 = time.time()
    mpp.Pool(processes=mp.cpu_count()).map(img_plot_writer, results)
    t1 = time.time()
    img_write_time = t1 - t0
    print('images writing spends: {} s'.format(img_write_time))


if __name__ == "__main__":
    main()
