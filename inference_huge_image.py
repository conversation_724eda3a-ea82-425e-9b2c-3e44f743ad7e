import argparse
from pathlib import Path
import glob
# from PIL import Image
# import ttach as tta
# import cv2
import numpy as np
import torch
import albumentations as albu
# from catalyst.dl import SupervisedRunner
from skimage import exposure
from skimage.morphology import remove_small_holes, remove_small_objects
from tools.cfg import py2cfg
from torch import nn
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from train_supervision import *
import random
import os
import rasterio as rio




def seed_everything(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = True

def sliding_window(img, step=10, window_size=(20, 20)):
    coords=[]
    """ Slide a window_shape window across the image with a stride of step """
    row_breaks = list(range(0, img.shape[1]-window_size[0], step)) + [img.shape[1]-window_size[0]]
    col_breaks = list(range(0, img.shape[2]-window_size[1], step)) + [img.shape[2]-window_size[1]]
    for row in row_breaks:
        for col in col_breaks:
            coords.append(np.array([row, col]))
    return coords

def landcoverai_to_rgb(mask):
    w, h = mask.shape[0], mask.shape[1]
    mask_rgb = np.zeros(shape=(w, h, 3), dtype=np.uint8)
    mask_convert = mask[np.newaxis, :, :]
    mask_rgb[np.all(mask_convert == 3, axis=0)] = [255, 255, 255]
    mask_rgb[np.all(mask_convert == 0, axis=0)] = [233, 193, 133]
    mask_rgb[np.all(mask_convert == 1, axis=0)] = [255, 0, 0]
    mask_rgb[np.all(mask_convert == 2, axis=0)] = [0, 255, 0]
    mask_rgb = cv2.cvtColor(mask_rgb, cv2.COLOR_RGB2BGR)
    return mask_rgb


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("-i", "--image_path", type=Path, required=True, help="Path to  huge image folder")
    arg("-c", "--config_path", type=Path, required=True, help="Path to  config")
    arg("-o", "--output_path", type=Path, help="Path to save resulting masks.", required=True)
    # arg("-t", "--tta", help="Test time augmentation.", default=None, choices=[None, "d4", "lr"])
    # arg("-ph", "--patch-height", help="height of patch size", type=int, default=512)
    # arg("-pw", "--patch-width", help="width of patch size", type=int, default=512)
    arg("-b", "--batch-size", help="batch size", type=int, default=2)
    # arg("-d", "--dataset", help="dataset", default="pv", choices=["pv", "landcoverai", "uavid", "building"])
    return parser.parse_args()

def minmax(im:np.ndarray,p=0.5, vmin=None, vmax=None):
    if (vmin is None) or (vmax is None):
        v_max = np.percentile(im[im!=0], 100-p, [1, 2])
        v_min = np.percentile(im[im!=0], p, [1, 2])
    else:
        v_max = vmax
        v_min = vmin
    v_max = v_max[:, np.newaxis,np.newaxis]
    v_min = v_min[:, np.newaxis,np.newaxis]
    # print(v_min,v_max)
    return np.clip((im-v_min)/(v_max-v_min), 0, 1)

class InferenceDataset(Dataset):
    def __init__(self, tile_list=None, transform=albu.Normalize()):
        self.tile_list = tile_list
        self.transform = transform

    def __getitem__(self, index):
        img = self.tile_list[index]
        img_id = index
        aug = self.transform(image=img)
        img = aug['image']
        img = torch.from_numpy(img).permute(2, 0, 1).float()
        results = dict(img_id=img_id, img=img)
        return results

    def __len__(self):
        return len(self.tile_list)

def clip_gamma(im, y_off, x_off, win_size, g=None):

    y_slice = slice(y_off, y_off+win_size)
    x_slice = slice(x_off, x_off+win_size)


    im = torch.from_numpy(exposure.adjust_gamma(im[:, y_slice, x_slice], gamma=g)).float()

    return  im

class InferenceDatasetPlus(Dataset):
    def __init__(self, im_path, win_size, step, g=0.7, is_rgb=False):
        vmin, vmax = [], []
        nodata = 0
        p=0.5
        with rio.open(im_path) as src:
            h, w  = src.shape
            self.height, self.width = h, w
            self.profile = src.profile.copy()
            sh,sw = h//4, w//4
            smallim = src.read(out_shape=(sh,sw))
            for i in range(smallim.shape[0]):
                im_band = smallim[i]
                vmax.append(np.percentile(im_band[im_band!=nodata], 100-p))
                vmin.append(np.percentile(im_band[im_band!=nodata], p))
            vmin = np.array(vmin)
            vmax = np.array(vmax)
            img = src.read()
            img = minmax(img, vmin=vmin, vmax=vmax)
            self.data = img[[2,1,0,3],:,:]

        self.coords = sliding_window(self.data, step, (win_size,win_size))
        self.g = g
        self.win_size = win_size
        self.is_rgb = is_rgb

    def __getitem__(self, index):
        sample = clip_gamma(self.data, self.coords[index][0], self.coords[index][1], self.win_size, g=self.g)
        end = 3 if self.is_rgb else 4
        s = slice(end)
        item = {}
        item['coord'] = self.coords[index]
        item['img'] = sample[s]
        return item

    def __len__(self):
        return len(self.coords)


def main():
    args = get_args()
    seed_everything(42)
    config = py2cfg(args.config_path)
    model = Supervision_Train.load_from_checkpoint(os.path.join(config.weights_path, config.test_weights_name+'.ckpt'), config=config)

    model.cuda()
    model.eval()

    # if args.tta == "lr":
    #     transforms = tta.Compose(
    #         [
    #             tta.HorizontalFlip(),
    #             tta.VerticalFlip()
    #         ]
    #     )
    #     model = tta.SegmentationTTAWrapper(model, transforms)
    # elif args.tta == "d4":
    #     transforms = tta.Compose(
    #         [
    #             tta.HorizontalFlip(),
    #             # tta.VerticalFlip(),
    #             # tta.Rotate90(angles=[0, 90, 180, 270]),
    #             tta.Scale(scales=[0.75, 1, 1.25, 1.5, 1.75]),
    #             # tta.Multiply(factors=[0.8, 1, 1.2])
    #         ]
    #     )
    #     model = tta.SegmentationTTAWrapper(model, transforms)


    if not os.path.exists(args.output_path):
        os.makedirs(args.output_path)
    # img_paths = list((args.image_path/'image').rglob('*.tif'))
    img_paths = list(args.image_path.rglob('*.TIF'))

    for img_path in img_paths:
        img_name = img_path.name
        # print('origin mask', original_mask.shape)
        # dataset, width_pad, height_pad, output_width, output_height, img_pad, img_shape = \
        #     make_dataset_for_one_huge_image(img_path, patch_size)
        dataset = InferenceDatasetPlus(img_path, win_size=config.sample_size, step=config.sample_size//3, g=0.7, is_rgb=True)
        output_probs = np.zeros(shape=(config.num_classes, dataset.height, dataset.width), dtype=np.float32)

        with torch.no_grad():
            dataloader = DataLoader(dataset=dataset, batch_size=args.batch_size,
                                    drop_last=False, shuffle=False)
            for input in tqdm(dataloader):
                # raw_prediction NxCxHxW
                raw_predictions = model(input['img'].cuda())
                # print('raw_pred shape:', raw_predictions.shape)
                raw_predictions = nn.Softmax(dim=1)(raw_predictions)

                for i in range(raw_predictions.shape[0]):
                    y_off, x_off = input['coord'][i]
                    y_slice = slice(y_off, y_off+config.sample_size)
                    x_slice = slice(x_off, x_off+config.sample_size)
                    output_probs[:, y_slice,x_slice] += raw_predictions[i].cpu().numpy()

        output_mask = output_probs.argmax(axis=0)

        # print(img_shape, output_mask.shape)
        # assert img_shape == output_mask.shape
        profile = dataset.profile.copy()
        profile.update({
            'drive':'COG',
            'RESAMPLING':'NEAREST',
            'count':1,
            'dtype':'uint8'
        })
        with rio.open(os.path.join(args.output_path, img_name), 'w',**profile) as dst:
            dst.write(output_mask,1)
        profile.update({
            'drive':'COG',
            'RESAMPLING':'BILINEAR',
            'count':config.num_classes,
            'dtype':'float32'
        })
        with rio.open(os.path.join(args.output_path, 'prob_'+img_name), 'w',**profile) as dst:
            dst.write(output_probs)


if __name__ == "__main__":
    main()
