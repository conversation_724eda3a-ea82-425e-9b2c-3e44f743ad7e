{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "from os import path\n", "sys.path.append(path.abspath('../series_cloud_research'))\n", "import utils.base as base\n", "import pathlib\n", "import random\n", "import pandas as pd\n", "import rioxarray as riox\n", "random.seed(12)\n", "from tqdm import tqdm\n", "# 1月单独做的时候用的seed=10"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["root = pathlib.Path('/mnt/mfs1/DBankData/Sentinel2.Data.C/')\n", "validtile = [\n", "    '49SCV','49RFP','49RGL','44TKK','50TKL','48RYV',\n", "    '49QDD','45SXR','49TCF','52TES','45SWC','46TFN',\n", "    '48RXU','44TPN','49SFU','50RQS','49RBJ','45TXN',\n", "    '50TQQ','47TQF','51STR','50RMN','46RGV','47SQU',\n", "    '49QEE','51TWM','47RQL','51TXG','46SFE','50SPE',\n", "    '51UWS','44SPC','47SMV','43SFB','44SNE','52UCU'\n", "]\n", "oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg/img')\n", "p_size = 2040\n", "edge_size = 18\n", "half_tr = 5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于每个格网，每个月，取出10个图像块。\n", "什么样的分布是合理的呢？一般都有这些指标：\n", "1. 空间上分布均匀——覆盖多种地物类型，对于局部来说，可以忽略，随机即可。\n", "2. 多种云覆盖情况：云量多/少，阴影多/少，云的大小"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# tname = validtile[1]\n", "\n", "for tname in validtile:\n", "    s2df = base.init_s2df_order(root,tname,'20190101','20200101')\n", "    s2df['names'] = [path.basename(i) for i in s2df.s2list]\n", "    for month in (1,2,3,4,5,6,7,8,9,10,11,12):\n", "        # month = 1\n", "        print(tname,month)\n", "        mdf = s2df.loc[s2df.s2date.dt.month==month]\n", "        names = mdf.names.to_list()\n", "        xr = base.load_s2l1c_time_series(mdf.s2list,[0], chunks={'x':512,'y':512})\n", "        xa = xr[list(xr.keys())[0]]\n", "        df = pd.DataFrame(data={'name':[],'yoff':[],'xoff':[]})\n", "        # crs = xr.rio.crs\n", "        n_try=0\n", "        while True:\n", "            n_try += 1\n", "            if n_try > 100:\n", "                break\n", "            ti = random.randint(0, xa.shape[0]-1)\n", "            yoff = random.randint(edge_size, xa.shape[2]-1-p_size-edge_size)\n", "            xoff = random.randint(edge_size, xa.shape[3]-1-p_size-edge_size)\n", "            yoff = yoff//6*6\n", "            xoff = xoff//6*6\n", "            # print(ti, yoff, xoff)\n", "            # print(df)\n", "            oname = oroot/tname\n", "            os.makedirs(oname.__str__(),exist_ok=True)\n", "            oname = oname/f'{names[ti]}-{int(xr.y[yoff]+half_tr)}-{int(xr.x[xoff]-half_tr)}.tif'\n", "            if oname.exists():\n", "                continue\n", "            if len(df)>0:\n", "                distance = ((df.xoff-xoff)**2+(df.yoff-yoff)**2)**0.5\n", "                # print(distance)\n", "                if distance.min() < 2700:\n", "                    continue\n", "            patch = xa[ti, :, yoff:yoff+p_size, xoff:xoff+p_size].compute()\n", "            mask = (patch == 0)\n", "            # 沿着所有维度计算掩码中 True 值的数量\n", "            count_zeros = mask.sum()\n", "            if count_zeros > 40:\n", "                continue\n", "            \n", "            # patch.rio.write_crs(crs, inplace=True)\n", "            # patch.rio.to_raster(oname,driver='COG',RESAMPLING='NEAREST')\n", "            patch.rio.to_raster(oname.__str__(),driver='COG')\n", "            df.loc[len(df)] = {'name':names[ti],'yoff':yoff,'xoff':xoff}\n", "            if len(df)>=10:\n", "                break\n", "            # print(n_try)\n", "            "]}, {"cell_type": "markdown", "metadata": {}, "source": ["随机分训练测试"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "ims = oroot.rglob('*.tif')\n", "df_im = pd.DataFrame(data={'imp':list(ims)})\n", "df_val = df_im.sample(frac=0.1)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import os\n", "for fn in df_val.imp:\n", "    fn = pathlib.Path(fn)\n", "    os.renames(fn,fn.parent.parent.parent/'val'/fn.parent.parent.name/fn.parent.name/fn.name)\n", "    # print(fn.parent.parent.parent/'val'/fn.parent.parent.name/fn.parent.name/fn.name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["导出STDL图块"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg')\n", "ims = oroot.rglob('img/*/*.tif')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["stdl_root = pathlib.Path('/mnt/mfs1/yinry/CC/STDL_RESULT')\n", "csp_root = pathlib.Path('/mnt/mfs1/yinry/CC/CloudScorePlus_download')\n", "# oroot_stdl = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg/stdl')\n", "# oroot_csp = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg/csp')\n", "# ims = oroot.rglob('*.tif')"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def export_sub(fn, tname, odir):\n", "    # oname = oroot/tname.parent.name/tname.name\n", "    oname = tname.parent.parent.parent/odir/tname.parent.name/tname.name\n", "    if oname.exists():\n", "        return None\n", "    try:\n", "        xr = riox.open_rasterio(fn,chunks={'x':512,'y':512})\n", "    except:\n", "        print('error open: ',fn)\n", "        return None\n", "    # _,yoff_im,xoff_im = path.splitext(tname.name)[0].split('-')\n", "    _,yoff, xoff = path.splitext(tname.name)[0].split('-')\n", "    yoff, xoff = int(yoff), int(xoff)\n", "    # yoff,xoff = int(yoff_im)//6, int(xoff_im)//6\n", "    # xr_sub = xr[:,yoff:yoff+p_size//6,xoff:xoff+p_size//6]\n", "    xr_sub = xr.loc[{'y':slice(yoff,yoff-p_size*half_tr*2),'x':slice(xoff,xoff+p_size*half_tr*2)}]#.compute()\n", "    os.makedirs(tname.parent.parent.parent/odir/tname.parent.name,exist_ok=True)\n", "    \n", "    # xr_sub.rio.to_raster(oname.__str__(),driver='COG')\n", "    try:\n", "        xr_sub.rio.to_raster(oname.__str__(),driver='COG')\n", "    except:\n", "        print('error save: ',fn)\n", "        return xr\n", "    return None\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/50RQS/S2B_MSIL1C_20190623T023559_N0207_R089_T50RQS_20190705T221722.tif\n", "error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/48RYV/S2B_MSIL1C_20190608T032549_N0207_R018_T48RYV_20190626T211136.tif\n", "error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/48RYV/S2B_MSIL1C_20190608T032549_N0207_R018_T48RYV_20190626T211136.tif\n", "error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/49TCF/S2B_MSIL1C_20190608T032549_N0207_R018_T49TCF_20190626T211136.tif\n", "error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/50RQS/S2B_MSIL1C_20190623T023559_N0207_R089_T50RQS_20190705T221722.tif\n", "error open:  /mnt/mfs1/yinry/CC/CloudScorePlus_download/45TXN/S2B_MSIL1C_20190608T050659_N0207_R019_T45TXN_20190626T183352.tif\n"]}], "source": ["for tname in ims:\n", "    fn_stdl = (stdl_root/tname.parent.name/tname.name.split('-')[0]).__str__()+'_transformer.tif'\n", "    # print(fn_stdl)\n", "    r = export_sub(fn_stdl, tname, 'stdl')\n", "    fn_csp = (csp_root/tname.parent.name/tname.name.split('.')[0]).__str__()+'.tif'\n", "    # print(fn_csp)\n", "    r = export_sub(fn_csp, tname, 'csp')\n", "\n", "    # xr = riox.open_rasterio(fn_stdl,chunks={'x':512,'y':512})\n", "    # _,yoff_im,xoff_im = path.splitext(tname.name)[0].split('-')\n", "    # yoff,xoff = int(yoff_im)//6, int(xoff_im)//6\n", "    # xr_sub = xr[:,yoff:yoff+p_size//6,xoff:xoff+p_size//6]\n", "    # os.makedirs(oroot_stdl/tname.parent.name,exist_ok=True)\n", "    # oname = oroot_stdl/tname.parent.name/tname.name\n", "    # xr_sub.rio.to_raster(oname.__str__(),driver='COG')"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["csp_root = pathlib.Path('/mnt/mfs1/yinry/CC/CloudScorePlus_download')\n", "all_csp = [i.name for i in csp_root.rglob('*.tif')]\n", "csp_df = pd.DataFrame(data={'name':all_csp})\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["csp_df.to_csv('/mnt/mfs1/yinry/CC/CloudScorePlus_download/list0217.csv',index=False)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["r.rio.to_raster('test.tif')"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["Affine(60.0, 0.0, 299940.0,\n", "       0.0, -60.0, 5400060.0)"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["r.rio.transform()"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["t = r.loc[{'x':slice(299940,300000)}]"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/plain": ["(299940.0, 5290140.0, 300000.0, 5400060.0)"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["t.rio.bounds()"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["t=xr.loc[{'y':slice(5399990,5399970)}]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg/img/49SCV/S2B_MSIL1C_20190119T033049_N0207_R018_T49SCV_20190119T060700.SAFE-5622-4242.tif'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["t.tr"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt;\n", "Dimensions:      (band: 4, x: 10980, y: 2, datetime: 13)\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 5.4e+06 5.4e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T02:31:09.024000 ... 201...\n", "Data variables:\n", "    EPSG_32652   (datetime, band, y, x) uint16 dask.array&lt;chunksize=(1, 4, 2, 512), meta=np.ndarray&gt;\n", "Attributes: (12/17)\n", "    DATATAKE_1_D<PERSON><PERSON>AKE_TYPE:            INS-NOBS\n", "    DATATAKE_1_SENSING_ORBIT_DIRECTION:  DESCENDING\n", "    DEGRADED_ANC_DATA_PERCENTAGE:        0.0\n", "    DEGRADED_MSI_DATA_PERCENTAGE:        0\n", "    FORMAT_CORRECTNESS:                  PASSED\n", "    GENERAL_QUALITY:                     PASSED\n", "    ...                                  ...\n", "    PRODUCT_TYPE:                        S2MSI1C\n", "    QUANTIFICATION_VALUE:                10000\n", "    RADIOMETRIC_QUALITY:                 PASSED\n", "    SENSOR_QUALITY:                      PASSED\n", "    SPECIAL_VALUE_NODATA:                0\n", "    SPECIAL_VALUE_SATURATED:             65535</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-3a8641cb-c6c3-46da-abb4-f9d43c40189c' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-3a8641cb-c6c3-46da-abb4-f9d43c40189c' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>x</span>: 10980</li><li><span class='xr-has-index'>y</span>: 2</li><li><span class='xr-has-index'>datetime</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-475a8ba8-da5a-4cd5-99d0-49246c6aa0ac' class='xr-section-summary-in' type='checkbox'  checked><label for='section-475a8ba8-da5a-4cd5-99d0-49246c6aa0ac' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-a973125b-9e62-44dd-a1e7-80d5f4ea0e52' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a973125b-9e62-44dd-a1e7-80d5f4ea0e52' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d26a1544-bade-42e6-aeb4-39d1ee09fad2' class='xr-var-data-in' type='checkbox'><label for='data-d26a1544-bade-42e6-aeb4-39d1ee09fad2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-e75fa58e-f271-4c0c-9aa5-62b34e0ae6a1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e75fa58e-f271-4c0c-9aa5-62b34e0ae6a1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-15c6f56a-f5dc-4589-8f3a-0dedc4dd2ab7' class='xr-var-data-in' type='checkbox'><label for='data-15c6f56a-f5dc-4589-8f3a-0dedc4dd2ab7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>5.4e+06 5.4e+06</div><input id='attrs-8e5099b8-b9a5-46d7-9dbc-0e38cf98a066' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8e5099b8-b9a5-46d7-9dbc-0e38cf98a066' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a75c9140-479a-45a9-b30c-6fdd2eab8041' class='xr-var-data-in' type='checkbox'><label for='data-a75c9140-479a-45a9-b30c-6fdd2eab8041' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([5399985., 5399975.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-d939b039-1cda-45d8-8f4e-d783fa9a68c6' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-d939b039-1cda-45d8-8f4e-d783fa9a68c6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b02eff22-e05d-43dc-bbac-0924e0687663' class='xr-var-data-in' type='checkbox'><label for='data-b02eff22-e05d-43dc-bbac-0924e0687663' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 52N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,129],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32652&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 52N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>129.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 52N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,129],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32652&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 5400000.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>datetime</span></div><div class='xr-var-dims'>(datetime)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-01T02:31:09.024000 ... 2...</div><input id='attrs-193c840e-276a-41be-90e3-bc5873f865f7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-193c840e-276a-41be-90e3-bc5873f865f7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8db82480-8400-4902-a553-32d47c1e37a3' class='xr-var-data-in' type='checkbox'><label for='data-8db82480-8400-4902-a553-32d47c1e37a3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2019-01-01T02:31:09.024000000&#x27;, &#x27;2019-01-04T02:41:09.024000000&#x27;,\n", "       &#x27;2019-01-06T02:31:01.024000000&#x27;, &#x27;2019-01-09T02:41:01.024000000&#x27;,\n", "       &#x27;2019-01-11T02:30:49.024000000&#x27;, &#x27;2019-01-14T02:40:49.024000000&#x27;,\n", "       &#x27;2019-01-16T02:30:31.024000000&#x27;, &#x27;2019-01-19T02:40:21.024000000&#x27;,\n", "       &#x27;2019-01-21T02:30:19.024000000&#x27;, &#x27;2019-01-24T02:40:09.024000000&#x27;,\n", "       &#x27;2019-01-26T02:29:51.024000000&#x27;, &#x27;2019-01-29T02:39:41.024000000&#x27;,\n", "       &#x27;2019-01-31T02:29:29.024000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-b1ab0fd5-184f-459f-babe-eeb84727ecb4' class='xr-section-summary-in' type='checkbox'  checked><label for='section-b1ab0fd5-184f-459f-babe-eeb84727ecb4' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>EPSG_32652</span></div><div class='xr-var-dims'>(datetime, band, y, x)</div><div class='xr-var-dtype'>uint16</div><div class='xr-var-preview xr-preview'>dask.array&lt;chunksize=(1, 4, 2, 512), meta=np.ndarray&gt;</div><input id='attrs-493c1413-12c1-4f1d-bd62-d680051168c5' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-493c1413-12c1-4f1d-bd62-d680051168c5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-85ce7161-6926-41fc-8616-d5c871df46a9' class='xr-var-data-in' type='checkbox'><label for='data-85ce7161-6926-41fc-8616-d5c871df46a9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div><div class='xr-var-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 2.18 MiB </td>\n", "                        <td> 8.00 kiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (13, 4, 2, 10980) </td>\n", "                        <td> (1, 4, 2, 512) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 286 chunks in 41 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"374\" height=\"90\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"25\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"0\" y1=\"25\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"0\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"1\" y1=\"0\" x2=\"1\" y2=\"25\" />\n", "  <line x1=\"3\" y1=\"0\" x2=\"3\" y2=\"25\" />\n", "  <line x1=\"5\" y1=\"0\" x2=\"5\" y2=\"25\" />\n", "  <line x1=\"7\" y1=\"0\" x2=\"7\" y2=\"25\" />\n", "  <line x1=\"9\" y1=\"0\" x2=\"9\" y2=\"25\" />\n", "  <line x1=\"11\" y1=\"0\" x2=\"11\" y2=\"25\" />\n", "  <line x1=\"13\" y1=\"0\" x2=\"13\" y2=\"25\" />\n", "  <line x1=\"15\" y1=\"0\" x2=\"15\" y2=\"25\" />\n", "  <line x1=\"17\" y1=\"0\" x2=\"17\" y2=\"25\" />\n", "  <line x1=\"19\" y1=\"0\" x2=\"19\" y2=\"25\" />\n", "  <line x1=\"21\" y1=\"0\" x2=\"21\" y2=\"25\" />\n", "  <line x1=\"23\" y1=\"0\" x2=\"23\" y2=\"25\" />\n", "  <line x1=\"25\" y1=\"0\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"0.0,0.0 25.412616514582485,0.0 25.412616514582485,25.412616514582485 0.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"12.706308\" y=\"45.412617\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >13</text>\n", "  <text x=\"45.412617\" y=\"12.706308\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,45.412617,12.706308)\">1</text>\n", "\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"95\" y1=\"25\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"95\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 109.9485979497544,14.948597949754403 109.9485979497544,40.36121446433689 95.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"215\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"100\" y1=\"0\" x2=\"115\" y2=\"14\" />\n", "  <line x1=\"106\" y1=\"0\" x2=\"121\" y2=\"14\" />\n", "  <line x1=\"111\" y1=\"0\" x2=\"126\" y2=\"14\" />\n", "  <line x1=\"117\" y1=\"0\" x2=\"132\" y2=\"14\" />\n", "  <line x1=\"122\" y1=\"0\" x2=\"137\" y2=\"14\" />\n", "  <line x1=\"128\" y1=\"0\" x2=\"143\" y2=\"14\" />\n", "  <line x1=\"134\" y1=\"0\" x2=\"149\" y2=\"14\" />\n", "  <line x1=\"139\" y1=\"0\" x2=\"154\" y2=\"14\" />\n", "  <line x1=\"145\" y1=\"0\" x2=\"160\" y2=\"14\" />\n", "  <line x1=\"150\" y1=\"0\" x2=\"165\" y2=\"14\" />\n", "  <line x1=\"156\" y1=\"0\" x2=\"171\" y2=\"14\" />\n", "  <line x1=\"162\" y1=\"0\" x2=\"177\" y2=\"14\" />\n", "  <line x1=\"167\" y1=\"0\" x2=\"182\" y2=\"14\" />\n", "  <line x1=\"173\" y1=\"0\" x2=\"188\" y2=\"14\" />\n", "  <line x1=\"178\" y1=\"0\" x2=\"193\" y2=\"14\" />\n", "  <line x1=\"184\" y1=\"0\" x2=\"199\" y2=\"14\" />\n", "  <line x1=\"190\" y1=\"0\" x2=\"205\" y2=\"14\" />\n", "  <line x1=\"195\" y1=\"0\" x2=\"210\" y2=\"14\" />\n", "  <line x1=\"201\" y1=\"0\" x2=\"216\" y2=\"14\" />\n", "  <line x1=\"206\" y1=\"0\" x2=\"221\" y2=\"14\" />\n", "  <line x1=\"212\" y1=\"0\" x2=\"227\" y2=\"14\" />\n", "  <line x1=\"215\" y1=\"0\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 215.0,0.0 229.9485979497544,14.948597949754403 109.9485979497544,14.948597949754403\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"40\" x2=\"229\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "  <line x1=\"115\" y1=\"14\" x2=\"115\" y2=\"40\" />\n", "  <line x1=\"121\" y1=\"14\" x2=\"121\" y2=\"40\" />\n", "  <line x1=\"126\" y1=\"14\" x2=\"126\" y2=\"40\" />\n", "  <line x1=\"132\" y1=\"14\" x2=\"132\" y2=\"40\" />\n", "  <line x1=\"137\" y1=\"14\" x2=\"137\" y2=\"40\" />\n", "  <line x1=\"143\" y1=\"14\" x2=\"143\" y2=\"40\" />\n", "  <line x1=\"149\" y1=\"14\" x2=\"149\" y2=\"40\" />\n", "  <line x1=\"154\" y1=\"14\" x2=\"154\" y2=\"40\" />\n", "  <line x1=\"160\" y1=\"14\" x2=\"160\" y2=\"40\" />\n", "  <line x1=\"165\" y1=\"14\" x2=\"165\" y2=\"40\" />\n", "  <line x1=\"171\" y1=\"14\" x2=\"171\" y2=\"40\" />\n", "  <line x1=\"177\" y1=\"14\" x2=\"177\" y2=\"40\" />\n", "  <line x1=\"182\" y1=\"14\" x2=\"182\" y2=\"40\" />\n", "  <line x1=\"188\" y1=\"14\" x2=\"188\" y2=\"40\" />\n", "  <line x1=\"193\" y1=\"14\" x2=\"193\" y2=\"40\" />\n", "  <line x1=\"199\" y1=\"14\" x2=\"199\" y2=\"40\" />\n", "  <line x1=\"205\" y1=\"14\" x2=\"205\" y2=\"40\" />\n", "  <line x1=\"210\" y1=\"14\" x2=\"210\" y2=\"40\" />\n", "  <line x1=\"216\" y1=\"14\" x2=\"216\" y2=\"40\" />\n", "  <line x1=\"221\" y1=\"14\" x2=\"221\" y2=\"40\" />\n", "  <line x1=\"227\" y1=\"14\" x2=\"227\" y2=\"40\" />\n", "  <line x1=\"229\" y1=\"14\" x2=\"229\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"109.9485979497544,14.948597949754403 229.9485979497544,14.948597949754403 229.9485979497544,40.36121446433689 109.9485979497544,40.36121446433689\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"169.948598\" y=\"60.361214\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >10980</text>\n", "  <text x=\"249.948598\" y=\"27.654906\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,249.948598,27.654906)\">2</text>\n", "  <text x=\"92.474299\" y=\"52.886915\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,92.474299,52.886915)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></li></ul></div></li><li class='xr-section-item'><input id='section-15c2521b-4d71-4b3c-98e6-74eeddef67bb' class='xr-section-summary-in' type='checkbox'  ><label for='section-15c2521b-4d71-4b3c-98e6-74eeddef67bb' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-165efd9b-be53-4957-8e70-08615e1757a6' class='xr-index-data-in' type='checkbox'/><label for='index-165efd9b-be53-4957-8e70-08615e1757a6' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-b8baa93c-99dd-463f-888c-799343dd293d' class='xr-index-data-in' type='checkbox'/><label for='index-b8baa93c-99dd-463f-888c-799343dd293d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-d4c63308-6476-48a0-b67c-1e63b74f753a' class='xr-index-data-in' type='checkbox'/><label for='index-d4c63308-6476-48a0-b67c-1e63b74f753a' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([5399985.0, 5399975.0], dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>datetime</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-dce20139-87e5-4016-903e-92ab690ef9fe' class='xr-index-data-in' type='checkbox'/><label for='index-dce20139-87e5-4016-903e-92ab690ef9fe' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2019-01-01 02:31:09.024000&#x27;, &#x27;2019-01-04 02:41:09.024000&#x27;,\n", "               &#x27;2019-01-06 02:31:01.024000&#x27;, &#x27;2019-01-09 02:41:01.024000&#x27;,\n", "               &#x27;2019-01-11 02:30:49.024000&#x27;, &#x27;2019-01-14 02:40:49.024000&#x27;,\n", "               &#x27;2019-01-16 02:30:31.024000&#x27;, &#x27;2019-01-19 02:40:21.024000&#x27;,\n", "               &#x27;2019-01-21 02:30:19.024000&#x27;, &#x27;2019-01-24 02:40:09.024000&#x27;,\n", "               &#x27;2019-01-26 02:29:51.024000&#x27;, &#x27;2019-01-29 02:39:41.024000&#x27;,\n", "               &#x27;2019-01-31 02:29:29.024000&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;datetime&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-e2e4fd97-06a2-4e93-9fbd-01996218f752' class='xr-section-summary-in' type='checkbox'  ><label for='section-e2e4fd97-06a2-4e93-9fbd-01996218f752' class='xr-section-summary' >Attributes: <span>(17)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>DATATAKE_1_DATATAKE_TYPE :</span></dt><dd>INS-NOBS</dd><dt><span>DATATAKE_1_SENSING_ORBIT_DIRECTION :</span></dt><dd>DESCENDING</dd><dt><span>DEGRADED_ANC_DATA_PERCENTAGE :</span></dt><dd>0.0</dd><dt><span>DEGRADED_MSI_DATA_PERCENTAGE :</span></dt><dd>0</dd><dt><span>FORMAT_CORRECTNESS :</span></dt><dd>PASSED</dd><dt><span>GENERAL_QUALITY :</span></dt><dd>PASSED</dd><dt><span>GEOMETRIC_QUALITY :</span></dt><dd>PASSED</dd><dt><span>PREVIEW_GEO_INFO :</span></dt><dd>Not applicable</dd><dt><span>PREVIEW_IMAGE_URL :</span></dt><dd>Not applicable</dd><dt><span>PROCESSING_BASELINE :</span></dt><dd>2.07</dd><dt><span>PROCESSING_LEVEL :</span></dt><dd>Level-1C</dd><dt><span>PRODUCT_TYPE :</span></dt><dd>S2MSI1C</dd><dt><span>QUANTIFICATION_VALUE :</span></dt><dd>10000</dd><dt><span>RADIOMETRIC_QUALITY :</span></dt><dd>PASSED</dd><dt><span>SENSOR_QUALITY :</span></dt><dd>PASSED</dd><dt><span>SPECIAL_VALUE_NODATA :</span></dt><dd>0</dd><dt><span>SPECIAL_VALUE_SATURATED :</span></dt><dd>65535</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset>\n", "Dimensions:      (band: 4, x: 10980, y: 2, datetime: 13)\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 5.4e+06 5.4e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T02:31:09.024000 ... 201...\n", "Data variables:\n", "    EPSG_32652   (datetime, band, y, x) uint16 dask.array<chunksize=(1, 4, 2, 512), meta=np.ndarray>\n", "Attributes: (12/17)\n", "    DATATAKE_1_D<PERSON><PERSON>AKE_TYPE:            INS-NOBS\n", "    DATATAKE_1_SENSING_ORBIT_DIRECTION:  DESCENDING\n", "    DEGRADED_ANC_DATA_PERCENTAGE:        0.0\n", "    DEGRADED_MSI_DATA_PERCENTAGE:        0\n", "    FORMAT_CORRECTNESS:                  PASSED\n", "    GENERAL_QUALITY:                     PASSED\n", "    ...                                  ...\n", "    PRODUCT_TYPE:                        S2MSI1C\n", "    QUANTIFICATION_VALUE:                10000\n", "    RADIOMETRIC_QUALITY:                 PASSED\n", "    SENSOR_QUALITY:                      PASSED\n", "    SPECIAL_VALUE_NODATA:                0\n", "    SPECIAL_VALUE_SATURATED:             65535"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["t"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["df.loc[0] = {'name':1,'yoff':1,'xoff':1}"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["1    1\n", "0    1\n", "Name: yoff, dtype: int64"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["(df.yoff-2)**2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["for i in range(10):\n", "    select = mdf.sample(1)\n", "select = mdf.sample(1)\n", "src = base.load_s2l1c_dir('/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190106T032129_N0207_R118_T49SCV_20190106T073547.SAFE',group=['10m'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["concating\n"]}], "source": ["random_int = random.randint(0, 100)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (datetime: 13, band: 4, y: 10980, x: 10980)&gt;\n", "dask.array&lt;concatenate, shape=(13, 4, 10980, 10980), dtype=uint16, chunksize=(1, 4, 512, 512), chunktype=numpy.ndarray&gt;\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T03:21:31.024000 ... 201...\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>datetime</span>: 13</li><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-c7218c34-fdea-45d2-ae4d-3f3c4e5a612e' class='xr-array-in' type='checkbox' checked><label for='section-c7218c34-fdea-45d2-ae4d-3f3c4e5a612e' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>dask.array&lt;chunksize=(1, 4, 512, 512), meta=np.ndarray&gt;</span></div><div class='xr-array-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 11.68 GiB </td>\n", "                        <td> 2.00 MiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (13, 4, 10980, 10980) </td>\n", "                        <td> (1, 4, 512, 512) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 6292 chunks in 40 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"374\" height=\"184\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"25\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"0\" y1=\"25\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"0\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"1\" y1=\"0\" x2=\"1\" y2=\"25\" />\n", "  <line x1=\"3\" y1=\"0\" x2=\"3\" y2=\"25\" />\n", "  <line x1=\"5\" y1=\"0\" x2=\"5\" y2=\"25\" />\n", "  <line x1=\"7\" y1=\"0\" x2=\"7\" y2=\"25\" />\n", "  <line x1=\"9\" y1=\"0\" x2=\"9\" y2=\"25\" />\n", "  <line x1=\"11\" y1=\"0\" x2=\"11\" y2=\"25\" />\n", "  <line x1=\"13\" y1=\"0\" x2=\"13\" y2=\"25\" />\n", "  <line x1=\"15\" y1=\"0\" x2=\"15\" y2=\"25\" />\n", "  <line x1=\"17\" y1=\"0\" x2=\"17\" y2=\"25\" />\n", "  <line x1=\"19\" y1=\"0\" x2=\"19\" y2=\"25\" />\n", "  <line x1=\"21\" y1=\"0\" x2=\"21\" y2=\"25\" />\n", "  <line x1=\"23\" y1=\"0\" x2=\"23\" y2=\"25\" />\n", "  <line x1=\"25\" y1=\"0\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"0.0,0.0 25.412616514582485,0.0 25.412616514582485,25.412616514582485 0.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"12.706308\" y=\"45.412617\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >13</text>\n", "  <text x=\"45.412617\" y=\"12.706308\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,45.412617,12.706308)\">1</text>\n", "\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"95\" y1=\"5\" x2=\"109\" y2=\"20\" />\n", "  <line x1=\"95\" y1=\"11\" x2=\"109\" y2=\"26\" />\n", "  <line x1=\"95\" y1=\"16\" x2=\"109\" y2=\"31\" />\n", "  <line x1=\"95\" y1=\"22\" x2=\"109\" y2=\"37\" />\n", "  <line x1=\"95\" y1=\"27\" x2=\"109\" y2=\"42\" />\n", "  <line x1=\"95\" y1=\"33\" x2=\"109\" y2=\"48\" />\n", "  <line x1=\"95\" y1=\"39\" x2=\"109\" y2=\"54\" />\n", "  <line x1=\"95\" y1=\"44\" x2=\"109\" y2=\"59\" />\n", "  <line x1=\"95\" y1=\"50\" x2=\"109\" y2=\"65\" />\n", "  <line x1=\"95\" y1=\"55\" x2=\"109\" y2=\"70\" />\n", "  <line x1=\"95\" y1=\"61\" x2=\"109\" y2=\"76\" />\n", "  <line x1=\"95\" y1=\"67\" x2=\"109\" y2=\"82\" />\n", "  <line x1=\"95\" y1=\"72\" x2=\"109\" y2=\"87\" />\n", "  <line x1=\"95\" y1=\"78\" x2=\"109\" y2=\"93\" />\n", "  <line x1=\"95\" y1=\"83\" x2=\"109\" y2=\"98\" />\n", "  <line x1=\"95\" y1=\"89\" x2=\"109\" y2=\"104\" />\n", "  <line x1=\"95\" y1=\"95\" x2=\"109\" y2=\"110\" />\n", "  <line x1=\"95\" y1=\"100\" x2=\"109\" y2=\"115\" />\n", "  <line x1=\"95\" y1=\"106\" x2=\"109\" y2=\"121\" />\n", "  <line x1=\"95\" y1=\"111\" x2=\"109\" y2=\"126\" />\n", "  <line x1=\"95\" y1=\"117\" x2=\"109\" y2=\"132\" />\n", "  <line x1=\"95\" y1=\"120\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"95\" y2=\"120\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 109.9485979497544,14.948597949754403 109.9485979497544,134.9485979497544 95.0,120.0\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"215\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"100\" y1=\"0\" x2=\"115\" y2=\"14\" />\n", "  <line x1=\"106\" y1=\"0\" x2=\"121\" y2=\"14\" />\n", "  <line x1=\"111\" y1=\"0\" x2=\"126\" y2=\"14\" />\n", "  <line x1=\"117\" y1=\"0\" x2=\"132\" y2=\"14\" />\n", "  <line x1=\"122\" y1=\"0\" x2=\"137\" y2=\"14\" />\n", "  <line x1=\"128\" y1=\"0\" x2=\"143\" y2=\"14\" />\n", "  <line x1=\"134\" y1=\"0\" x2=\"149\" y2=\"14\" />\n", "  <line x1=\"139\" y1=\"0\" x2=\"154\" y2=\"14\" />\n", "  <line x1=\"145\" y1=\"0\" x2=\"160\" y2=\"14\" />\n", "  <line x1=\"150\" y1=\"0\" x2=\"165\" y2=\"14\" />\n", "  <line x1=\"156\" y1=\"0\" x2=\"171\" y2=\"14\" />\n", "  <line x1=\"162\" y1=\"0\" x2=\"177\" y2=\"14\" />\n", "  <line x1=\"167\" y1=\"0\" x2=\"182\" y2=\"14\" />\n", "  <line x1=\"173\" y1=\"0\" x2=\"188\" y2=\"14\" />\n", "  <line x1=\"178\" y1=\"0\" x2=\"193\" y2=\"14\" />\n", "  <line x1=\"184\" y1=\"0\" x2=\"199\" y2=\"14\" />\n", "  <line x1=\"190\" y1=\"0\" x2=\"205\" y2=\"14\" />\n", "  <line x1=\"195\" y1=\"0\" x2=\"210\" y2=\"14\" />\n", "  <line x1=\"201\" y1=\"0\" x2=\"216\" y2=\"14\" />\n", "  <line x1=\"206\" y1=\"0\" x2=\"221\" y2=\"14\" />\n", "  <line x1=\"212\" y1=\"0\" x2=\"227\" y2=\"14\" />\n", "  <line x1=\"215\" y1=\"0\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 215.0,0.0 229.9485979497544,14.948597949754403 109.9485979497544,14.948597949754403\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"20\" x2=\"229\" y2=\"20\" />\n", "  <line x1=\"109\" y1=\"26\" x2=\"229\" y2=\"26\" />\n", "  <line x1=\"109\" y1=\"31\" x2=\"229\" y2=\"31\" />\n", "  <line x1=\"109\" y1=\"37\" x2=\"229\" y2=\"37\" />\n", "  <line x1=\"109\" y1=\"42\" x2=\"229\" y2=\"42\" />\n", "  <line x1=\"109\" y1=\"48\" x2=\"229\" y2=\"48\" />\n", "  <line x1=\"109\" y1=\"54\" x2=\"229\" y2=\"54\" />\n", "  <line x1=\"109\" y1=\"59\" x2=\"229\" y2=\"59\" />\n", "  <line x1=\"109\" y1=\"65\" x2=\"229\" y2=\"65\" />\n", "  <line x1=\"109\" y1=\"70\" x2=\"229\" y2=\"70\" />\n", "  <line x1=\"109\" y1=\"76\" x2=\"229\" y2=\"76\" />\n", "  <line x1=\"109\" y1=\"82\" x2=\"229\" y2=\"82\" />\n", "  <line x1=\"109\" y1=\"87\" x2=\"229\" y2=\"87\" />\n", "  <line x1=\"109\" y1=\"93\" x2=\"229\" y2=\"93\" />\n", "  <line x1=\"109\" y1=\"98\" x2=\"229\" y2=\"98\" />\n", "  <line x1=\"109\" y1=\"104\" x2=\"229\" y2=\"104\" />\n", "  <line x1=\"109\" y1=\"110\" x2=\"229\" y2=\"110\" />\n", "  <line x1=\"109\" y1=\"115\" x2=\"229\" y2=\"115\" />\n", "  <line x1=\"109\" y1=\"121\" x2=\"229\" y2=\"121\" />\n", "  <line x1=\"109\" y1=\"126\" x2=\"229\" y2=\"126\" />\n", "  <line x1=\"109\" y1=\"132\" x2=\"229\" y2=\"132\" />\n", "  <line x1=\"109\" y1=\"134\" x2=\"229\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "  <line x1=\"115\" y1=\"14\" x2=\"115\" y2=\"134\" />\n", "  <line x1=\"121\" y1=\"14\" x2=\"121\" y2=\"134\" />\n", "  <line x1=\"126\" y1=\"14\" x2=\"126\" y2=\"134\" />\n", "  <line x1=\"132\" y1=\"14\" x2=\"132\" y2=\"134\" />\n", "  <line x1=\"137\" y1=\"14\" x2=\"137\" y2=\"134\" />\n", "  <line x1=\"143\" y1=\"14\" x2=\"143\" y2=\"134\" />\n", "  <line x1=\"149\" y1=\"14\" x2=\"149\" y2=\"134\" />\n", "  <line x1=\"154\" y1=\"14\" x2=\"154\" y2=\"134\" />\n", "  <line x1=\"160\" y1=\"14\" x2=\"160\" y2=\"134\" />\n", "  <line x1=\"165\" y1=\"14\" x2=\"165\" y2=\"134\" />\n", "  <line x1=\"171\" y1=\"14\" x2=\"171\" y2=\"134\" />\n", "  <line x1=\"177\" y1=\"14\" x2=\"177\" y2=\"134\" />\n", "  <line x1=\"182\" y1=\"14\" x2=\"182\" y2=\"134\" />\n", "  <line x1=\"188\" y1=\"14\" x2=\"188\" y2=\"134\" />\n", "  <line x1=\"193\" y1=\"14\" x2=\"193\" y2=\"134\" />\n", "  <line x1=\"199\" y1=\"14\" x2=\"199\" y2=\"134\" />\n", "  <line x1=\"205\" y1=\"14\" x2=\"205\" y2=\"134\" />\n", "  <line x1=\"210\" y1=\"14\" x2=\"210\" y2=\"134\" />\n", "  <line x1=\"216\" y1=\"14\" x2=\"216\" y2=\"134\" />\n", "  <line x1=\"221\" y1=\"14\" x2=\"221\" y2=\"134\" />\n", "  <line x1=\"227\" y1=\"14\" x2=\"227\" y2=\"134\" />\n", "  <line x1=\"229\" y1=\"14\" x2=\"229\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"109.9485979497544,14.948597949754403 229.9485979497544,14.948597949754403 229.9485979497544,134.9485979497544 109.9485979497544,134.9485979497544\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"169.948598\" y=\"154.948598\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >10980</text>\n", "  <text x=\"249.948598\" y=\"74.948598\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(-90,249.948598,74.948598)\">10980</text>\n", "  <text x=\"92.474299\" y=\"147.474299\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,92.474299,147.474299)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></div></li><li class='xr-section-item'><input id='section-45e151da-06fd-495c-aa58-94b1cd5d19be' class='xr-section-summary-in' type='checkbox'  checked><label for='section-45e151da-06fd-495c-aa58-94b1cd5d19be' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-98848a4c-5cfd-44b7-89bf-0a88783c1b10' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-98848a4c-5cfd-44b7-89bf-0a88783c1b10' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e9be5e74-3695-4410-a4a9-c20b2d859f64' class='xr-var-data-in' type='checkbox'><label for='data-e9be5e74-3695-4410-a4a9-c20b2d859f64' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-128287bc-61b5-480b-a179-2a43a2f5b40d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-128287bc-61b5-480b-a179-2a43a2f5b40d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f7f02f81-3c10-4241-8860-56d9334a9956' class='xr-var-data-in' type='checkbox'><label for='data-f7f02f81-3c10-4241-8860-56d9334a9956' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-72dbf4b6-10b3-4eea-84e3-b5d5d570fc0d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-72dbf4b6-10b3-4eea-84e3-b5d5d570fc0d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-93aafffc-2ac4-47c8-a31c-50490479894a' class='xr-var-data-in' type='checkbox'><label for='data-93aafffc-2ac4-47c8-a31c-50490479894a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-5a39dec4-a846-4e16-ada5-e2b94cb1f5ef' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-5a39dec4-a846-4e16-ada5-e2b94cb1f5ef' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ea80cee4-8983-4fc0-96b2-c945ea5d1414' class='xr-var-data-in' type='checkbox'><label for='data-ea80cee4-8983-4fc0-96b2-c945ea5d1414' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>datetime</span></div><div class='xr-var-dims'>(datetime)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-01T03:21:31.024000 ... 2...</div><input id='attrs-c9a246e2-da39-4c3f-80de-35b5df95f276' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c9a246e2-da39-4c3f-80de-35b5df95f276' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5620b6c7-a043-4092-88f2-ab0bffeeb5a7' class='xr-var-data-in' type='checkbox'><label for='data-5620b6c7-a043-4092-88f2-ab0bffeeb5a7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2019-01-01T03:21:31.024000000&#x27;, &#x27;2019-01-04T03:31:31.024000000&#x27;,\n", "       &#x27;2019-01-06T03:21:29.024000000&#x27;, &#x27;2019-01-09T03:31:19.024000000&#x27;,\n", "       &#x27;2019-01-11T03:21:11.024000000&#x27;, &#x27;2019-01-14T03:31:01.024000000&#x27;,\n", "       &#x27;2019-01-16T03:20:59.024000000&#x27;, &#x27;2019-01-19T03:30:49.024000000&#x27;,\n", "       &#x27;2019-01-21T03:20:41.024000000&#x27;, &#x27;2019-01-24T03:30:31.024000000&#x27;,\n", "       &#x27;2019-01-26T03:20:19.024000000&#x27;, &#x27;2019-01-29T03:30:09.024000000&#x27;,\n", "       &#x27;2019-01-31T03:19:51.024000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-a9fa57d1-ae35-46b2-b384-33ae3996159d' class='xr-section-summary-in' type='checkbox'  ><label for='section-a9fa57d1-ae35-46b2-b384-33ae3996159d' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-c824997f-8d7c-4ee2-afdd-31d698d34c40' class='xr-index-data-in' type='checkbox'/><label for='index-c824997f-8d7c-4ee2-afdd-31d698d34c40' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-66c8fe8f-7239-45cb-a92e-c6f3e66f05af' class='xr-index-data-in' type='checkbox'/><label for='index-66c8fe8f-7239-45cb-a92e-c6f3e66f05af' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-7f805575-4393-424b-b4c3-1f0e3115b75a' class='xr-index-data-in' type='checkbox'/><label for='index-7f805575-4393-424b-b4c3-1f0e3115b75a' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>datetime</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-00be3ca7-57d1-4a2e-a568-98b2ce3efd26' class='xr-index-data-in' type='checkbox'/><label for='index-00be3ca7-57d1-4a2e-a568-98b2ce3efd26' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2019-01-01 03:21:31.024000&#x27;, &#x27;2019-01-04 03:31:31.024000&#x27;,\n", "               &#x27;2019-01-06 03:21:29.024000&#x27;, &#x27;2019-01-09 03:31:19.024000&#x27;,\n", "               &#x27;2019-01-11 03:21:11.024000&#x27;, &#x27;2019-01-14 03:31:01.024000&#x27;,\n", "               &#x27;2019-01-16 03:20:59.024000&#x27;, &#x27;2019-01-19 03:30:49.024000&#x27;,\n", "               &#x27;2019-01-21 03:20:41.024000&#x27;, &#x27;2019-01-24 03:30:31.024000&#x27;,\n", "               &#x27;2019-01-26 03:20:19.024000&#x27;, &#x27;2019-01-29 03:30:09.024000&#x27;,\n", "               &#x27;2019-01-31 03:19:51.024000&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;datetime&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-2d33b585-8630-4968-b1e1-632ca6766676' class='xr-section-summary-in' type='checkbox'  checked><label for='section-2d33b585-8630-4968-b1e1-632ca6766676' class='xr-section-summary' >Attributes: <span>(9)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (datetime: 13, band: 4, y: 10980, x: 10980)>\n", "dask.array<concatenate, shape=(13, 4, 10980, 10980), dtype=uint16, chunksize=(1, 4, 512, 512), chunktype=numpy.ndarray>\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T03:21:31.024000 ... 201...\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["xa"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (band: 4, y: 100, x: 300)&gt;\n", "dask.array&lt;getitem, shape=(4, 100, 300), dtype=uint16, chunksize=(4, 100, 300), chunktype=numpy.ndarray&gt;\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3.02e+05 3.02e+05 3.02e+05 ... 3.05e+05 3.05e+05\n", "  * y            (y) float64 3.998e+06 3.998e+06 ... 3.997e+06 3.997e+06\n", "    spatial_ref  int64 0\n", "    datetime     datetime64[ns] 2019-01-04T03:31:31.024000\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>y</span>: 100</li><li><span class='xr-has-index'>x</span>: 300</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-8cf7b5d8-46cc-4ffb-b5cc-908e6177541e' class='xr-array-in' type='checkbox' checked><label for='section-8cf7b5d8-46cc-4ffb-b5cc-908e6177541e' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>dask.array&lt;chunksize=(4, 100, 300), meta=np.ndarray&gt;</span></div><div class='xr-array-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 234.38 kiB </td>\n", "                        <td> 234.38 kiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (4, 100, 300) </td>\n", "                        <td> (4, 100, 300) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 1 chunks in 41 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"195\" height=\"109\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"25\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"10\" y1=\"43\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"10\" y2=\"43\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"15\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"10.0,0.0 25.78980198634574,15.78980198634574 25.78980198634574,59.675636493453396 10.0,43.88583450710766\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"130\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"15\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"25\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"130\" y1=\"0\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"10.0,0.0 130.0,0.0 145.78980198634574,15.78980198634574 25.78980198634574,15.78980198634574\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"25\" y1=\"15\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"59\" x2=\"145\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"25\" y1=\"15\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "  <line x1=\"145\" y1=\"15\" x2=\"145\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"25.78980198634574,15.78980198634574 145.78980198634574,15.78980198634574 145.78980198634574,59.675636493453396 25.78980198634574,59.675636493453396\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"85.789802\" y=\"79.675636\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >300</text>\n", "  <text x=\"165.789802\" y=\"37.732719\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(-90,165.789802,37.732719)\">100</text>\n", "  <text x=\"7.894901\" y=\"71.780736\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,7.894901,71.780736)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></div></li><li class='xr-section-item'><input id='section-fee3e1f4-a648-41ba-8e2a-592fde4947d0' class='xr-section-summary-in' type='checkbox'  checked><label for='section-fee3e1f4-a648-41ba-8e2a-592fde4947d0' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-a3e77cd1-0d04-4217-a2dc-eeb438580824' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a3e77cd1-0d04-4217-a2dc-eeb438580824' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-81b0c395-dbe4-4aba-95ec-ad33a799b297' class='xr-var-data-in' type='checkbox'><label for='data-81b0c395-dbe4-4aba-95ec-ad33a799b297' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3.02e+05 3.02e+05 ... 3.05e+05</div><input id='attrs-3a6dbe74-1757-4da5-b45a-58d2e90e9427' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3a6dbe74-1757-4da5-b45a-58d2e90e9427' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-06b933d8-a4f8-433b-ae79-fff40fc909a7' class='xr-var-data-in' type='checkbox'><label for='data-06b933d8-a4f8-433b-ae79-fff40fc909a7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([302005., 302015., 302025., ..., 304975., 304985., 304995.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3.998e+06 3.998e+06 ... 3.997e+06</div><input id='attrs-4e9a14fa-18ab-4f34-bbce-af045e25fd5e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4e9a14fa-18ab-4f34-bbce-af045e25fd5e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2f2caea8-8ee3-46a3-a7ba-1b6eae8bdb1e' class='xr-var-data-in' type='checkbox'><label for='data-2f2caea8-8ee3-46a3-a7ba-1b6eae8bdb1e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([3998015., 3998005., 3997995., 3997985., 3997975., 3997965., 3997955.,\n", "       3997945., 3997935., 3997925., 3997915., 3997905., 3997895., 3997885.,\n", "       3997875., 3997865., 3997855., 3997845., 3997835., 3997825., 3997815.,\n", "       3997805., 3997795., 3997785., 3997775., 3997765., 3997755., 3997745.,\n", "       3997735., 3997725., 3997715., 3997705., 3997695., 3997685., 3997675.,\n", "       3997665., 3997655., 3997645., 3997635., 3997625., 3997615., 3997605.,\n", "       3997595., 3997585., 3997575., 3997565., 3997555., 3997545., 3997535.,\n", "       3997525., 3997515., 3997505., 3997495., 3997485., 3997475., 3997465.,\n", "       3997455., 3997445., 3997435., 3997425., 3997415., 3997405., 3997395.,\n", "       3997385., 3997375., 3997365., 3997355., 3997345., 3997335., 3997325.,\n", "       3997315., 3997305., 3997295., 3997285., 3997275., 3997265., 3997255.,\n", "       3997245., 3997235., 3997225., 3997215., 3997205., 3997195., 3997185.,\n", "       3997175., 3997165., 3997155., 3997145., 3997135., 3997125., 3997115.,\n", "       3997105., 3997095., 3997085., 3997075., 3997065., 3997055., 3997045.,\n", "       3997035., 3997025.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-b5bf67c1-4adc-43d0-be4c-a278ae081654' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-b5bf67c1-4adc-43d0-be4c-a278ae081654' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1abf1cbb-2d3f-4b6e-8fe0-2c7e27e696e2' class='xr-var-data-in' type='checkbox'><label for='data-1abf1cbb-2d3f-4b6e-8fe0-2c7e27e696e2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>datetime</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-04T03:31:31.024000</div><input id='attrs-cddee7bc-839b-4fad-a1cc-daff570c0d5c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cddee7bc-839b-4fad-a1cc-daff570c0d5c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b027c44a-88b1-44ab-87c5-9c15611a6658' class='xr-var-data-in' type='checkbox'><label for='data-b027c44a-88b1-44ab-87c5-9c15611a6658' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(&#x27;2019-01-04T03:31:31.024000000&#x27;, dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-8092ce23-9e9d-4a21-bce8-32afe3a821fc' class='xr-section-summary-in' type='checkbox'  ><label for='section-8092ce23-9e9d-4a21-bce8-32afe3a821fc' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-c4afc1e8-b75a-4397-8975-261bb21e323d' class='xr-index-data-in' type='checkbox'/><label for='index-c4afc1e8-b75a-4397-8975-261bb21e323d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-95a4fc58-f8e5-4fdf-a6d6-92ccadfc73f0' class='xr-index-data-in' type='checkbox'/><label for='index-95a4fc58-f8e5-4fdf-a6d6-92ccadfc73f0' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([302005.0, 302015.0, 302025.0, 302035.0, 302045.0, 302055.0, 302065.0,\n", "       302075.0, 302085.0, 302095.0,\n", "       ...\n", "       304905.0, 304915.0, 304925.0, 304935.0, 304945.0, 304955.0, 304965.0,\n", "       304975.0, 304985.0, 304995.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=300))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-46ff1b02-e879-4f1a-a0bb-f6880ed8b9fe' class='xr-index-data-in' type='checkbox'/><label for='index-46ff1b02-e879-4f1a-a0bb-f6880ed8b9fe' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([3998015.0, 3998005.0, 3997995.0, 3997985.0, 3997975.0, 3997965.0,\n", "       3997955.0, 3997945.0, 3997935.0, 3997925.0, 3997915.0, 3997905.0,\n", "       3997895.0, 3997885.0, 3997875.0, 3997865.0, 3997855.0, 3997845.0,\n", "       3997835.0, 3997825.0, 3997815.0, 3997805.0, 3997795.0, 3997785.0,\n", "       3997775.0, 3997765.0, 3997755.0, 3997745.0, 3997735.0, 3997725.0,\n", "       3997715.0, 3997705.0, 3997695.0, 3997685.0, 3997675.0, 3997665.0,\n", "       3997655.0, 3997645.0, 3997635.0, 3997625.0, 3997615.0, 3997605.0,\n", "       3997595.0, 3997585.0, 3997575.0, 3997565.0, 3997555.0, 3997545.0,\n", "       3997535.0, 3997525.0, 3997515.0, 3997505.0, 3997495.0, 3997485.0,\n", "       3997475.0, 3997465.0, 3997455.0, 3997445.0, 3997435.0, 3997425.0,\n", "       3997415.0, 3997405.0, 3997395.0, 3997385.0, 3997375.0, 3997365.0,\n", "       3997355.0, 3997345.0, 3997335.0, 3997325.0, 3997315.0, 3997305.0,\n", "       3997295.0, 3997285.0, 3997275.0, 3997265.0, 3997255.0, 3997245.0,\n", "       3997235.0, 3997225.0, 3997215.0, 3997205.0, 3997195.0, 3997185.0,\n", "       3997175.0, 3997165.0, 3997155.0, 3997145.0, 3997135.0, 3997125.0,\n", "       3997115.0, 3997105.0, 3997095.0, 3997085.0, 3997075.0, 3997065.0,\n", "       3997055.0, 3997045.0, 3997035.0, 3997025.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-d309ce80-f8a4-48d6-8223-ab9334b2b955' class='xr-section-summary-in' type='checkbox'  checked><label for='section-d309ce80-f8a4-48d6-8223-ab9334b2b955' class='xr-section-summary' >Attributes: <span>(9)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (band: 4, y: 100, x: 300)>\n", "dask.array<getitem, shape=(4, 100, 300), dtype=uint16, chunksize=(4, 100, 300), chunktype=numpy.ndarray>\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3.02e+05 3.02e+05 3.02e+05 ... 3.05e+05 3.05e+05\n", "  * y            (y) float64 3.998e+06 3.998e+06 ... 3.997e+06 3.997e+06\n", "    spatial_ref  int64 0\n", "    datetime     datetime64[ns] 2019-01-04T03:31:31.024000\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["xa[1,:,200:300,200:500]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190119T033049_N0207_R018_T49SCV_20190119T060700.SAFE\n"]}], "source": ["print(pathlib.Path(select.s2list.iloc[0]))"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["import rasterio as rio"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["src = base.load_s2l1c_dir('/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190106T032129_N0207_R118_T49SCV_20190106T073547.SAFE',group=['10m'])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["a=src['EPSG_32649'][1]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (y: 10980, x: 10980)&gt;\n", "[120560400 values with dtype=uint16]\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-c919cb91-525a-4aff-a6a7-0006f1409be7' class='xr-array-in' type='checkbox' checked><label for='section-c919cb91-525a-4aff-a6a7-0006f1409be7' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>...</span></div><div class='xr-array-data'><pre>[120560400 values with dtype=uint16]</pre></div></div></li><li class='xr-section-item'><input id='section-960356f5-75ea-4423-bf80-2c66931a7e82' class='xr-section-summary-in' type='checkbox'  checked><label for='section-960356f5-75ea-4423-bf80-2c66931a7e82' class='xr-section-summary' >Coordinates: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>band</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>2</div><input id='attrs-21138366-b46a-4954-ba38-3c80b3abaaa2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-21138366-b46a-4954-ba38-3c80b3abaaa2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7b35f191-97ca-4321-896d-d5ca9fbc47ec' class='xr-var-data-in' type='checkbox'><label for='data-7b35f191-97ca-4321-896d-d5ca9fbc47ec' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(2)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-ae63d970-465a-4f47-9085-756e58f841dc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ae63d970-465a-4f47-9085-756e58f841dc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b78ccb10-7e4e-4175-9050-1b0066a4eaaf' class='xr-var-data-in' type='checkbox'><label for='data-b78ccb10-7e4e-4175-9050-1b0066a4eaaf' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-632d6c74-f9b7-4efa-9bf6-1d413e77e61b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-632d6c74-f9b7-4efa-9bf6-1d413e77e61b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0d33c7c3-d430-4dd4-9909-319f5568c27a' class='xr-var-data-in' type='checkbox'><label for='data-0d33c7c3-d430-4dd4-9909-319f5568c27a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-558e6588-8329-436c-adc0-0ff783a90411' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-558e6588-8329-436c-adc0-0ff783a90411' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7240d36a-c5df-4236-a1ca-d2562160f4b9' class='xr-var-data-in' type='checkbox'><label for='data-7240d36a-c5df-4236-a1ca-d2562160f4b9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-42490846-85b3-41a9-94ee-c43299cd8165' class='xr-section-summary-in' type='checkbox'  ><label for='section-42490846-85b3-41a9-94ee-c43299cd8165' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-92f6df7c-3031-43fa-9ab9-278aa697df81' class='xr-index-data-in' type='checkbox'/><label for='index-92f6df7c-3031-43fa-9ab9-278aa697df81' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-4df72b98-83b6-4b81-b2d1-460ba9f19963' class='xr-index-data-in' type='checkbox'/><label for='index-4df72b98-83b6-4b81-b2d1-460ba9f19963' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-877f344c-9ac0-48fa-bf2b-e08e53881ded' class='xr-section-summary-in' type='checkbox'  ><label for='section-877f344c-9ac0-48fa-bf2b-e08e53881ded' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE :</span></dt><dd>1512.79</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (y: 10980, x: 10980)>\n", "[120560400 values with dtype=uint16]\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["np.stack"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (y: 10980, x: 10980)&gt;\n", "array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-f26a7504-6331-4a2e-a0e4-f45619edb721' class='xr-array-in' type='checkbox' checked><label for='section-f26a7504-6331-4a2e-a0e4-f45619edb721' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>0 0 0 0 0 0 0 0 0 0 ... 4261 4422 4531 4577 4581 4599 4604 4543 4514</span></div><div class='xr-array-data'><pre>array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)</pre></div></div></li><li class='xr-section-item'><input id='section-8962d31e-b790-4069-9c9e-8e2fd6fd3fe1' class='xr-section-summary-in' type='checkbox'  checked><label for='section-8962d31e-b790-4069-9c9e-8e2fd6fd3fe1' class='xr-section-summary' >Coordinates: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>band</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>2</div><input id='attrs-8b38758f-8a8d-4a3d-9466-34531ceb8e94' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8b38758f-8a8d-4a3d-9466-34531ceb8e94' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-92ba673b-6060-4478-8126-2c6be533a3aa' class='xr-var-data-in' type='checkbox'><label for='data-92ba673b-6060-4478-8126-2c6be533a3aa' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(2)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-be231033-8235-4679-b8fb-01bfb16f7f51' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-be231033-8235-4679-b8fb-01bfb16f7f51' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ae0b39bf-61ad-4896-afc1-69eab1083264' class='xr-var-data-in' type='checkbox'><label for='data-ae0b39bf-61ad-4896-afc1-69eab1083264' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-86cfd30f-f803-4c5b-8e73-aceb9dad2b7f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-86cfd30f-f803-4c5b-8e73-aceb9dad2b7f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-eaf76111-ba76-4ebf-8d31-05ffea9d21f0' class='xr-var-data-in' type='checkbox'><label for='data-eaf76111-ba76-4ebf-8d31-05ffea9d21f0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-95e42c14-f4f2-4d97-a8be-75d2418ece2f' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-95e42c14-f4f2-4d97-a8be-75d2418ece2f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9b085236-ed7c-4cec-a96c-39dc4066e51d' class='xr-var-data-in' type='checkbox'><label for='data-9b085236-ed7c-4cec-a96c-39dc4066e51d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-44af2c4b-fde2-4c62-94d8-9b77dff0df49' class='xr-section-summary-in' type='checkbox'  ><label for='section-44af2c4b-fde2-4c62-94d8-9b77dff0df49' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-3c9ee0bd-d135-43f8-82c1-8867cd9cf9f4' class='xr-index-data-in' type='checkbox'/><label for='index-3c9ee0bd-d135-43f8-82c1-8867cd9cf9f4' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-29d7385d-11f3-4414-8b43-cefb5f5945b7' class='xr-index-data-in' type='checkbox'/><label for='index-29d7385d-11f3-4414-8b43-cefb5f5945b7' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-11e2a07a-6410-4d95-a2a9-6718b7d06d9b' class='xr-section-summary-in' type='checkbox'  ><label for='section-11e2a07a-6410-4d95-a2a9-6718b7d06d9b' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE :</span></dt><dd>1512.79</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (y: 10980, x: 10980)>\n", "array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a.compute()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}