{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "from os import path\n", "sys.path.append(path.abspath('../series_cloud_research'))\n", "import utils.base as base\n", "import pathlib\n", "import random\n", "import pandas as pd\n", "import rioxarray as riox\n", "random.seed(12)\n", "from tqdm import tqdm\n", "# 1月单独做的时候用的seed=10"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["root = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfv/image')\n", "oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/img')\n", "p_size = 2040\n", "edge_size = 24\n", "half_tr = 8"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["flist = list(root.rglob('*.tif'))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对于每个格网，每个月，取出10个图像块。\n", "什么样的分布是合理的呢？一般都有这些指标：\n", "1. 空间上分布均匀——覆盖多种地物类型，对于局部来说，可以忽略，随机即可。\n", "2. 多种云覆盖情况：云量多/少，阴影多/少，云的大小"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["GF1_WFV1_E82.0_N7.8_20150102_L2A0000566561.tif\n", "10796 8692\n", "10940 5752\n", "2360 6276\n", "200 6160\n", "7928 4512\n", "10564 13300\n", "7564 11336\n", "9876 3752\n", "9168 48\n", "10856 10248\n", "2404 7232\n", "6044 2680\n", "5588 14864\n", "3468 984\n", "9464 13436\n", "3292 1244\n", "8436 11276\n", "13444 5544\n", "11188 6652\n", "13096 13824\n", "1452 328\n", "1016 13452\n", "10868 8360\n", "3680 1512\n", "6964 7292\n", "1864 10848\n", "6956 2236\n", "8860 5144\n", "10204 9168\n", "2700 11440\n", "864 9116\n", "2828 8332\n", "13388 1392\n", "6584 9976\n", "6884 10968\n", "9788 7716\n", "7832 10004\n", "6324 8888\n", "520 10520\n", "11924 1376\n", "3164 12920\n", "10896 4308\n", "5860 5972\n", "11328 6340\n", "11060 5100\n", "1888 4156\n", "3872 5516\n", "5976 6104\n", "8400 9412\n", "13296 12556\n", "8216 2960\n", "484 6264\n", "13500 14096\n", "7068 556\n", "8524 444\n", "3660 11092\n", "7020 736\n", "6384 3372\n", "12668 9972\n", "1704 12364\n", "9032 3636\n", "2940 1300\n", "11424 4508\n", "624 7124\n", "4552 8188\n", "5660 9888\n", "10456 11916\n", "876 8444\n", "7548 6056\n", "3444 5588\n", "4640 7488\n", "7824 11440\n", "7904 3952\n", "2768 7636\n", "9016 5988\n", "3004 3124\n", "12580 12284\n", "3624 14964\n", "9964 132\n", "4416 15020\n", "12648 13152\n", "5620 2964\n", "3980 13048\n", "164 8236\n", "676 4076\n", "10164 1924\n", "5936 8976\n", "8272 2560\n", "412 5304\n", "12924 10976\n", "4600 10500\n", "1956 3784\n", "12868 8660\n", "8252 7844\n", "8048 10296\n", "5816 4324\n", "11236 7312\n", "12128 2680\n", "7376 4796\n", "10128 14328\n", "GF1_WFV1_E88.6_N28.0_20141208_L2A0000845193.tif\n", "776 4580\n", "5428 14148\n", "8464 10512\n", "11424 7316\n", "11568 13000\n", "3528 12048\n", "4692 8556\n", "4200 488\n", "11216 84\n", "11396 468\n", "2180 9164\n", "4376 6908\n", "2288 3976\n", "3304 7152\n", "7752 984\n", "9196 9364\n", "10256 11800\n", "4248 12480\n", "664 6864\n", "7088 9664\n", "1544 6652\n", "12952 3284\n", "9528 9148\n", "5716 6060\n", "10476 12076\n", "6332 9252\n", "2660 304\n", "11308 11656\n", "2568 528\n", "13092 644\n", "1216 6828\n", "8788 3300\n", "12952 6240\n", "13004 1632\n", "11660 5808\n", "3116 5136\n", "4456 13016\n", "11312 11272\n", "8896 8080\n", "10216 10036\n", "2064 3496\n", "8776 9836\n", "11500 11148\n", "6480 9536\n", "4048 1196\n", "6688 5852\n", "5324 1372\n", "11248 5468\n", "2344 11272\n", "1504 11832\n", "5140 5112\n", "11420 2968\n", "12468 11136\n", "10824 10460\n", "7164 6012\n", "2904 508\n", "3448 8016\n", "10228 7292\n", "7976 2960\n", "952 10956\n", "9756 5612\n", "7508 3928\n", "5388 1608\n", "3696 148\n", "5924 6028\n", "4604 13928\n", "3320 11300\n", "7108 4872\n", "3632 8484\n", "2424 8232\n", "11460 5468\n", "2680 8224\n", "7636 6392\n", "9960 8228\n", "11588 4116\n", "7560 7552\n", "12892 14028\n", "828 13648\n", "12676 3748\n", "6700 8836\n", "11472 12496\n", "12996 6500\n", "8564 11056\n", "7228 10888\n", "880 1764\n", "9096 9368\n", "11176 5136\n", "5760 12508\n", "12020 300\n", "5300 12720\n", "8212 5936\n", "10384 5056\n", "2812 2248\n", "7840 3720\n", "13144 8940\n", "2224 4148\n", "8124 776\n", "7052 13188\n", "6000 4700\n", "6716 10304\n", "GF1_WFV1_E99.4_N36.3_20140716_L2A0000278923.tif\n", "9832 4656\n", "6396 13340\n", "12956 14724\n", "9972 5280\n", "2452 2084\n", "4272 10524\n", "556 13828\n", "10808 9992\n", "13064 12872\n", "6224 6780\n", "6968 10144\n", "10812 4336\n", "5588 6688\n", "12616 6244\n", "8092 6324\n", "6568 13372\n", "13248 3016\n", "2804 12944\n", "3384 13716\n", "4196 1820\n", "4412 8588\n", "888 14248\n", "10720 14332\n", "12180 13476\n", "11744 5900\n", "12060 5028\n", "12652 2272\n", "5120 13672\n", "3484 12292\n", "424 9124\n", "5076 720\n", "236 13296\n", "2824 3224\n", "2696 3248\n", "11752 13272\n", "4880 3136\n", "3036 12696\n", "5132 8972\n", "376 9964\n", "1700 5996\n", "11244 8184\n", "8808 3304\n", "7368 1628\n", "8664 124\n", "10696 2096\n", "13140 6456\n", "8300 7992\n", "10436 4828\n", "7680 5852\n", "10412 13792\n", "6388 13996\n", "6820 14648\n", "8488 484\n", "11736 13864\n", "4584 360\n", "2564 8076\n", "12428 14040\n", "6024 11332\n", "1712 13840\n", "11204 13068\n", "7016 9012\n", "6672 9980\n", "6164 12372\n", "1200 13768\n", "2140 5732\n", "1044 3820\n", "1792 6952\n", "1404 13224\n", "948 13732\n", "160 4112\n", "528 1876\n", "11272 11248\n", "9276 10112\n", "11912 10676\n", "12256 13876\n", "480 9740\n", "3756 9640\n", "13184 7812\n", "10332 3408\n", "7572 12420\n", "5540 6960\n", "10668 8920\n", "5200 13132\n", "3984 6424\n", "1452 10772\n", "9964 5548\n", "984 13076\n", "12560 11256\n", "5956 2352\n", "4908 2028\n", "7976 9856\n", "2260 10984\n", "4572 228\n", "3256 3768\n", "2040 7928\n", "7164 12000\n", "5596 13552\n", "6664 12428\n", "5228 5588\n", "9756 6276\n", "GF1_WFV1_E102.0_N28.0_20140302_L2A0000280476.tif\n", "12560 14152\n", "7968 5636\n", "3212 14596\n", "10252 10436\n", "8804 10172\n", "5948 1076\n", "3800 5616\n", "6336 4388\n", "10056 9268\n", "2100 13760\n", "8884 10936\n", "7236 12228\n", "4592 4152\n", "5648 6880\n", "1700 1976\n", "12096 9636\n", "10376 8092\n", "2852 5964\n", "9000 2580\n", "9784 10196\n", "9480 4548\n", "6220 5524\n", "5100 6156\n", "5836 14880\n", "5436 8256\n", "9824 7556\n", "1188 2856\n", "10256 6572\n", "3380 5304\n", "10188 6036\n", "10116 5756\n", "2348 4884\n", "5532 5468\n", "10796 10544\n", "6532 724\n", "8472 5404\n", "10632 8828\n", "1228 13700\n", "11024 8632\n", "1680 14204\n", "4120 2704\n", "7976 1508\n", "11060 5116\n", "2832 13016\n", "7204 3768\n", "4652 1124\n", "11292 3844\n", "3096 3236\n", "4512 10476\n", "2020 10460\n", "3496 9684\n", "3020 7868\n", "12204 5104\n", "7532 2192\n", "7944 6440\n", "7252 7648\n", "6392 5912\n", "4496 11740\n", "12336 3240\n", "3940 9692\n", "8640 12872\n", "11924 7968\n", "8240 10824\n", "10876 11748\n", "10116 13936\n", "11724 5588\n", "5844 6560\n", "5044 2260\n", "424 1240\n", "10996 12964\n", "4808 6416\n", "11032 4772\n", "7900 2504\n", "11328 13732\n", "8548 6916\n", "9480 5172\n", "2960 4268\n", "6948 6800\n", "872 2352\n", "848 7444\n", "1936 8724\n", "5056 4048\n", "2620 3476\n", "2020 12788\n", "9060 6244\n", "8412 8056\n", "5060 9872\n", "1548 3176\n", "2416 13764\n", "3760 6248\n", "8684 14924\n", "4224 8680\n", "6036 12888\n", "3112 2084\n", "3420 12560\n", "9060 1796\n", "12988 3940\n", "4292 584\n", "8768 8972\n", "7288 1800\n", "GF1_WFV1_E104.1_S0.6_20151012_L2A0001096133.tif\n", "8040 10136\n", "8540 3060\n", "9328 13772\n", "10576 968\n", "7164 4956\n", "376 8116\n", "6316 5908\n", "6576 1416\n", "1296 8972\n", "8576 7476\n", "9224 12944\n", "12436 9028\n", "6568 8936\n", "156 13900\n", "8284 6124\n", "6820 14776\n", "2336 420\n", "5516 13064\n", "1212 8668\n", "11128 10220\n", "4560 5128\n", "3624 4932\n", "6696 4608\n", "13124 8464\n", "12256 6472\n", "8612 1344\n", "12752 11156\n", "11120 760\n", "4716 10676\n", "5976 11368\n", "4720 5700\n", "1368 10740\n", "2044 13540\n", "720 1904\n", "13256 2328\n", "1072 9900\n", "2484 2220\n", "12908 8272\n", "8752 2768\n", "2924 9004\n", "13564 14724\n", "5808 3680\n", "13656 6616\n", "340 10848\n", "2392 4652\n", "7528 9348\n", "7488 11508\n", "5368 12336\n", "2772 13520\n", "4144 12324\n", "192 4700\n", "7108 12400\n", "12536 7128\n", "3400 2796\n", "360 10216\n", "5840 5888\n", "4816 1564\n", "10064 8052\n", "6116 6820\n", "5848 5352\n", "680 9300\n", "4980 8848\n", "8704 10212\n", "9112 14844\n", "180 4916\n", "10240 14308\n", "9184 5184\n", "2304 13764\n", "964 5232\n", "3940 8956\n", "10860 12832\n", "11596 14660\n", "3372 14512\n", "2016 1452\n", "1104 10012\n", "2896 12196\n", "7080 13444\n", "11152 5600\n", "4408 12788\n", "760 7188\n", "3608 14956\n", "6148 5208\n", "5548 864\n", "10368 14460\n", "5184 10840\n", "11328 7888\n", "3360 14116\n", "3616 10196\n", "1128 13556\n", "1560 10980\n", "7872 8108\n", "13256 2708\n", "8448 3340\n", "11056 14844\n", "7688 8768\n", "12740 6412\n", "11592 5532\n", "3152 13876\n", "11128 2232\n", "9696 11204\n", "GF1_WFV1_E104.3_N12.9_20140522_L2A0000356326.tif\n", "2784 8708\n", "7748 11772\n", "7356 2552\n", "1900 3328\n", "10672 6760\n", "3516 2904\n", "4224 10248\n", "1180 5396\n", "7716 11232\n", "7408 8488\n", "1508 7132\n", "10756 6440\n", "3304 2788\n", "9376 10672\n", "2956 10672\n", "9480 2384\n", "1400 8892\n", "11512 2756\n", "11872 5336\n", "10280 9640\n", "10884 2812\n", "732 3840\n", "7752 11988\n", "7964 12172\n", "2312 2632\n", "1472 14072\n", "7564 9996\n", "11384 9228\n", "9136 3784\n", "2456 8140\n", "13020 14916\n", "2420 14664\n", "4532 8484\n", "9020 9764\n", "4080 8616\n", "10356 4292\n", "4120 1316\n", "6460 2692\n", "9008 4476\n", "8952 14412\n", "9428 10992\n", "508 7008\n", "9856 700\n", "7404 13644\n", "8548 2084\n", "3300 10348\n", "7152 12612\n", "9144 3020\n", "11036 1044\n", "11036 14888\n", "324 160\n", "2024 12052\n", "8756 2328\n", "6628 4920\n", "9168 3424\n", "1480 7212\n", "10692 11636\n", "8804 13388\n", "1460 8388\n", "2900 13016\n", "4860 1152\n", "13032 2700\n", "6652 440\n", "5500 196\n", "2496 1784\n", "1332 188\n", "4376 12468\n", "6100 4332\n", "5012 11836\n", "5888 1968\n", "10804 9604\n", "2792 7208\n", "9772 13084\n", "12188 1604\n", "11508 7168\n", "13516 12856\n", "6680 6112\n", "4612 14996\n", "5948 1804\n", "1756 5392\n", "4504 3796\n", "11428 5360\n", "4228 12764\n", "12608 7872\n", "7836 5600\n", "88 1980\n", "1416 6580\n", "140 2612\n", "3768 7200\n", "8196 11152\n", "8824 14164\n", "13232 6456\n", "11428 12560\n", "10744 6356\n", "11360 1124\n", "1540 14608\n", "2596 9980\n", "7320 2776\n", "11288 12224\n", "13560 7380\n", "GF1_WFV1_E104.8_N51.3_20150617_L2A0000979951.tif\n", "5140 9048\n", "4904 11944\n", "3892 792\n", "60 14332\n", "3236 5188\n", "8108 6452\n", "9208 5148\n", "12420 11712\n", "2872 15124\n", "2240 13184\n", "1108 6800\n", "2168 1316\n", "10232 5412\n", "6268 120\n", "5240 7596\n", "4540 5844\n", "1264 5100\n", "5560 5652\n", "360 1792\n", "5536 6964\n", "3892 4384\n", "3216 1000\n", "6808 2232\n", "3408 15228\n", "12816 11788\n", "10144 13356\n", "184 12120\n", "3736 4772\n", "1804 864\n", "13056 11236\n", "10692 11868\n", "11616 2372\n", "376 12992\n", "6068 8792\n", "1464 6048\n", "3468 2844\n", "7140 10224\n", "1944 1512\n", "1348 15132\n", "12316 9192\n", "5792 13776\n", "7852 3052\n", "2268 10296\n", "2940 11820\n", "8168 5548\n", "7024 1676\n", "3068 12124\n", "2488 5020\n", "7308 12392\n", "1880 15060\n", "1036 484\n", "7780 964\n", "860 8436\n", "12052 3964\n", "12600 5208\n", "11580 232\n", "5120 2736\n", "8972 6932\n", "10304 6820\n", "1208 5400\n", "2060 688\n", "7992 13880\n", "11092 1660\n", "1004 7756\n", "10524 6940\n", "12772 13460\n", "9524 5096\n", "10644 8684\n", "7916 8180\n", "7032 9496\n", "4008 8992\n", "7824 6660\n", "2188 10316\n", "3244 9632\n", "144 7648\n", "12684 5428\n", "2828 11820\n", "3852 13308\n", "7912 4624\n", "8640 9764\n", "11932 12788\n", "1208 3416\n", "2424 13540\n", "3016 13592\n", "10176 384\n", "2816 8100\n", "9192 13324\n", "10592 7028\n", "3684 9804\n", "11436 6760\n", "10884 7180\n", "2704 2944\n", "11204 14644\n", "5676 14848\n", "9032 8156\n", "5240 11200\n", "2840 15168\n", "7192 2900\n", "372 15220\n", "8856 1716\n", "GF1_WFV1_E115.1_N49.6_20140423_L2A0000314913.tif\n", "4068 12944\n", "9980 4224\n", "11740 6756\n", "9244 4676\n", "7432 5528\n", "10292 7504\n", "7128 10792\n", "1108 11412\n", "10244 112\n", "2020 10496\n", "10864 11572\n", "8588 14220\n", "1952 8804\n", "8216 8892\n", "536 2396\n", "12548 3228\n", "8748 14692\n", "1040 456\n", "13844 2048\n", "7104 6316\n", "7192 10220\n", "512 10068\n", "7344 13864\n", "13104 6316\n", "728 2904\n", "5752 1000\n", "3292 6560\n", "1732 4436\n", "10992 10472\n", "9044 4608\n", "6212 9160\n", "3100 11264\n", "6444 3428\n", "5264 4032\n", "1800 15160\n", "2596 3424\n", "11912 8028\n", "9896 7472\n", "12764 14064\n", "3176 12536\n", "2628 10284\n", "1892 8880\n", "992 1524\n", "12712 9428\n", "2428 7116\n", "12988 12404\n", "12652 6184\n", "6096 2024\n", "8292 6304\n", "9840 13640\n", "9036 8156\n", "10016 10976\n", "7124 948\n", "11144 3652\n", "2392 4380\n", "13308 7956\n", "7352 1972\n", "11152 13248\n", "5448 4704\n", "36 3380\n", "3952 11316\n", "8240 6904\n", "10112 10092\n", "13368 2076\n", "1440 632\n", "6664 14040\n", "472 8452\n", "10624 4504\n", "5048 3800\n", "2460 736\n", "188 10152\n", "13688 14608\n", "11692 7332\n", "12420 9884\n", "10028 7832\n", "736 11304\n", "5692 1652\n", "10404 12376\n", "192 2892\n", "4568 9708\n", "13144 10224\n", "13916 10092\n", "8056 2944\n", "2228 13380\n", "8052 6152\n", "9252 9756\n", "13136 604\n", "1728 13704\n", "7472 5956\n", "5172 12608\n", "13896 15032\n", "6540 3672\n", "13024 9308\n", "6888 14120\n", "12528 11620\n", "11152 5172\n", "1940 3652\n", "4884 7764\n", "2244 13660\n", "12728 6504\n", "GF1_WFV1_E120.2_N53.0_20150705_L2A0000939154.tif\n", "13528 4948\n", "10308 11772\n", "2336 1084\n", "11352 12344\n", "3764 1112\n", "12984 3112\n", "12244 2360\n", "12612 14156\n", "9576 6884\n", "2032 5132\n", "13976 15312\n", "1832 11412\n", "9528 13660\n", "1604 4420\n", "7184 7896\n", "9072 11096\n", "2340 10608\n", "4044 5324\n", "6844 3340\n", "11752 7132\n", "2868 11240\n", "11160 3076\n", "13980 8904\n", "9876 12096\n", "4060 4364\n", "5052 4764\n", "7548 4228\n", "8128 2096\n", "4440 2452\n", "13504 8648\n", "3268 8516\n", "12176 3796\n", "3256 9960\n", "10204 5336\n", "196 204\n", "9472 14320\n", "12316 9912\n", "8296 13272\n", "10308 11632\n", "1952 7552\n", "7520 12860\n", "2876 6136\n", "7780 12208\n", "10080 9836\n", "10736 13260\n", "12128 1976\n", "5020 8916\n", "13444 2288\n", "13404 8288\n", "7020 11860\n", "11796 6652\n", "6808 8008\n", "9460 6052\n", "5340 6032\n", "7588 11644\n", "5072 10752\n", "12252 10068\n", "5724 10076\n", "8588 236\n", "7628 15440\n", "2880 9444\n", "10620 4624\n", "7332 464\n", "5656 8760\n", "8424 11980\n", "2164 6860\n", "10644 8912\n", "12848 6028\n", "13172 2668\n", "2384 11092\n", "10176 12116\n", "5240 12880\n", "1500 8340\n", "9416 7096\n", "3612 2312\n", "4980 11764\n", "9688 4556\n", "8348 6692\n", "144 1444\n", "3700 13424\n", "9168 14204\n", "13840 7012\n", "11172 8180\n", "4216 12120\n", "1716 3504\n", "13776 11360\n", "6208 7864\n", "4612 14788\n", "7300 5832\n", "10048 11512\n", "3624 8504\n", "1764 9676\n", "9808 13632\n", "10796 5332\n", "9204 1420\n", "2640 7332\n", "14124 1988\n", "7064 7844\n", "8904 552\n", "8412 2212\n", "GF1_WFV1_W48.2_S5.6_20140506_L2A0000228109.tif\n", "7712 8148\n", "6964 9372\n", "10484 6312\n", "3716 13968\n", "3968 3740\n", "11964 9172\n", "7644 4944\n", "11980 14292\n", "9980 11936\n", "11108 6684\n", "11988 13532\n", "4352 9428\n", "11068 12844\n", "9560 8432\n", "2008 12496\n", "12380 14832\n", "11408 6660\n", "6404 5620\n", "10276 540\n", "11608 3412\n", "1332 160\n", "4268 1480\n", "2724 8464\n", "11208 3760\n", "8404 10900\n", "13224 1688\n", "664 7944\n", "10100 13248\n", "3740 14768\n", "9064 13712\n", "4588 11224\n", "10652 12592\n", "9024 396\n", "4492 12924\n", "92 13464\n", "6992 5936\n", "9352 3100\n", "8384 200\n", "5576 11396\n", "8348 14724\n", "5196 760\n", "10864 13180\n", "516 5668\n", "12784 10692\n", "6836 7504\n", "8096 348\n", "9824 7548\n", "7128 8580\n", "11992 4144\n", "6296 2500\n", "524 8628\n", "7220 13304\n", "8944 10280\n", "6160 13352\n", "6452 8300\n", "6720 11192\n", "2164 14892\n", "13552 5992\n", "8992 5568\n", "10256 14644\n", "10668 13552\n", "8408 9704\n", "2156 9192\n", "9932 216\n", "6092 9184\n", "3556 980\n", "4296 8648\n", "4932 3684\n", "1788 9976\n", "4616 2608\n", "252 5388\n", "840 12640\n", "8636 9516\n", "1156 2204\n", "4144 6260\n", "376 1300\n", "6784 6016\n", "11716 10996\n", "9544 9692\n", "10712 4124\n", "1596 13616\n", "8348 3628\n", "4144 11840\n", "9844 8664\n", "7632 10676\n", "10332 8460\n", "13136 1220\n", "8244 10156\n", "6952 5976\n", "9140 9296\n", "2776 14976\n", "5304 6412\n", "864 8192\n", "12740 13792\n", "12520 13676\n", "11088 11628\n", "640 8732\n", "10988 7064\n", "8772 12768\n", "4868 10192\n", "GF1_WFV1_W50.9_S17.4_20140506_L2A0000227311.tif\n", "1852 192\n", "5868 9248\n", "1640 10984\n", "13520 5900\n", "5104 13620\n", "4284 12172\n", "11776 14536\n", "868 4808\n", "10864 3740\n", "9216 11572\n", "5608 9048\n", "4900 6892\n", "1464 6596\n", "13720 10616\n", "4540 10664\n", "5700 1676\n", "4192 1320\n", "9144 8612\n", "8124 1796\n", "11580 1324\n", "148 5644\n", "7700 13708\n", "10984 13028\n", "636 1068\n", "8256 7336\n", "12512 68\n", "9052 5232\n", "10564 12212\n", "3192 5444\n", "9404 1080\n", "12316 2076\n", "7796 2624\n", "7420 13992\n", "5652 7828\n", "13096 14776\n", "6196 12252\n", "5440 2848\n", "7136 416\n", "9348 2812\n", "9956 11780\n", "1824 3844\n", "1228 5936\n", "6280 11356\n", "900 1540\n", "5056 7456\n", "10352 7236\n", "3740 15292\n", "12976 1860\n", "4292 7456\n", "5996 4288\n", "6644 14392\n", "13936 14076\n", "808 2984\n", "13312 4816\n", "1572 11752\n", "3300 9124\n", "9500 12312\n", "4192 8456\n", "12044 11004\n", "5140 11276\n", "2412 9936\n", "3196 11008\n", "5792 3616\n", "9292 4004\n", "5932 872\n", "6848 11708\n", "8668 12932\n", "1688 632\n", "11000 14140\n", "1900 2536\n", "8148 8444\n", "7848 4160\n", "9256 5328\n", "8072 8260\n", "12856 13120\n", "4188 13924\n", "3832 8488\n", "13660 4364\n", "3924 1716\n", "12840 15284\n", "1216 15188\n", "10884 3532\n", "8692 3084\n", "8968 5284\n", "5692 5608\n", "6944 92\n", "6384 2144\n", "9680 9968\n", "572 8160\n", "5988 14152\n", "3324 7320\n", "4468 13424\n", "6468 2264\n", "2312 9372\n", "7888 12656\n", "11360 7568\n", "3044 10332\n", "9588 11400\n", "2948 11436\n", "5228 5084\n", "GF1_WFV1_W59.9_S17.4_20140528_L2A0000238651.tif\n", "10592 1768\n", "1824 1808\n", "8960 13272\n", "11612 4836\n", "10252 7840\n", "9220 1840\n", "5768 11280\n", "13736 1716\n", "8460 308\n", "13540 3092\n", "12056 15116\n", "5500 11692\n", "13380 14816\n", "5756 4848\n", "10116 4724\n", "13832 9400\n", "10504 10208\n", "3408 3296\n", "2660 15420\n", "13628 7156\n", "5064 1828\n", "2416 12272\n", "9500 14560\n", "6864 12932\n", "12140 15436\n", "2968 15560\n", "8404 9496\n", "2844 8348\n", "6392 7468\n", "6476 14016\n", "2152 7864\n", "2384 268\n", "5168 500\n", "9020 15260\n", "2268 844\n", "10416 6152\n", "3352 4944\n", "12740 13960\n", "6604 12176\n", "12880 7316\n", "2736 4280\n", "4988 8372\n", "1296 10644\n", "9320 12480\n", "4800 10868\n", "1808 40\n", "4948 2620\n", "1652 4968\n", "8976 9456\n", "8436 12684\n", "1896 12728\n", "12908 8160\n", "13560 6404\n", "5108 2344\n", "4464 14188\n", "1220 8300\n", "5036 4620\n", "8424 3012\n", "7344 2852\n", "164 5452\n", "1628 10908\n", "3884 10644\n", "13632 3212\n", "12804 14144\n", "8104 2200\n", "5960 2456\n", "11036 12404\n", "848 10612\n", "7856 11500\n", "13080 8640\n", "9988 4208\n", "9844 12048\n", "2040 15268\n", "3732 6324\n", "12636 12516\n", "2704 13136\n", "1980 868\n", "8796 13240\n", "2688 4004\n", "8196 2096\n", "11228 820\n", "1444 7580\n", "3228 7448\n", "8904 3072\n", "6232 11700\n", "1856 3064\n", "11276 15236\n", "2380 5780\n", "5476 3368\n", "392 10620\n", "1168 13760\n", "8784 2900\n", "5828 12780\n", "6104 6720\n", "13284 4236\n", "2660 12576\n", "2512 12544\n", "10420 11172\n", "528 3644\n", "3764 7284\n", "GF1_WFV1_W62.5_S10.6_20140610_L2A0000253147.tif\n", "13384 12408\n", "2568 10620\n", "4576 1468\n", "5760 10204\n", "13312 10452\n", "908 8956\n", "10624 14932\n", "11592 11276\n", "8484 11400\n", "9080 15132\n", "220 5336\n", "12340 13156\n", "11864 8268\n", "12124 8104\n", "1124 8812\n", "11380 13388\n", "6176 2148\n", "1528 14896\n", "4300 4372\n", "5656 7652\n", "1904 5056\n", "2712 1856\n", "11572 4792\n", "13532 10060\n", "10720 10488\n", "5040 12268\n", "7232 2516\n", "528 4508\n", "1780 13632\n", "2696 8616\n", "1116 12128\n", "7040 7584\n", "13272 6904\n", "2960 564\n", "9964 13808\n", "28 14848\n", "3456 11740\n", "12028 3564\n", "4308 10340\n", "6656 11164\n", "4640 8644\n", "6192 14208\n", "4328 13748\n", "10032 9152\n", "792 2904\n", "396 9552\n", "728 8964\n", "5016 10672\n", "9488 14440\n", "7952 1400\n", "2436 15480\n", "9156 14724\n", "2144 12552\n", "1116 8392\n", "5488 6060\n", "4240 5460\n", "12840 14032\n", "9264 13944\n", "10516 10104\n", "4584 14412\n", "6452 6404\n", "1888 14700\n", "4728 3500\n", "7028 10992\n", "4800 5536\n", "9680 10332\n", "3916 14944\n", "6128 13316\n", "11416 8624\n", "7840 2580\n", "6436 872\n", "2936 1180\n", "10920 13464\n", "2172 12820\n", "11496 12412\n", "11864 10024\n", "2316 1184\n", "9524 8576\n", "172 10872\n", "544 12684\n", "10176 13228\n", "144 6788\n", "5800 14272\n", "1136 5772\n", "2312 3388\n", "5108 11164\n", "4740 2548\n", "1508 5468\n", "1700 10156\n", "4984 2988\n", "4140 3316\n", "2808 11736\n", "556 7216\n", "6316 11572\n", "7732 3448\n", "6624 9344\n", "7808 14028\n", "10180 4720\n", "188 13688\n", "5992 2380\n", "GF1_WFV1_W83.1_N41.3_20140820_L2A0000320017.tif\n", "4936 10924\n", "2544 704\n", "9692 4016\n", "5400 1200\n", "696 3252\n", "8700 5768\n", "13324 5328\n", "13348 8028\n", "2976 4072\n", "4520 3640\n", "7000 4216\n", "10940 4932\n", "11856 3168\n", "4480 8400\n", "4624 11812\n", "7164 4152\n", "5616 4120\n", "11960 2632\n", "11600 10856\n", "8644 824\n", "13088 7624\n", "12608 5596\n", "12528 3104\n", "12048 4544\n", "6516 11260\n", "9116 3340\n", "9436 4144\n", "844 6932\n", "5916 9256\n", "6940 4212\n", "8632 9088\n", "3100 7004\n", "3612 6240\n", "10620 8400\n", "5252 7968\n", "616 8904\n", "3976 11584\n", "6924 1792\n", "10136 8380\n", "1144 10968\n", "8128 1316\n", "2624 3400\n", "488 8692\n", "5064 4360\n", "5524 9172\n", "11064 11324\n", "1484 252\n", "3420 9832\n", "13516 3932\n", "13116 940\n", "7992 1636\n", "5312 5596\n", "8324 5944\n", "4420 11132\n", "13580 7376\n", "10420 2472\n", "5464 2128\n", "3644 6632\n", "1064 11456\n", "1132 9880\n", "9400 14196\n", "7452 6076\n", "6044 3640\n", "10984 14160\n", "2720 3796\n", "12016 6660\n", "8772 14572\n", "424 4060\n", "5812 13664\n", "3284 6684\n", "7356 12700\n", "11968 12364\n", "9220 13904\n", "6192 12072\n", "12132 11984\n", "1820 14836\n", "2268 5996\n", "3684 13932\n", "2500 3132\n", "6524 9592\n", "10076 9544\n", "12212 12428\n", "13660 2976\n", "3088 6124\n", "10800 9216\n", "11676 13212\n", "10308 3920\n", "12244 8868\n", "1884 12232\n", "2388 11304\n", "8064 4216\n", "7716 1648\n", "976 5920\n", "1948 3192\n", "9216 8644\n", "4644 792\n", "9764 12068\n", "3276 3196\n", "3384 4492\n", "756 14216\n", "GF1_WFV1_W85.7_N36.3_20140522_L2A0000356325.tif\n", "536 12668\n", "2648 6012\n", "6064 13840\n", "11968 964\n", "11400 8680\n", "1052 12436\n", "12240 10292\n", "7816 7260\n", "10684 9012\n", "11680 4288\n", "7952 12864\n", "4436 9016\n", "10376 14548\n", "13224 9772\n", "1616 332\n", "10556 10060\n", "6888 11716\n", "2676 6804\n", "5392 7088\n", "1156 7112\n", "13224 9192\n", "9540 7156\n", "8732 14236\n", "12596 13592\n", "7484 6508\n", "568 4636\n", "9648 9140\n", "3836 8800\n", "9956 4220\n", "1652 1704\n", "13112 9936\n", "8752 3000\n", "372 13440\n", "1776 1564\n", "10532 2372\n", "8980 9244\n", "10608 3932\n", "10772 6460\n", "6084 13532\n", "12484 1580\n", "12724 14520\n", "3928 10672\n", "4284 14112\n", "12648 6612\n", "1552 1904\n", "932 8280\n", "5676 3928\n", "7980 10400\n", "9980 6652\n", "260 2668\n", "12140 12332\n", "1412 14624\n", "2520 7084\n", "3508 2536\n", "8212 6796\n", "2700 9692\n", "2332 7168\n", "1228 3296\n", "44 3592\n", "364 4132\n", "2352 9988\n", "1452 14428\n", "5064 6512\n", "3232 9028\n", "2388 7956\n", "8120 6244\n", "7908 14300\n", "2952 11132\n", "8792 6840\n", "9572 13372\n", "2616 4080\n", "2036 1664\n", "10388 3856\n", "5468 1164\n", "5064 4180\n", "7020 1224\n", "9920 1804\n", "7384 13132\n", "4316 8244\n", "704 2020\n", "5496 10712\n", "2368 8232\n", "3004 6584\n", "12344 5284\n", "8276 13840\n", "4656 8520\n", "8004 5400\n", "1512 12300\n", "1756 14480\n", "9088 9436\n", "12916 8732\n", "11892 12836\n", "4832 5888\n", "356 5332\n", "11244 7684\n", "2064 5572\n", "4084 14484\n", "12012 7224\n", "13084 12184\n", "3636 13648\n", "GF1_WFV1_W90.3_N36.3_20140821_L2A0000320015.tif\n", "8492 10096\n", "8332 12496\n", "1044 2204\n", "4636 11532\n", "3360 13160\n", "5332 9040\n", "12840 7976\n", "1280 5016\n", "11792 6192\n", "1700 13892\n", "12312 9772\n", "2736 6604\n", "6868 1600\n", "1208 3408\n", "12780 4512\n", "5260 14328\n", "4756 7828\n", "7928 5424\n", "6896 2832\n", "3792 12044\n", "11620 4108\n", "5552 4408\n", "11152 448\n", "9832 6004\n", "10940 6988\n", "2688 10200\n", "12808 3520\n", "1712 2780\n", "1748 11916\n", "9884 10084\n", "11660 11320\n", "3440 14036\n", "4208 10980\n", "8344 4316\n", "3796 13740\n", "136 2436\n", "4736 11676\n", "7380 8272\n", "8976 1540\n", "7648 6396\n", "10384 10540\n", "3576 8420\n", "9396 728\n", "12524 4728\n", "608 3820\n", "1700 6260\n", "2856 8020\n", "10924 252\n", "7528 4720\n", "10008 8636\n", "5112 14004\n", "10496 10692\n", "12036 6944\n", "9548 7180\n", "5828 364\n", "9376 11736\n", "208 8340\n", "9184 300\n", "2160 10872\n", "628 4648\n", "9380 12392\n", "2808 6612\n", "9420 3616\n", "1760 832\n", "11300 960\n", "10876 8024\n", "7816 4244\n", "10892 1736\n", "10280 7824\n", "1708 13680\n", "10808 1900\n", "10840 11860\n", "2292 3452\n", "7836 14052\n", "4164 8932\n", "8608 5924\n", "6032 6416\n", "5656 3788\n", "11880 10984\n", "7984 8124\n", "2340 6772\n", "4588 2248\n", "6240 14020\n", "4600 1140\n", "2656 772\n", "9040 1532\n", "11720 84\n", "8172 11292\n", "1384 3272\n", "2888 8204\n", "7884 13276\n", "212 4828\n", "3344 13792\n", "5408 1484\n", "428 5040\n", "52 13812\n", "2568 4560\n", "11380 13664\n", "1368 12644\n", "9068 3604\n", "GF1_WFV1_W94.4_N41.3_20140425_L2A0000356317.tif\n", "196 9932\n", "1592 10396\n", "4524 1516\n", "1080 4936\n", "11376 10452\n", "5596 1744\n", "496 13064\n", "5496 3180\n", "2748 840\n", "2164 1068\n", "3312 14912\n", "3936 14364\n", "10784 492\n", "11828 14840\n", "10624 11436\n", "2748 14484\n", "5312 6036\n", "556 3388\n", "4428 12952\n", "4808 3760\n", "3100 6964\n", "912 4328\n", "9972 7800\n", "9728 12456\n", "6488 2076\n", "3112 8264\n", "8348 7424\n", "9540 6140\n", "9280 8052\n", "1184 1528\n", "3292 1972\n", "1992 1540\n", "2924 9888\n", "4376 9360\n", "2828 10472\n", "13704 784\n", "4500 4232\n", "9800 1100\n", "6952 9696\n", "4344 14520\n", "2900 9044\n", "9108 6392\n", "8744 12836\n", "3432 9820\n", "9656 11736\n", "2512 11896\n", "11492 4284\n", "12448 1184\n", "4944 4204\n", "3440 10704\n", "12188 12768\n", "2832 1076\n", "13064 9492\n", "8844 5240\n", "7332 12672\n", "3940 948\n", "1920 13124\n", "6800 12624\n", "6564 13536\n", "2148 7968\n", "12836 6456\n", "10976 1552\n", "500 8712\n", "5300 11852\n", "5476 11568\n", "12764 6864\n", "536 5512\n", "11956 4208\n", "6928 13220\n", "1700 14532\n", "6188 8408\n", "4480 4096\n", "11020 1888\n", "4256 112\n", "4024 5048\n", "13320 6532\n", "12820 12668\n", "9652 2256\n", "3624 6168\n", "6368 2896\n", "5516 4076\n", "11548 8300\n", "4868 14280\n", "8276 8844\n", "6856 5548\n", "12396 1068\n", "5472 13732\n", "6544 4784\n", "10500 4824\n", "2556 11164\n", "12332 1560\n", "8764 10748\n", "8436 8432\n", "3828 1988\n", "1100 11828\n", "5864 14608\n", "3500 8896\n", "164 2168\n", "12156 4224\n", "4524 7344\n", "GF1_WFV1_W158.9_N21.3_20160806_L2A0001750182.tif\n", "436 14440\n", "13084 11720\n", "11544 4572\n", "11980 2248\n", "3244 9980\n", "5548 16572\n", "4396 7180\n", "13660 13816\n", "6072 6436\n", "10456 6052\n", "2048 3072\n", "6456 12944\n", "1192 9352\n", "11376 15292\n", "5276 1712\n", "11980 16348\n", "11744 10520\n", "9636 13428\n", "5160 5864\n", "6040 2928\n", "13184 3312\n", "12244 11680\n", "5140 16612\n", "3224 4804\n", "11964 1920\n", "6068 11756\n", "6156 672\n", "4536 13104\n", "9112 9460\n", "4756 9736\n", "6104 3728\n", "10380 9556\n", "11684 15080\n", "1064 9288\n", "8252 5160\n", "9548 16688\n", "1820 1704\n", "4720 11128\n", "7792 13680\n", "3932 4864\n", "4672 10324\n", "11636 5844\n", "10608 7472\n", "9916 10816\n", "11416 5488\n", "4136 8272\n", "8508 7880\n", "3492 9440\n", "10904 15432\n", "7352 7456\n", "1760 8148\n", "12656 1156\n", "4168 2576\n", "11232 8728\n", "11352 11188\n", "6712 9496\n", "1368 4236\n", "2944 11316\n", "420 9016\n", "2888 6660\n", "2692 11996\n", "3368 13132\n", "6388 156\n", "1832 13552\n", "9436 2180\n", "3220 11580\n", "5872 11700\n", "8672 11272\n", "9860 10184\n", "2620 1380\n", "4700 9144\n", "3748 15176\n", "280 312\n", "12436 13648\n", "4784 6316\n", "12632 6116\n", "6156 9560\n", "9548 3568\n", "8504 3716\n", "5616 8300\n", "10768 13720\n", "4960 3604\n", "9920 14464\n", "912 15452\n", "1804 14688\n", "1932 8600\n", "4596 6180\n", "3700 3380\n", "8484 936\n", "12556 6944\n", "5408 2936\n", "2252 14860\n", "760 4784\n", "6888 13160\n", "2404 11404\n", "12152 15816\n", "700 8540\n", "1772 9676\n", "12460 8328\n", "7380 1940\n", "GF1_WFV2_E31.2_S21.1_20140220_L2A0000169218.tif\n", "12036 1168\n", "6184 10864\n", "11696 4452\n", "8520 2304\n", "588 11288\n", "4584 10080\n", "2776 3732\n", "4084 3476\n", "10452 3500\n", "4760 7360\n", "6312 12668\n", "5124 9836\n", "7348 10760\n", "3384 12328\n", "1420 8116\n", "10364 3228\n", "2444 2428\n", "10316 984\n", "1844 12768\n", "6448 12820\n", "5228 2548\n", "10720 372\n", "5276 520\n", "3508 1944\n", "8080 5400\n", "9484 9416\n", "1996 2964\n", "2252 3268\n", "3540 12320\n", "3932 7036\n", "9404 10596\n", "4848 11052\n", "9400 7588\n", "6796 5292\n", "7560 3744\n", "9364 10132\n", "3032 6080\n", "11296 9912\n", "7144 3864\n", "10932 10052\n", "3872 11608\n", "7380 4972\n", "8240 11640\n", "9356 1536\n", "12652 3596\n", "1524 3716\n", "4756 564\n", "3236 4988\n", "1356 1276\n", "4372 5548\n", "7768 10488\n", "9136 5268\n", "9520 392\n", "12144 5884\n", "12884 10472\n", "8176 12640\n", "12180 8816\n", "4044 4060\n", "5516 5680\n", "10368 7452\n", "9296 7024\n", "6348 5404\n", "6560 9452\n", "2596 6712\n", "13192 12532\n", "3756 4824\n", "9856 10988\n", "5780 2708\n", "2592 436\n", "11100 5296\n", "11540 5612\n", "10216 9452\n", "3812 952\n", "12016 4636\n", "7084 8644\n", "1936 3916\n", "1096 2804\n", "3244 7364\n", "11448 936\n", "12728 2544\n", "3508 4412\n", "7000 5744\n", "13136 5508\n", "2020 5372\n", "3236 5388\n", "13216 96\n", "4032 6996\n", "12224 7764\n", "5292 11288\n", "100 4976\n", "3852 5464\n", "10388 10052\n", "4956 464\n", "1148 7332\n", "1320 1868\n", "8984 5568\n", "1844 4612\n", "6996 2512\n", "10592 6216\n", "12680 12484\n", "GF1_WFV2_E77.8_N36.0_20150724_L2A0000971862.tif\n", "3372 4720\n", "12620 11376\n", "11892 3396\n", "888 7376\n", "9252 148\n", "2672 10640\n", "7620 3328\n", "944 7204\n", "816 5232\n", "10032 2328\n", "6732 9384\n", "6716 11652\n", "1028 7048\n", "12228 11392\n", "6716 4096\n", "9780 11748\n", "7488 8692\n", "7400 4916\n", "2140 3536\n", "2000 2448\n", "3480 10940\n", "5744 8664\n", "2288 8036\n", "1296 2624\n", "6572 7964\n", "4496 976\n", "6284 7980\n", "3360 5504\n", "9456 7136\n", "9916 6652\n", "10556 3544\n", "12272 4424\n", "7056 10016\n", "5528 480\n", "4192 11720\n", "6708 4464\n", "8952 11980\n", "7396 9300\n", "8088 3296\n", "8356 2944\n", "7524 40\n", "936 444\n", "8568 6392\n", "1084 6956\n", "5024 2728\n", "3140 7628\n", "9776 6656\n", "8844 6584\n", "6600 10780\n", "3036 11200\n", "10356 11468\n", "4852 4624\n", "8708 6640\n", "4076 2748\n", "8736 936\n", "3460 1764\n", "2728 9008\n", "7408 6720\n", "1736 3568\n", "6428 1724\n", "1772 4040\n", "1636 11852\n", "12500 11812\n", "1352 10388\n", "8100 5340\n", "7296 348\n", "544 7976\n", "12344 11276\n", "2236 4316\n", "10508 7692\n", "4804 10268\n", "880 7376\n", "4748 7180\n", "1176 908\n", "5972 2656\n", "8616 10880\n", "9456 5892\n", "4816 7972\n", "1864 8896\n", "10928 6348\n", "10368 7096\n", "1368 10796\n", "10664 1480\n", "1256 9584\n", "2212 10384\n", "236 11704\n", "12664 2884\n", "5172 4724\n", "5396 7064\n", "2928 7484\n", "9160 10980\n", "3784 11512\n", "3792 1460\n", "4080 7688\n", "11916 11544\n", "11400 6532\n", "2456 1100\n", "10664 11164\n", "5772 11176\n", "9300 3084\n", "GF1_WFV4_W152.9_N21.9_20160806_L2A0001748198.tif\n", "5620 8472\n", "13324 6192\n", "11892 8444\n", "11512 9644\n", "4688 13340\n", "5468 7300\n", "12656 11032\n", "11604 12032\n", "10364 6856\n", "5116 1080\n", "3588 1068\n", "8508 6732\n", "8296 704\n", "204 10916\n", "12540 5292\n", "2760 13208\n", "8008 2676\n", "6192 424\n", "5488 6284\n", "4252 10204\n", "2628 11196\n", "12064 1748\n", "2216 1816\n", "10672 12100\n", "5652 7456\n", "13408 3896\n", "2844 14240\n", "11888 8072\n", "10256 8420\n", "2764 1344\n", "9612 13648\n", "9544 2356\n", "5328 4120\n", "10676 12136\n", "6524 4196\n", "13128 12192\n", "10672 44\n", "13436 1824\n", "13656 10124\n", "12684 4904\n", "11240 12288\n", "388 10872\n", "9080 9212\n", "12004 724\n", "8132 6744\n", "8580 1784\n", "7812 7164\n", "5036 8192\n", "3436 13708\n", "4556 12552\n", "13512 4120\n", "10052 13628\n", "7788 1800\n", "5184 10580\n", "6796 1208\n", "6620 11920\n", "2760 13812\n", "11320 13820\n", "4772 6312\n", "6608 5516\n", "5448 100\n", "8476 1580\n", "3468 9020\n", "2916 13916\n", "13248 12728\n", "10328 5200\n", "12808 12548\n", "11992 10092\n", "4780 12368\n", "9080 8024\n", "48 13700\n", "10116 3336\n", "12444 384\n", "740 13140\n", "11216 1536\n", "6224 13656\n", "3004 7924\n", "8104 12000\n", "12080 6164\n", "13432 10024\n", "7480 7972\n", "1956 1384\n", "11720 12924\n", "6584 1292\n", "10276 4628\n", "9204 5416\n", "7840 8600\n", "10344 11124\n", "11604 1860\n", "9316 6176\n", "9772 5864\n", "5572 9380\n", "240 348\n", "1160 2024\n", "13492 10088\n", "7480 2232\n", "12116 4480\n", "7884 8048\n", "11388 12380\n", "7880 9992\n", "GF1_WFV2_E80.0_N22.6_20140314_L2A0000356282.tif\n", "9032 4980\n", "5124 5268\n", "7988 5876\n", "3848 1148\n", "7196 3692\n", "6380 10980\n", "13056 12580\n", "6816 12208\n", "11364 9072\n", "9364 4212\n", "6824 4564\n", "5376 5932\n", "10236 10624\n", "4092 5400\n", "6148 5220\n", "1816 6892\n", "7700 8300\n", "964 10428\n", "4172 5564\n", "5800 1144\n", "7604 964\n", "1860 10492\n", "6024 9768\n", "604 10132\n", "7432 440\n", "1680 6788\n", "2568 4160\n", "3916 10184\n", "6512 12748\n", "3936 10640\n", "1924 652\n", "7408 1584\n", "2716 11144\n", "2944 5352\n", "9596 5052\n", "228 2832\n", "5628 7564\n", "12584 10660\n", "7228 7160\n", "11400 172\n", "12432 5120\n", "10036 10492\n", "12576 4908\n", "11332 11708\n", "9400 7152\n", "196 3308\n", "400 7692\n", "4416 1156\n", "6460 5936\n", "552 4784\n", "3536 8136\n", "12144 156\n", "11512 1392\n", "12368 12112\n", "10156 3480\n", "7760 5248\n", "680 7392\n", "228 4632\n", "6772 4680\n", "11996 9332\n", "11044 1760\n", "8984 1740\n", "4272 8488\n", "12516 10260\n", "10956 2112\n", "4240 2660\n", "3112 4428\n", "11340 12160\n", "10544 9776\n", "7828 6748\n", "8860 484\n", "1328 812\n", "11252 508\n", "13168 1484\n", "1288 5756\n", "8720 236\n", "6940 3940\n", "6988 1652\n", "10680 5108\n", "9228 2688\n", "224 12180\n", "2340 5096\n", "10812 1688\n", "316 10856\n", "8064 6456\n", "12284 476\n", "2892 7104\n", "11744 4072\n", "3132 10936\n", "10192 10988\n", "6228 528\n", "732 3408\n", "9180 7392\n", "8128 5616\n", "5704 920\n", "10272 12752\n", "848 7340\n", "10736 11452\n", "1848 12616\n", "8824 776\n", "GF1_WFV2_E100.2_N0.7_20151013_L2A0001098147.tif\n", "8316 180\n", "3324 6940\n", "6348 6640\n", "6456 6276\n", "12568 5020\n", "10308 3296\n", "4024 9260\n", "8576 8696\n", "112 716\n", "1364 3936\n", "1332 11900\n", "9260 8352\n", "4040 11852\n", "11664 3652\n", "4124 11588\n", "12628 6132\n", "7136 8020\n", "4036 2612\n", "256 11812\n", "11960 2324\n", "11616 292\n", "13068 4524\n", "10176 4168\n", "1956 1952\n", "176 8432\n", "7204 4968\n", "8924 4768\n", "5924 9792\n", "10084 5952\n", "1032 5048\n", "10212 8772\n", "828 4476\n", "7840 3540\n", "9540 6144\n", "8480 3616\n", "9056 11508\n", "6920 11164\n", "6892 436\n", "192 8068\n", "10240 10944\n", "10044 772\n", "3112 1644\n", "8852 11204\n", "13084 1048\n", "3908 3964\n", "10036 4764\n", "4892 7480\n", "1672 11004\n", "4648 1408\n", "1788 152\n", "9572 9156\n", "420 944\n", "5068 3136\n", "4996 1416\n", "11848 11500\n", "1408 10540\n", "6124 6624\n", "6176 2816\n", "6268 5008\n", "9592 4348\n", "8068 1024\n", "13052 9516\n", "8756 1072\n", "9984 7872\n", "1320 7152\n", "5440 8220\n", "6644 7404\n", "5720 76\n", "5572 4360\n", "6244 632\n", "948 5608\n", "10328 9224\n", "3372 6996\n", "2544 4268\n", "9964 3324\n", "5672 5544\n", "10124 3352\n", "4808 5044\n", "13112 8300\n", "32 7188\n", "5436 6460\n", "7480 8084\n", "1504 3132\n", "5672 9572\n", "3764 6864\n", "11060 11456\n", "2380 2432\n", "8396 10740\n", "13172 3580\n", "8696 4320\n", "10420 7764\n", "9528 12104\n", "2344 1504\n", "3268 6856\n", "8204 6120\n", "3312 10776\n", "5948 7668\n", "6232 6244\n", "11568 7804\n", "1520 9808\n", "GF1_WFV2_E103.9_N9.2_20140314_L2A0000185484.tif\n", "4752 40\n", "528 8880\n", "4816 4028\n", "852 2424\n", "2876 7252\n", "11560 2988\n", "1836 5436\n", "8004 10364\n", "4440 6148\n", "1088 2608\n", "2496 3364\n", "8444 12268\n", "4356 9660\n", "6004 2900\n", "7160 476\n", "3684 2484\n", "3320 9052\n", "7828 11400\n", "2160 11004\n", "12996 4280\n", "5972 13476\n", "2500 28\n", "11092 12228\n", "3280 4076\n", "3360 5436\n", "1944 2964\n", "6568 4416\n", "10440 5632\n", "6684 12968\n", "8244 2920\n", "916 8608\n", "11900 1904\n", "12344 5520\n", "12468 12620\n", "460 1684\n", "5128 7248\n", "3060 11544\n", "11044 5360\n", "11476 1692\n", "11520 5308\n", "4404 920\n", "9596 12384\n", "6992 5872\n", "8924 1512\n", "4352 5044\n", "11556 5012\n", "5428 7956\n", "2008 4480\n", "2496 9068\n", "9184 3428\n", "11704 7944\n", "4888 2788\n", "11068 928\n", "3712 9392\n", "6536 6948\n", "1760 3320\n", "3592 4176\n", "9044 1784\n", "1024 2756\n", "792 3272\n", "9584 7548\n", "2376 2264\n", "4664 10980\n", "8168 9100\n", "4072 3968\n", "4872 12220\n", "12028 13328\n", "4264 12892\n", "8240 11896\n", "4764 11360\n", "9972 6740\n", "3916 13296\n", "7112 2264\n", "6172 11184\n", "4452 5820\n", "8128 8648\n", "9216 6068\n", "8436 3908\n", "7216 3520\n", "1988 4060\n", "1400 10416\n", "10452 4176\n", "12804 2324\n", "3648 13308\n", "3584 9420\n", "2788 6080\n", "4772 11248\n", "3924 8380\n", "9388 2964\n", "9896 7644\n", "12736 11620\n", "13040 13248\n", "8980 7164\n", "2288 8848\n", "5728 10780\n", "12716 2112\n", "13040 12252\n", "8844 3832\n", "5128 13136\n", "11960 4636\n", "GF1_WFV2_E105.1_S4.3_20151012_L2A0001096136.tif\n", "1796 12180\n", "7624 6256\n", "3020 6988\n", "220 7652\n", "7624 8044\n", "6412 8940\n", "2552 6036\n", "12180 10688\n", "13100 8828\n", "9472 4268\n", "9340 3960\n", "1380 3072\n", "5708 4464\n", "12292 780\n", "968 11440\n", "7748 1836\n", "5372 4572\n", "2424 2352\n", "416 9520\n", "336 3344\n", "5796 6848\n", "312 3060\n", "11244 7328\n", "11808 7348\n", "7796 10160\n", "12320 356\n", "3672 4076\n", "2992 9024\n", "1188 6772\n", "6588 10340\n", "1168 8468\n", "4588 3112\n", "11212 4024\n", "4520 1440\n", "7240 7972\n", "11040 736\n", "11116 672\n", "3052 3188\n", "2812 4212\n", "13052 7124\n", "5888 3032\n", "5488 8692\n", "4256 1976\n", "9712 5396\n", "11088 528\n", "11908 9744\n", "10900 11692\n", "10056 8176\n", "1700 1760\n", "5944 432\n", "1460 4460\n", "3960 7392\n", "10556 2968\n", "11824 9160\n", "2636 10252\n", "5148 332\n", "6428 11556\n", "7608 5324\n", "11712 2656\n", "5104 6912\n", "9444 4356\n", "3428 3500\n", "10384 2368\n", "10972 5128\n", "4032 2376\n", "10636 5644\n", "4800 3208\n", "4868 9644\n", "12812 4168\n", "8456 7724\n", "528 2288\n", "2280 3532\n", "2212 11432\n", "3096 8140\n", "10324 9764\n", "1796 8152\n", "6072 6348\n", "7852 10240\n", "580 3636\n", "8240 3212\n", "2176 5484\n", "10984 9028\n", "13176 3684\n", "13040 2728\n", "7172 2644\n", "3796 10432\n", "8828 3944\n", "6208 6976\n", "5496 11780\n", "13088 10072\n", "1716 10544\n", "4508 10440\n", "1252 11908\n", "9412 3084\n", "352 11060\n", "1756 100\n", "12952 4332\n", "5516 6300\n", "2476 12304\n", "4148 4540\n", "GF1_WFV2_E105.8_N24.3_20140723_L2A0000305784.tif\n", "1356 8596\n", "9016 7100\n", "324 6136\n", "2676 5284\n", "4904 2400\n", "1312 12012\n", "9812 284\n", "12872 10744\n", "5960 11952\n", "3852 6424\n", "4072 7724\n", "8068 3092\n", "12412 5528\n", "2412 4808\n", "13096 10352\n", "8944 11796\n", "8988 8584\n", "8720 5912\n", "5284 3120\n", "9976 4836\n", "264 4692\n", "6336 7516\n", "5948 9236\n", "11708 6888\n", "848 1680\n", "3052 2868\n", "12132 9688\n", "188 10676\n", "5452 10312\n", "4132 5324\n", "336 520\n", "10460 10180\n", "12376 6608\n", "11116 9868\n", "5868 11464\n", "6072 9400\n", "10872 864\n", "10448 8156\n", "7636 6556\n", "4424 8144\n", "1740 1296\n", "3892 3160\n", "4956 1088\n", "7868 6048\n", "10404 3632\n", "11232 4456\n", "632 12092\n", "4364 11732\n", "7180 7544\n", "3316 7808\n", "3136 3832\n", "2160 12132\n", "10308 8368\n", "2240 28\n", "3492 2968\n", "3464 8840\n", "2708 3812\n", "4144 1852\n", "3632 828\n", "10796 4336\n", "1284 6132\n", "4772 11024\n", "9308 9272\n", "2604 8152\n", "3600 7940\n", "6528 6256\n", "10364 536\n", "5460 4520\n", "3152 5708\n", "9300 2352\n", "5492 12360\n", "632 10600\n", "12224 424\n", "4160 1852\n", "1608 6236\n", "2652 4132\n", "11380 2032\n", "7968 4920\n", "9404 3104\n", "1652 9980\n", "1968 1116\n", "8312 4036\n", "9180 3844\n", "11492 988\n", "8688 8328\n", "1304 3452\n", "11448 12064\n", "2028 4376\n", "1612 6948\n", "4868 10964\n", "11224 2704\n", "6420 2068\n", "9276 4064\n", "3804 11356\n", "6788 2420\n", "7652 3948\n", "12144 8052\n", "4140 1876\n", "3628 10144\n", "12056 5376\n", "GF1_WFV2_E109.1_N39.3_20140428_L2A0000314921.tif\n", "10136 3076\n", "4092 3160\n", "1444 4848\n", "2176 11256\n", "4520 2056\n", "396 6432\n", "11476 8176\n", "12620 8480\n", "4512 4588\n", "3548 11760\n", "6488 3924\n", "6632 10416\n", "12776 9608\n", "13272 7004\n", "12164 12344\n", "8984 7908\n", "6392 11536\n", "612 1144\n", "1756 3860\n", "1456 10300\n", "9556 11608\n", "2564 3144\n", "11516 12056\n", "11260 12308\n", "3640 2780\n", "8040 6836\n", "5652 8592\n", "12404 3620\n", "9492 3716\n", "9212 5504\n", "10688 1696\n", "11348 5512\n", "8312 11380\n", "124 1240\n", "8600 6892\n", "4572 1388\n", "452 12216\n", "8148 6388\n", "7844 11340\n", "6212 9824\n", "11992 8008\n", "9704 2688\n", "11780 10584\n", "6900 5576\n", "12068 4556\n", "9444 7600\n", "8736 1248\n", "6336 3712\n", "6760 1556\n", "3120 6560\n", "376 4532\n", "5760 3012\n", "4228 11552\n", "13404 11700\n", "4700 5624\n", "12144 6444\n", "2068 9888\n", "9040 11328\n", "10956 6564\n", "808 5972\n", "256 2360\n", "10564 10688\n", "2144 2596\n", "8848 5708\n", "4208 9752\n", "8656 11568\n", "5108 2668\n", "700 4524\n", "11468 3244\n", "11012 11696\n", "2928 3860\n", "9212 980\n", "11628 432\n", "8588 7804\n", "9104 10536\n", "11364 492\n", "3348 5880\n", "6128 7012\n", "11372 12624\n", "10656 1716\n", "9092 2452\n", "2464 10720\n", "8656 7232\n", "12808 11984\n", "8504 7908\n", "11360 1220\n", "12152 9972\n", "11184 1376\n", "12812 3364\n", "11316 2120\n", "7864 5156\n", "3616 8172\n", "6272 6572\n", "10792 2548\n", "10116 2084\n", "3212 10204\n", "11928 4084\n", "8148 88\n", "12920 1684\n", "11712 8420\n", "GF1_WFV2_E112.1_N0.7_20151011_L2A0001094728.tif\n", "3532 2136\n", "9452 6564\n", "9288 9644\n", "8452 4716\n", "7016 10976\n", "7440 4180\n", "12208 8268\n", "13120 9668\n", "6520 12408\n", "9128 4028\n", "5920 156\n", "12224 6676\n", "840 6388\n", "7832 2976\n", "6540 56\n", "8396 11952\n", "8464 4624\n", "3372 10072\n", "8048 932\n", "468 5840\n", "6256 972\n", "12284 8916\n", "2628 8612\n", "11388 10364\n", "9072 6036\n", "12120 7888\n", "2528 1284\n", "9720 8552\n", "7728 12164\n", "5432 6908\n", "8364 1040\n", "6844 1164\n", "6060 6384\n", "6384 2588\n", "3300 8576\n", "6800 5968\n", "4844 9780\n", "1576 588\n", "4416 6784\n", "13092 1640\n", "13064 8472\n", "1700 5180\n", "10584 4272\n", "1376 6168\n", "3008 7200\n", "2568 8276\n", "8584 1560\n", "7244 2872\n", "8140 9772\n", "12712 5532\n", "12852 6912\n", "3768 8864\n", "6320 924\n", "5960 8784\n", "9432 7008\n", "7408 9340\n", "10436 6360\n", "13164 11780\n", "12560 7168\n", "2992 8452\n", "2476 4836\n", "40 12264\n", "7568 11764\n", "11988 4820\n", "5208 3624\n", "8008 2160\n", "8236 460\n", "3176 8524\n", "10912 4260\n", "8972 7712\n", "2408 396\n", "9088 9148\n", "1144 10864\n", "7912 3500\n", "12772 9016\n", "404 9816\n", "6656 9016\n", "1956 3112\n", "4692 5420\n", "12240 9676\n", "6104 8680\n", "4560 9448\n", "648 4924\n", "6096 11712\n", "4576 3112\n", "2380 9688\n", "3104 9860\n", "7920 9916\n", "6356 12056\n", "5052 468\n", "2496 8348\n", "1140 5084\n", "6724 7540\n", "9520 5628\n", "764 800\n", "10748 5196\n", "10996 10696\n", "4504 6624\n", "7132 9416\n", "4228 5772\n", "GF1_WFV2_E115.5_N42.6_20140423_L2A0000314906.tif\n", "5204 10512\n", "13460 12416\n", "1100 828\n", "4956 8800\n", "8276 2632\n", "12032 5184\n", "928 8168\n", "5788 8764\n", "12508 10436\n", "11640 2028\n", "9768 12532\n", "2452 5220\n", "388 10696\n", "5828 56\n", "7860 10656\n", "8096 4232\n", "232 11564\n", "6528 6516\n", "2924 5096\n", "4944 216\n", "2376 10936\n", "13284 28\n", "3572 2852\n", "5060 7072\n", "10672 10236\n", "13340 9032\n", "6644 172\n", "10376 2752\n", "9888 10396\n", "8032 10416\n", "10980 816\n", "5828 4796\n", "1040 2740\n", "11584 4100\n", "11872 7856\n", "4796 9432\n", "3552 2968\n", "13192 10128\n", "7480 11704\n", "932 2160\n", "10812 6236\n", "2168 8944\n", "1620 10520\n", "2184 1612\n", "11140 7248\n", "11928 5216\n", "7780 1484\n", "2876 3152\n", "9120 332\n", "12952 11612\n", "10660 1148\n", "3100 7904\n", "12064 3132\n", "8412 6772\n", "6492 7208\n", "5868 660\n", "10624 12680\n", "1064 10648\n", "10792 8724\n", "1220 2420\n", "12872 1972\n", "4520 200\n", "1148 12712\n", "10844 12396\n", "13152 8196\n", "3672 10628\n", "8696 6548\n", "9320 10664\n", "11876 7072\n", "1384 1648\n", "3212 1256\n", "6796 488\n", "7440 8372\n", "5168 3816\n", "5696 12300\n", "760 788\n", "8100 9768\n", "12908 1504\n", "4104 11888\n", "13476 1040\n", "1296 9204\n", "5760 5024\n", "3460 11656\n", "6880 7848\n", "10664 8576\n", "13484 8492\n", "9288 8928\n", "8388 8592\n", "7224 1196\n", "4448 6920\n", "208 1484\n", "13364 824\n", "13140 11292\n", "9144 11344\n", "13076 8320\n", "10324 5528\n", "8008 1984\n", "12332 10060\n", "7424 9504\n", "12944 12204\n", "GF1_WFV2_E127.2_N45.9_20140704_L2A0000309976.tif\n", "12328 1956\n", "9392 7960\n", "1724 3668\n", "13088 7156\n", "12492 8428\n", "7176 11216\n", "2496 13120\n", "6672 6256\n", "2504 1168\n", "9836 232\n", "4476 5732\n", "4804 10796\n", "13600 10044\n", "10132 13192\n", "8800 11372\n", "3128 8536\n", "5180 1284\n", "1972 4620\n", "7348 5808\n", "5476 7396\n", "6320 7148\n", "796 2572\n", "11348 10588\n", "2040 12360\n", "7416 2752\n", "13448 2756\n", "3064 10584\n", "6008 8448\n", "3068 564\n", "10812 11328\n", "2632 8492\n", "3792 7064\n", "11872 9672\n", "8812 2672\n", "1744 6532\n", "4880 7664\n", "13644 4044\n", "8952 6816\n", "7636 12664\n", "2748 12920\n", "12040 6644\n", "104 3884\n", "5456 8952\n", "12968 5372\n", "1924 11688\n", "6392 12880\n", "1252 12788\n", "4520 9048\n", "9448 12684\n", "11876 12164\n", "4732 12076\n", "4020 3204\n", "7364 6472\n", "4104 11504\n", "7912 9016\n", "3920 7424\n", "4584 5280\n", "656 248\n", "312 6532\n", "5516 2380\n", "12556 11960\n", "5152 10340\n", "7560 7224\n", "7280 288\n", "1496 11436\n", "5604 8904\n", "13268 12908\n", "8368 8660\n", "10316 1260\n", "7504 6612\n", "13552 7604\n", "4580 4840\n", "24 9736\n", "4904 12808\n", "2680 7884\n", "7020 11468\n", "3616 13224\n", "9160 12420\n", "2368 492\n", "8840 5284\n", "12948 3444\n", "3456 11688\n", "3084 10456\n", "5740 5108\n", "7056 11648\n", "7368 2732\n", "10252 1236\n", "4052 6440\n", "800 2568\n", "10940 2640\n", "2152 3016\n", "2764 10576\n", "8104 5112\n", "5856 4276\n", "8664 8480\n", "1372 6508\n", "11348 7800\n", "8260 2060\n", "4108 720\n", "2360 9392\n", "GF1_WFV2_W50.1_S24.5_20140510_L2A0000222530.tif\n", "10860 3792\n", "308 7952\n", "1256 9588\n", "5292 5300\n", "9304 9052\n", "7368 7184\n", "8840 7172\n", "12420 9332\n", "3232 7308\n", "12824 9344\n", "12660 7640\n", "1028 2408\n", "2688 972\n", "8700 11280\n", "2564 11892\n", "6876 11412\n", "8500 9500\n", "196 11348\n", "1940 2156\n", "9536 2312\n", "1380 6080\n", "804 3580\n", "3516 1204\n", "10784 11692\n", "740 3780\n", "1708 12436\n", "4164 4164\n", "5404 684\n", "8776 10720\n", "6356 4288\n", "428 1488\n", "12880 4504\n", "7560 7632\n", "7240 10364\n", "592 8624\n", "5892 9336\n", "6320 11368\n", "11744 1532\n", "12304 4272\n", "8168 10128\n", "7952 10576\n", "11932 308\n", "13300 5920\n", "5492 3400\n", "5700 6104\n", "3400 3084\n", "11740 8256\n", "2380 11556\n", "9712 12268\n", "7200 10004\n", "3244 2912\n", "8208 8432\n", "12536 1924\n", "6228 3100\n", "3384 1848\n", "11908 6060\n", "4896 5960\n", "812 2992\n", "2572 1800\n", "12832 10100\n", "9744 7836\n", "1388 552\n", "7100 9384\n", "1028 11816\n", "8668 1208\n", "11124 5216\n", "4104 4168\n", "12244 6464\n", "2592 7972\n", "9212 92\n", "6224 5496\n", "2272 10068\n", "956 3256\n", "504 2548\n", "5772 7428\n", "4004 10572\n", "3052 12608\n", "3840 8072\n", "6200 3428\n", "6832 6316\n", "9992 5116\n", "452 12240\n", "4420 8380\n", "4952 4408\n", "9888 10600\n", "1352 2100\n", "9492 8500\n", "9680 1472\n", "6912 9644\n", "11032 7180\n", "12444 2684\n", "7996 5552\n", "7768 3564\n", "2804 9128\n", "8692 10112\n", "7824 2460\n", "2524 9540\n", "11472 12656\n", "8484 4956\n", "4852 5048\n", "GF1_WFV2_W55.4_S31.1_20140527_L2A0000356274.tif\n", "6432 7392\n", "7888 3944\n", "7804 8864\n", "11908 5248\n", "8832 4376\n", "1128 9928\n", "8984 192\n", "12244 3316\n", "6476 12384\n", "10160 10712\n", "12224 13240\n", "10980 11488\n", "9012 8432\n", "2852 11008\n", "8520 7800\n", "2116 9460\n", "1564 3028\n", "10324 1208\n", "9944 11840\n", "9056 152\n", "11152 8440\n", "2940 5784\n", "3892 6628\n", "13536 2908\n", "10848 3516\n", "1684 13208\n", "276 3464\n", "4856 10912\n", "7492 2736\n", "4592 3320\n", "12724 10504\n", "12732 6400\n", "8860 696\n", "5560 6820\n", "10104 6052\n", "6496 2140\n", "6368 5204\n", "252 644\n", "11284 10548\n", "11296 7604\n", "13292 5192\n", "10108 8652\n", "7488 4368\n", "1908 6148\n", "3708 2840\n", "10240 2724\n", "10552 8588\n", "10284 11788\n", "7340 8532\n", "6968 8844\n", "6164 5832\n", "3896 13236\n", "7900 8976\n", "892 88\n", "12348 4796\n", "6332 4768\n", "5304 2488\n", "7180 920\n", "3500 12868\n", "11596 5968\n", "13332 5188\n", "5152 352\n", "2508 7936\n", "11356 7516\n", "10840 4496\n", "8580 252\n", "4364 3732\n", "11096 9192\n", "7688 2980\n", "2600 9000\n", "716 9788\n", "6576 5172\n", "6548 5444\n", "3144 12796\n", "1144 3952\n", "9944 9064\n", "7020 2940\n", "12860 2236\n", "8148 12816\n", "3584 13192\n", "6620 11544\n", "6852 8612\n", "4720 3872\n", "2168 1276\n", "96 4148\n", "12292 7700\n", "8804 68\n", "13028 244\n", "2320 2932\n", "7124 1568\n", "6672 2400\n", "424 1712\n", "9440 11048\n", "12668 3396\n", "4224 5940\n", "2704 936\n", "2688 1380\n", "9884 9704\n", "1816 8600\n", "92 7496\n", "GF1_WFV2_W56.4_S21.1_20140613_L2A0000253149.tif\n", "3300 536\n", "6940 1584\n", "7400 6972\n", "8744 5540\n", "4956 1348\n", "9080 9144\n", "3268 6164\n", "7200 5832\n", "2104 11980\n", "6096 7456\n", "1128 9020\n", "4836 4472\n", "9352 10268\n", "8984 11824\n", "2800 4296\n", "4376 12504\n", "7032 3068\n", "10392 8700\n", "2200 11336\n", "11500 7128\n", "3940 4352\n", "5240 7400\n", "1568 7368\n", "4108 5380\n", "6732 12244\n", "5532 3252\n", "8412 7988\n", "9508 6396\n", "9840 11516\n", "8612 10720\n", "8600 860\n", "4212 7432\n", "1748 676\n", "3404 12476\n", "3228 1908\n", "144 740\n", "3236 8148\n", "7620 7000\n", "1144 7440\n", "12772 12580\n", "9588 7004\n", "216 8604\n", "6960 10492\n", "5240 10448\n", "2784 3404\n", "11560 8808\n", "7040 352\n", "3656 8676\n", "6012 7476\n", "10736 4412\n", "1032 8744\n", "12880 3792\n", "4460 12076\n", "5116 1328\n", "13088 1320\n", "4928 1432\n", "11156 5300\n", "8056 5684\n", "10452 1768\n", "11156 9000\n", "11508 6540\n", "3696 5608\n", "1376 168\n", "8228 476\n", "6100 5004\n", "11820 9908\n", "5884 1196\n", "12988 6652\n", "12852 9592\n", "12128 10744\n", "13356 2816\n", "1720 4984\n", "7076 10712\n", "2784 5852\n", "2036 5876\n", "11484 6456\n", "10984 4516\n", "10176 1256\n", "8400 3208\n", "1416 3228\n", "7744 8424\n", "5888 2964\n", "10368 12484\n", "8064 5216\n", "10000 11060\n", "6664 1472\n", "1828 3400\n", "11084 4492\n", "10816 5152\n", "104 8344\n", "156 3276\n", "4644 7252\n", "8548 5736\n", "3408 9568\n", "9072 5020\n", "3888 4300\n", "460 800\n", "6088 9692\n", "4296 856\n", "6692 5568\n", "GF1_WFV2_W70.8_N19.2_20140801_L2A0000292230.tif\n", "4548 4004\n", "13276 6772\n", "10172 7308\n", "12016 5648\n", "10348 1824\n", "1684 3248\n", "12500 5956\n", "10160 2820\n", "7044 3096\n", "6820 416\n", "236 7992\n", "13176 1748\n", "7468 9012\n", "3696 12336\n", "9524 8020\n", "3144 6228\n", "11240 5048\n", "6400 3180\n", "2096 2816\n", "8364 948\n", "4876 11212\n", "2720 8904\n", "4404 9052\n", "4432 2032\n", "7028 9448\n", "10372 8288\n", "9320 1900\n", "2772 256\n", "8280 1240\n", "4960 10880\n", "100 5652\n", "9312 2744\n", "11048 720\n", "5060 3380\n", "9620 1864\n", "8928 7748\n", "3868 8552\n", "5516 10636\n", "3848 10828\n", "8552 1804\n", "11000 5456\n", "2332 5456\n", "392 6600\n", "5772 10796\n", "7360 9180\n", "8556 7196\n", "13252 12096\n", "2496 3308\n", "984 5916\n", "3020 6512\n", "12408 5380\n", "4096 4004\n", "4076 5812\n", "2452 3408\n", "7656 7288\n", "5536 10496\n", "2356 6316\n", "728 7404\n", "6860 4948\n", "4680 9392\n", "10508 1540\n", "11676 10328\n", "7144 5156\n", "168 7524\n", "6892 368\n", "9184 11512\n", "3768 12168\n", "7716 88\n", "8504 612\n", "12380 5712\n", "10864 10384\n", "2968 2184\n", "5592 3788\n", "11764 3492\n", "9776 10684\n", "12228 2164\n", "11220 4928\n", "9832 6748\n", "8744 5992\n", "4668 9168\n", "3456 12084\n", "2784 3564\n", "11208 11428\n", "9860 8716\n", "1188 7804\n", "7232 10500\n", "5392 12408\n", "4784 2272\n", "11932 2872\n", "6940 8624\n", "8144 10132\n", "6472 5524\n", "2064 11200\n", "10760 10840\n", "8264 416\n", "7140 11524\n", "5324 1860\n", "5748 10920\n", "5588 1676\n", "10176 6744\n", "GF1_WFV2_W84.0_N34.3_20140522_L2A0000356275.tif\n", "11596 292\n", "5120 164\n", "3004 72\n", "620 1964\n", "9832 5064\n", "6400 6908\n", "11220 5288\n", "8696 4132\n", "10944 7352\n", "9748 592\n", "2412 4132\n", "1600 10480\n", "9992 9172\n", "1800 1840\n", "7996 12188\n", "5228 2952\n", "2256 3620\n", "716 5048\n", "716 9520\n", "6340 8900\n", "336 9532\n", "12632 560\n", "4680 9184\n", "12280 6832\n", "8696 9476\n", "12472 7912\n", "12032 6796\n", "1908 6784\n", "8936 6496\n", "10152 11268\n", "3344 2516\n", "4356 10292\n", "1084 10032\n", "7200 6704\n", "7380 3428\n", "5104 12228\n", "10352 5052\n", "3732 8452\n", "8344 8464\n", "10344 5920\n", "5968 8988\n", "5052 1084\n", "3704 2656\n", "7616 3988\n", "6692 5548\n", "10100 3832\n", "996 10828\n", "5628 980\n", "12352 9612\n", "9824 6228\n", "12492 8896\n", "9028 2560\n", "2128 9056\n", "4604 2272\n", "9024 3312\n", "4048 232\n", "2052 7268\n", "12064 10988\n", "5976 5416\n", "10320 12168\n", "11976 8708\n", "10448 9152\n", "3572 11844\n", "10404 108\n", "6496 10928\n", "3672 3464\n", "2600 4760\n", "3368 9044\n", "7384 1224\n", "10024 11080\n", "1460 6988\n", "580 10024\n", "8768 10016\n", "1892 11100\n", "12444 10492\n", "6508 4596\n", "6072 11420\n", "12908 844\n", "8176 8432\n", "11796 2280\n", "12796 1872\n", "8196 1592\n", "9676 1064\n", "10472 832\n", "5020 4748\n", "752 5768\n", "11564 2440\n", "1076 5324\n", "3092 5676\n", "2988 9644\n", "11824 11212\n", "12928 7492\n", "6616 2060\n", "7660 1840\n", "2080 2316\n", "5264 4196\n", "3900 2836\n", "7360 5120\n", "8248 2308\n", "2308 9452\n", "GF1_WFV2_W86.7_N39.3_20140825_L2A0000320025.tif\n", "8116 9524\n", "3404 6044\n", "12152 732\n", "7928 96\n", "11696 3404\n", "9320 2024\n", "2348 3696\n", "4804 11016\n", "9324 10216\n", "3892 8080\n", "12288 10424\n", "6808 1208\n", "10864 11672\n", "2592 7864\n", "9784 11056\n", "5696 32\n", "7788 2684\n", "8484 7912\n", "12692 1584\n", "11432 3920\n", "9720 7408\n", "8716 5880\n", "2120 5200\n", "10200 2616\n", "10124 7568\n", "824 8756\n", "12080 5580\n", "4408 11756\n", "8556 10540\n", "11652 3956\n", "6176 5576\n", "524 8352\n", "7132 5820\n", "3708 11724\n", "13188 112\n", "12256 7516\n", "10084 7496\n", "10428 3192\n", "5852 424\n", "4784 2536\n", "600 8376\n", "8736 1284\n", "8104 2288\n", "4556 1276\n", "2228 11816\n", "9100 4976\n", "1800 6020\n", "9064 5388\n", "5828 11508\n", "7264 2724\n", "2716 8440\n", "10016 1996\n", "1752 11984\n", "1128 3160\n", "3712 4212\n", "5432 5688\n", "1200 6320\n", "1944 5584\n", "6640 5224\n", "2672 8504\n", "2768 4780\n", "4040 3524\n", "3224 2624\n", "12416 1076\n", "4108 4400\n", "8924 10572\n", "11512 3976\n", "4444 7540\n", "520 10900\n", "9468 2856\n", "9676 5344\n", "10780 2668\n", "10904 3008\n", "2956 5884\n", "13028 7156\n", "8360 8252\n", "1052 8728\n", "12920 8680\n", "2452 12296\n", "11776 824\n", "9632 11524\n", "8264 9908\n", "10476 7276\n", "9192 10260\n", "1324 7344\n", "724 2092\n", "2812 4336\n", "8040 10624\n", "10588 3500\n", "11812 6620\n", "3028 2244\n", "5932 10864\n", "8196 7640\n", "4144 4328\n", "12292 5264\n", "12680 5044\n", "3064 3744\n", "11284 7288\n", "5508 12296\n", "9692 2464\n", "GF1_WFV2_W102.1_N37.6_20140517_L2A0000244678.tif\n", "9404 1580\n", "8388 4680\n", "3244 11672\n", "11076 4220\n", "3648 6976\n", "3132 8784\n", "11556 8088\n", "10748 6132\n", "7484 4764\n", "9984 7840\n", "6708 7456\n", "8560 7304\n", "12200 9480\n", "7912 2340\n", "6348 4756\n", "12792 9560\n", "11052 10804\n", "9404 8352\n", "4380 5824\n", "1576 12152\n", "7980 5080\n", "10172 9276\n", "10056 2380\n", "11268 10528\n", "1144 4516\n", "3108 7620\n", "10556 8492\n", "11988 8416\n", "3360 1704\n", "12420 8944\n", "3268 1892\n", "7500 8200\n", "3372 9360\n", "12120 5044\n", "11324 5888\n", "8832 9428\n", "4704 5180\n", "9476 11316\n", "2520 7540\n", "10860 2944\n", "8904 1504\n", "2232 1376\n", "4692 5772\n", "1156 6320\n", "4520 5012\n", "1732 7740\n", "1020 4224\n", "1756 8052\n", "5304 1020\n", "5896 8956\n", "6460 2088\n", "6464 1356\n", "8536 6440\n", "4880 3292\n", "7664 5196\n", "9784 3772\n", "10924 3504\n", "9852 6252\n", "10408 6104\n", "8772 676\n", "6728 3916\n", "11976 5752\n", "9652 11332\n", "12756 5144\n", "4488 2408\n", "3064 7788\n", "10056 7292\n", "10688 10808\n", "8276 7896\n", "6632 8288\n", "12192 7748\n", "4824 9656\n", "8096 3320\n", "12524 2844\n", "992 10084\n", "3156 928\n", "10424 6992\n", "6568 5468\n", "8388 6432\n", "5292 4440\n", "3820 9672\n", "11080 960\n", "11428 12048\n", "8332 7544\n", "2824 8832\n", "5888 10052\n", "10148 4276\n", "12204 160\n", "4748 3080\n", "5184 9720\n", "2312 632\n", "7192 8372\n", "3508 368\n", "5044 7464\n", "12348 200\n", "6448 9288\n", "9320 2168\n", "8020 7092\n", "4520 2408\n", "6476 12208\n", "GF1_WFV3_E87.8_N2.1_20140316_L2A0000184430.tif\n", "9740 11672\n", "4132 4092\n", "9560 9468\n", "7192 7700\n", "10008 3600\n", "10328 980\n", "7500 888\n", "4932 2360\n", "11712 9256\n", "2536 4416\n", "7168 3256\n", "10900 10572\n", "3408 1636\n", "4444 4580\n", "9856 11172\n", "9488 672\n", "776 5724\n", "5948 10240\n", "4876 7120\n", "40 4512\n", "11608 2200\n", "8484 5368\n", "9752 4656\n", "5528 8360\n", "11956 5852\n", "8864 7092\n", "11724 12496\n", "5084 10324\n", "3496 6256\n", "7052 11224\n", "9304 6824\n", "1920 580\n", "2008 1340\n", "5244 7080\n", "12568 9232\n", "6648 5252\n", "10792 10904\n", "3368 11328\n", "10952 4852\n", "8776 4436\n", "10532 1056\n", "9676 7688\n", "13184 12172\n", "6152 10648\n", "7136 9552\n", "4552 1228\n", "11340 1436\n", "8312 8712\n", "12064 6340\n", "10860 916\n", "12120 8104\n", "4584 7148\n", "224 152\n", "10428 10104\n", "2868 3224\n", "9820 9492\n", "8332 7956\n", "1648 9504\n", "5688 5240\n", "11444 9456\n", "12184 680\n", "5168 9344\n", "6196 1800\n", "11448 2116\n", "7228 3440\n", "11988 540\n", "2416 12384\n", "5800 6252\n", "3780 416\n", "7780 11592\n", "12996 4232\n", "7000 12220\n", "3940 6220\n", "12632 8664\n", "12076 8360\n", "5736 12876\n", "6512 11560\n", "6384 6832\n", "6508 10092\n", "6200 8388\n", "6956 2800\n", "5392 6796\n", "10028 12232\n", "1068 12672\n", "12592 8540\n", "1084 12440\n", "5388 852\n", "10912 5648\n", "5520 12144\n", "6772 3476\n", "7600 10056\n", "4484 5212\n", "12708 12212\n", "4564 10692\n", "12300 12428\n", "2868 6120\n", "3660 5704\n", "5404 752\n", "4960 5480\n", "5120 888\n", "GF1_WFV3_E89.3_N35.6_20140702_L2A0000845154.tif\n", "10960 3328\n", "11300 12120\n", "2448 3944\n", "2332 10308\n", "10988 10312\n", "8252 1536\n", "8112 2420\n", "6192 11052\n", "8492 6540\n", "3812 7824\n", "6904 12228\n", "4688 11412\n", "4144 3896\n", "6712 5072\n", "11264 2044\n", "1480 2320\n", "11756 6872\n", "2724 12188\n", "2392 1684\n", "8892 11796\n", "11404 3860\n", "5288 10316\n", "1856 9308\n", "6936 11184\n", "4504 4564\n", "11700 8044\n", "10964 7828\n", "8152 3680\n", "6844 3124\n", "6776 6020\n", "13300 8772\n", "7464 4612\n", "3988 9864\n", "5672 2924\n", "12660 9416\n", "1896 936\n", "4296 9848\n", "1312 11720\n", "12404 2300\n", "520 2108\n", "9044 2568\n", "4508 3180\n", "6008 2892\n", "5868 1708\n", "12944 6316\n", "5304 12176\n", "10984 5744\n", "7984 1136\n", "11472 12336\n", "7452 8876\n", "1896 9136\n", "10592 7252\n", "10968 1444\n", "8328 9580\n", "5920 6868\n", "824 11144\n", "1152 7948\n", "6172 12392\n", "12048 7976\n", "1168 7996\n", "12188 580\n", "13196 9360\n", "1036 5688\n", "10476 3820\n", "2220 8652\n", "6536 11092\n", "12740 7252\n", "11248 12292\n", "3480 5900\n", "3652 7656\n", "804 6972\n", "2380 11088\n", "12316 5456\n", "3752 10596\n", "784 3452\n", "5316 1028\n", "7644 9556\n", "4132 6500\n", "5028 9576\n", "8160 6240\n", "2476 9792\n", "5808 4612\n", "6760 12192\n", "7388 336\n", "5552 5048\n", "192 1932\n", "12200 2952\n", "7892 9584\n", "3496 7712\n", "5808 7348\n", "3628 5060\n", "9756 9620\n", "10368 10960\n", "4844 5992\n", "8072 4064\n", "6916 2496\n", "1096 11736\n", "12336 1912\n", "2140 12108\n", "9188 9424\n", "GF1_WFV3_E99.0_N3.8_20160522_L2A0001599283.tif\n", "8300 10308\n", "9588 7776\n", "268 7540\n", "12644 1812\n", "2184 4072\n", "688 9180\n", "4540 792\n", "4932 8296\n", "5924 8908\n", "5700 11924\n", "4348 7184\n", "3692 11356\n", "612 6700\n", "8472 11984\n", "7472 3132\n", "10484 1244\n", "3204 5440\n", "796 11728\n", "7680 8340\n", "316 8300\n", "6032 10848\n", "5784 11768\n", "12952 4512\n", "5424 4788\n", "6316 9540\n", "836 6160\n", "9092 8904\n", "4436 3704\n", "12692 7524\n", "10776 10216\n", "6760 6148\n", "5240 11756\n", "2172 444\n", "5144 2588\n", "9248 8296\n", "192 9440\n", "12620 2716\n", "6252 9336\n", "10948 9824\n", "12720 10724\n", "1988 6184\n", "11560 9096\n", "5428 1160\n", "4180 6328\n", "10680 5572\n", "11952 11340\n", "2840 5076\n", "10700 9284\n", "7900 11092\n", "1376 4848\n", "11536 9232\n", "10144 9840\n", "9016 11652\n", "6564 3264\n", "9448 2856\n", "7120 448\n", "10668 9704\n", "4584 4124\n", "11812 2664\n", "10864 9616\n", "9336 11292\n", "3364 32\n", "212 7312\n", "5308 9488\n", "5968 11072\n", "6008 11652\n", "10840 8108\n", "1328 9608\n", "4288 12224\n", "4720 4108\n", "10632 6304\n", "4448 56\n", "8140 8776\n", "904 3052\n", "3960 9800\n", "8692 12336\n", "1752 11532\n", "2676 832\n", "5456 9384\n", "4892 8824\n", "5816 5444\n", "4624 10468\n", "9636 3520\n", "376 8500\n", "5048 6772\n", "7116 2888\n", "3580 7992\n", "12332 4668\n", "7728 10592\n", "832 916\n", "5724 3988\n", "13032 1120\n", "11028 8936\n", "3928 1928\n", "10880 10072\n", "2512 8932\n", "3096 4660\n", "9212 11684\n", "11468 2756\n", "184 7072\n", "GF1_WFV3_E103.0_N5.4_20150103_L2A0000566131.tif\n", "11144 9796\n", "6944 9640\n", "3616 8008\n", "13016 6832\n", "5744 6648\n", "124 8144\n", "7584 12076\n", "7372 7624\n", "5000 4088\n", "12824 10780\n", "1148 6732\n", "56 388\n", "10656 5328\n", "9332 6280\n", "6388 6436\n", "532 10592\n", "8420 3440\n", "4244 3372\n", "8668 2004\n", "92 11620\n", "6268 1344\n", "4892 9084\n", "3172 3524\n", "4492 3420\n", "964 11068\n", "2776 9028\n", "1272 2524\n", "3996 9992\n", "1208 10724\n", "2508 9312\n", "12012 9056\n", "12672 2944\n", "11992 11580\n", "12416 6392\n", "1412 104\n", "5020 10764\n", "3856 12400\n", "6456 8752\n", "6404 4296\n", "10332 6060\n", "2500 4440\n", "5088 4460\n", "10688 4516\n", "5784 8664\n", "3548 512\n", "12504 7172\n", "9976 5896\n", "1516 3576\n", "10436 9436\n", "1248 7580\n", "9624 1204\n", "11516 7576\n", "6584 2536\n", "13120 9616\n", "4672 11748\n", "140 7072\n", "3308 1100\n", "6928 3368\n", "3856 12356\n", "2584 11892\n", "11328 10392\n", "10024 12024\n", "1492 9360\n", "4480 11264\n", "10264 2004\n", "10244 4160\n", "6040 10344\n", "9128 10120\n", "5636 9416\n", "11824 2844\n", "1588 1640\n", "8244 844\n", "5564 3372\n", "3004 9384\n", "9208 3684\n", "3288 11004\n", "7704 11768\n", "9748 152\n", "5832 7516\n", "9052 8560\n", "11640 8792\n", "3692 1484\n", "2728 8576\n", "2788 88\n", "10636 4060\n", "11752 836\n", "380 760\n", "11488 5888\n", "10236 12404\n", "4612 3548\n", "12624 2596\n", "1388 2300\n", "4868 4224\n", "8508 7420\n", "11476 6588\n", "9928 3616\n", "1896 1044\n", "3564 4200\n", "10200 1972\n", "1312 11408\n", "GF1_WFV3_E103.3_N18.9_20140523_L2A0000356148.tif\n", "11684 8096\n", "1736 12024\n", "11900 9364\n", "6648 4428\n", "9452 4576\n", "4220 8200\n", "9004 10604\n", "3176 5220\n", "728 8708\n", "3940 11480\n", "4916 1308\n", "9852 3732\n", "13000 10888\n", "11756 6244\n", "8160 1812\n", "10428 928\n", "10460 3804\n", "10124 1336\n", "7264 2840\n", "7420 8076\n", "712 7156\n", "11324 12416\n", "11156 12088\n", "8704 11500\n", "2856 388\n", "4424 2380\n", "8396 11148\n", "744 988\n", "10944 1540\n", "11208 6956\n", "9332 11860\n", "12988 8340\n", "2740 9936\n", "2132 528\n", "8644 9884\n", "708 2884\n", "11024 9028\n", "10024 4572\n", "1388 3728\n", "5316 12560\n", "8444 8440\n", "5136 2424\n", "7688 5128\n", "100 11472\n", "8060 6816\n", "13012 9204\n", "4288 10164\n", "3104 1096\n", "9176 6880\n", "11992 7052\n", "1412 1744\n", "13376 3948\n", "4880 304\n", "1824 328\n", "1704 7092\n", "12440 2288\n", "4388 7584\n", "8292 11740\n", "5552 2464\n", "6648 11196\n", "10040 10316\n", "9516 5472\n", "11320 4304\n", "1956 4416\n", "1000 4740\n", "4808 7456\n", "12524 3052\n", "5244 7972\n", "13416 7596\n", "5592 7768\n", "12132 604\n", "2288 12240\n", "11308 9700\n", "12880 9144\n", "9808 7908\n", "6452 1812\n", "12916 7000\n", "5456 5636\n", "8148 9036\n", "9176 8408\n", "536 12252\n", "2608 12324\n", "9484 9784\n", "11080 4496\n", "6192 3332\n", "8172 7108\n", "6920 7128\n", "968 3032\n", "6152 236\n", "1484 5980\n", "11608 3916\n", "10592 8632\n", "3864 5000\n", "8084 5040\n", "9032 9672\n", "9956 6628\n", "6216 10668\n", "12392 1752\n", "4724 4020\n", "6668 60\n", "GF1_WFV3_E111.8_N20.6_20140123_L2A0000161278.tif\n", "8912 12140\n", "9044 9748\n", "11628 6548\n", "9112 7824\n", "1972 1936\n", "660 4900\n", "128 672\n", "6056 6784\n", "12176 10604\n", "2240 10272\n", "2108 1348\n", "2688 12308\n", "8100 5024\n", "3152 9868\n", "11040 3092\n", "11624 11016\n", "4900 10600\n", "7636 4016\n", "5480 5708\n", "10312 60\n", "11280 10836\n", "3784 11868\n", "10184 768\n", "12188 11048\n", "5960 4720\n", "560 4012\n", "9252 11364\n", "12388 2856\n", "7760 2740\n", "1520 4392\n", "5880 4432\n", "720 9104\n", "7376 9344\n", "92 6020\n", "12876 8324\n", "11472 1512\n", "11820 2728\n", "7912 7860\n", "4564 9432\n", "2152 10128\n", "6676 892\n", "3460 4968\n", "7152 3772\n", "8412 5200\n", "6520 11588\n", "12548 10152\n", "9748 11384\n", "10968 11832\n", "4524 4732\n", "10112 1972\n", "3808 11732\n", "4176 500\n", "8084 10940\n", "4676 12156\n", "1516 6140\n", "540 976\n", "5056 3944\n", "10684 1420\n", "10912 620\n", "5376 7672\n", "3112 7332\n", "3788 9024\n", "11692 5036\n", "6412 3292\n", "3976 4820\n", "4776 1980\n", "11804 11420\n", "48 1052\n", "8672 9188\n", "4380 5944\n", "10772 8692\n", "392 3372\n", "10280 6936\n", "3144 472\n", "6548 4000\n", "504 4196\n", "12676 10272\n", "6432 8456\n", "10792 2676\n", "2876 7656\n", "2572 10848\n", "5852 2944\n", "8792 6400\n", "4420 200\n", "3976 864\n", "1436 6872\n", "3492 10956\n", "13052 1264\n", "6868 5376\n", "12112 3848\n", "1036 5776\n", "9620 5384\n", "13128 7124\n", "9072 9248\n", "2512 7596\n", "7976 4032\n", "5836 10620\n", "12796 5288\n", "12000 11188\n", "8184 1780\n", "GF1_WFV3_E114.0_N10.5_20150529_L2A0000980257.tif\n", "3780 5428\n", "1364 9288\n", "11564 4416\n", "1416 6012\n", "7804 5192\n", "10916 8804\n", "4336 11828\n", "2368 9508\n", "12700 6056\n", "8384 3068\n", "4248 8660\n", "2344 1428\n", "3224 5200\n", "9164 872\n", "1072 4604\n", "8432 1148\n", "11040 4948\n", "2172 9148\n", "3556 2124\n", "10220 1488\n", "9032 3100\n", "7328 2372\n", "4196 2764\n", "9296 3568\n", "11528 9120\n", "76 3952\n", "8560 9240\n", "1300 7292\n", "10916 2520\n", "9980 10912\n", "9272 7568\n", "2152 12860\n", "11172 7080\n", "11508 7864\n", "2660 4328\n", "9804 9496\n", "3400 12764\n", "6284 10980\n", "6596 5760\n", "12308 6212\n", "452 7140\n", "4276 11964\n", "9036 8008\n", "1600 5824\n", "3212 3524\n", "3024 4284\n", "7676 11240\n", "12328 8556\n", "7368 344\n", "5100 10700\n", "11432 776\n", "12384 9308\n", "11908 7896\n", "7764 6528\n", "5208 6784\n", "60 11828\n", "8060 7984\n", "2616 3232\n", "12860 11872\n", "1280 4868\n", "10396 6196\n", "3904 9488\n", "232 9432\n", "3832 800\n", "2444 9348\n", "3516 1212\n", "4896 12868\n", "3796 9832\n", "1036 4008\n", "6096 9980\n", "7644 11428\n", "6800 3544\n", "104 7664\n", "4164 9564\n", "2348 8896\n", "2808 9112\n", "10300 11028\n", "4728 2128\n", "7740 7708\n", "6728 5956\n", "3784 7184\n", "1992 9128\n", "1840 1628\n", "108 3504\n", "676 10584\n", "12912 8224\n", "5384 672\n", "2176 12064\n", "10132 5628\n", "10028 2172\n", "2708 1108\n", "4348 11060\n", "4488 584\n", "8996 1812\n", "4420 12840\n", "476 12684\n", "2328 1008\n", "3036 7408\n", "5652 3904\n", "2116 7412\n", "GF1_WFV3_E114.1_N2.1_20151011_L2A0001094727.tif\n", "9548 9352\n", "11064 2840\n", "692 156\n", "9396 10916\n", "8604 2912\n", "1076 7472\n", "8000 8768\n", "7652 10264\n", "12648 1512\n", "10060 10620\n", "9312 3632\n", "11164 4496\n", "6148 3668\n", "10128 2932\n", "10504 4796\n", "6832 8160\n", "7964 10800\n", "3448 10464\n", "6664 5212\n", "5416 2464\n", "8072 1820\n", "7576 2076\n", "4368 8860\n", "5048 8244\n", "9936 4176\n", "13068 8716\n", "11500 6592\n", "11036 7992\n", "4268 10244\n", "4848 7156\n", "7560 5848\n", "8388 7560\n", "9636 7104\n", "10132 6000\n", "7092 11364\n", "12112 10248\n", "8476 8308\n", "268 9848\n", "10356 7256\n", "1752 9884\n", "5160 3696\n", "11320 10008\n", "9224 216\n", "5492 7872\n", "9372 7164\n", "9648 6352\n", "9104 7848\n", "5080 4972\n", "1716 2804\n", "2248 3320\n", "5692 8664\n", "4016 10928\n", "4644 2904\n", "10816 8160\n", "11636 8748\n", "4216 4780\n", "9552 1232\n", "4196 7648\n", "5436 6344\n", "4176 10544\n", "8112 3476\n", "12508 4596\n", "12408 1024\n", "4692 3148\n", "12408 10656\n", "5476 8648\n", "12712 6012\n", "12508 3848\n", "10680 3500\n", "11960 1064\n", "11700 9292\n", "6988 8288\n", "4872 7856\n", "5864 9552\n", "11352 10024\n", "9932 3480\n", "2788 9640\n", "392 10648\n", "608 6496\n", "4320 10704\n", "7584 3644\n", "3252 10440\n", "12620 4464\n", "9776 3808\n", "208 8668\n", "6684 7524\n", "11128 3224\n", "4584 3308\n", "6144 5536\n", "9892 12236\n", "8436 1448\n", "3068 11268\n", "7900 1388\n", "7984 12088\n", "2512 5728\n", "12992 2164\n", "12208 5572\n", "10420 12436\n", "12792 11524\n", "3548 360\n", "GF1_WFV3_E121.7_N27.2_20160801_L2A0001735665.tif\n", "8300 3800\n", "11696 1884\n", "12704 6420\n", "2984 9496\n", "4868 3504\n", "12976 6368\n", "9480 4704\n", "36 6396\n", "8252 5304\n", "10356 13584\n", "9648 8532\n", "11684 9648\n", "9180 9796\n", "3780 9440\n", "4240 6644\n", "9772 8996\n", "2948 9572\n", "8744 11004\n", "3936 5776\n", "2216 2444\n", "3696 7064\n", "12396 9956\n", "4496 7736\n", "11096 4252\n", "8984 11264\n", "1148 56\n", "7552 8120\n", "8336 9148\n", "12912 9200\n", "7188 4416\n", "6456 132\n", "13664 11488\n", "4244 84\n", "672 2628\n", "11444 8088\n", "9996 10400\n", "12416 7628\n", "13724 13708\n", "7348 5872\n", "4788 7956\n", "5188 3252\n", "10344 8052\n", "724 5848\n", "8272 3852\n", "1888 1180\n", "11376 704\n", "1304 3436\n", "5832 7592\n", "8668 4848\n", "1220 6496\n", "9336 12016\n", "5176 5924\n", "548 12044\n", "10280 5280\n", "1916 4272\n", "12516 11404\n", "1900 7976\n", "5080 12880\n", "9048 4948\n", "3304 7544\n", "12924 48\n", "3920 2384\n", "6500 9428\n", "9192 4324\n", "1384 4304\n", "3132 5464\n", "12316 2468\n", "13476 13712\n", "6564 4424\n", "2732 11084\n", "6336 13148\n", "2752 1088\n", "2124 4984\n", "12292 348\n", "13124 12868\n", "5508 2324\n", "704 12664\n", "2908 7076\n", "9820 2692\n", "11092 7364\n", "12704 1100\n", "11344 11624\n", "3936 3548\n", "12672 4760\n", "13356 9744\n", "1220 8992\n", "328 6764\n", "9700 13056\n", "2404 12176\n", "8848 2500\n", "2760 2012\n", "10628 5096\n", "13640 13008\n", "4168 12408\n", "8948 10524\n", "2172 12588\n", "12072 5896\n", "3700 2440\n", "3664 212\n", "3144 12980\n", "GF1_WFV3_E133.2_N40.6_20160820_L2A0001774408.tif\n", "6832 4972\n", "10124 8044\n", "11456 10084\n", "7624 10120\n", "2744 6544\n", "10060 12684\n", "8852 12760\n", "4768 200\n", "5632 11628\n", "12104 6512\n", "12844 1972\n", "11740 10972\n", "12256 7748\n", "2000 7028\n", "2896 8356\n", "10092 2112\n", "1296 11744\n", "12236 8008\n", "13348 5156\n", "440 8332\n", "9204 10240\n", "600 10536\n", "1244 96\n", "12472 9776\n", "3016 7684\n", "5240 4996\n", "5236 11940\n", "1388 1604\n", "5748 1248\n", "4784 10276\n", "672 11276\n", "1232 4580\n", "1480 10220\n", "11432 4436\n", "3236 6336\n", "6172 10768\n", "9468 11696\n", "3712 12308\n", "10220 7712\n", "10808 3992\n", "8044 4668\n", "8044 7876\n", "11112 1344\n", "5344 12444\n", "1620 8040\n", "4864 3560\n", "13616 11512\n", "68 12732\n", "5952 976\n", "4292 6388\n", "10412 5112\n", "1844 5732\n", "1656 3864\n", "13428 12872\n", "4488 3504\n", "11280 8668\n", "11392 6160\n", "7700 6408\n", "2960 5012\n", "576 1576\n", "620 13016\n", "6708 8212\n", "620 9056\n", "944 12480\n", "9368 10128\n", "2864 3348\n", "9196 2496\n", "10036 10672\n", "3664 6480\n", "12212 2764\n", "2184 2840\n", "8044 1648\n", "4912 6528\n", "2348 7396\n", "10968 9096\n", "7360 4616\n", "1636 10964\n", "8556 8416\n", "12556 136\n", "736 10576\n", "9300 6552\n", "8384 9248\n", "13560 6832\n", "6692 560\n", "332 5216\n", "2368 11276\n", "9164 8992\n", "3548 10020\n", "2664 2200\n", "12988 11784\n", "10396 9952\n", "5076 10544\n", "140 10524\n", "5596 11544\n", "12860 8768\n", "5740 12136\n", "1236 6792\n", "12248 7696\n", "8116 1592\n", "7348 1616\n", "GF1_WFV3_E166.1_S14.8_20150314_L2A0000696278.tif\n", "6368 3956\n", "9516 2176\n", "4076 1244\n", "12188 4944\n", "684 11260\n", "3624 11352\n", "4712 8436\n", "104 8100\n", "9184 13060\n", "11404 11996\n", "11340 1740\n", "688 4544\n", "12532 2832\n", "6768 1684\n", "12120 5820\n", "12204 5376\n", "152 4276\n", "11664 9464\n", "72 7448\n", "9108 7272\n", "6180 9144\n", "7628 516\n", "2204 11628\n", "2620 932\n", "1612 10268\n", "7496 644\n", "11796 5360\n", "10996 6316\n", "8668 6380\n", "8932 2740\n", "11736 7100\n", "1844 4136\n", "8792 12884\n", "1452 4888\n", "7116 11624\n", "11752 7476\n", "932 13288\n", "2196 2768\n", "9012 8764\n", "9968 1228\n", "1592 7020\n", "9272 6388\n", "4452 14032\n", "4932 7480\n", "3204 2136\n", "11316 13808\n", "11284 12148\n", "8648 11792\n", "6448 620\n", "336 13372\n", "4600 8592\n", "5948 8916\n", "3792 12668\n", "3460 1996\n", "6032 3672\n", "10516 12676\n", "1344 11632\n", "9196 4788\n", "8456 1880\n", "11224 5888\n", "3832 5824\n", "8908 5040\n", "6180 6048\n", "5716 13004\n", "4720 5388\n", "4912 10752\n", "4828 6504\n", "12780 7924\n", "2428 2552\n", "8920 2800\n", "7700 6816\n", "8056 840\n", "748 1060\n", "13244 6052\n", "5860 3708\n", "948 12716\n", "2696 6280\n", "8868 11516\n", "12912 3504\n", "4612 11836\n", "1700 6324\n", "3348 8856\n", "9220 10108\n", "3252 8048\n", "3288 932\n", "9328 6192\n", "5176 2108\n", "10892 3832\n", "764 4460\n", "3108 11820\n", "244 3200\n", "6412 6468\n", "8684 10340\n", "2748 9872\n", "12360 9140\n", "1828 4040\n", "10596 7336\n", "9956 3632\n", "5284 4252\n", "4388 12312\n", "GF1_WFV3_W46.5_S11.4_20140612_L2A0000253070.tif\n", "9660 4572\n", "6296 2544\n", "688 1064\n", "10980 5780\n", "12220 11960\n", "5676 10312\n", "316 8180\n", "4620 9652\n", "5580 2116\n", "12796 916\n", "4700 10636\n", "11324 2712\n", "3000 4416\n", "9284 4104\n", "7772 2828\n", "10852 5352\n", "8752 1668\n", "9892 8252\n", "10080 10692\n", "2664 3896\n", "8560 628\n", "3236 304\n", "4164 6208\n", "7424 3656\n", "6600 7220\n", "10788 5564\n", "9636 7228\n", "12708 2604\n", "11036 3080\n", "9488 4944\n", "72 4320\n", "5684 8932\n", "9344 2492\n", "2348 4332\n", "8472 8928\n", "4240 9624\n", "5404 7076\n", "2428 10300\n", "9524 11744\n", "604 12260\n", "1884 11380\n", "10164 3256\n", "5576 7420\n", "8892 6180\n", "1248 11684\n", "10296 180\n", "8160 704\n", "2788 3248\n", "2648 3708\n", "7460 7612\n", "7972 9672\n", "7456 7768\n", "8268 6108\n", "6084 4660\n", "3284 11632\n", "480 9832\n", "5596 4920\n", "1240 10836\n", "7968 9132\n", "9532 9916\n", "10024 9760\n", "12304 12688\n", "5240 7076\n", "12856 9836\n", "3396 420\n", "576 7932\n", "11364 11632\n", "164 6240\n", "4188 10880\n", "900 10200\n", "1720 1620\n", "2640 6388\n", "1808 640\n", "4844 5488\n", "4100 11592\n", "11024 1172\n", "5648 11640\n", "5292 32\n", "12236 6528\n", "6632 9956\n", "1264 7764\n", "13176 8328\n", "3616 5260\n", "6960 564\n", "3144 8052\n", "2792 320\n", "6252 6108\n", "3884 9516\n", "5088 12364\n", "10332 11680\n", "9968 6328\n", "4496 11952\n", "9904 11908\n", "7360 4412\n", "10812 6204\n", "9996 9640\n", "3412 3408\n", "7176 11628\n", "13008 12700\n", "7268 7964\n", "GF1_WFV3_W47.1_S19.8_20140510_L2A0000222534.tif\n", "5680 40\n", "7488 11712\n", "6792 7132\n", "1268 10516\n", "3184 9928\n", "2372 4916\n", "740 360\n", "2152 7884\n", "11340 9080\n", "10856 1292\n", "8532 7700\n", "12536 1680\n", "3836 3588\n", "5532 5572\n", "11932 10236\n", "11540 1344\n", "4012 3692\n", "3984 3280\n", "10476 2380\n", "916 132\n", "1180 11124\n", "7900 8644\n", "4824 5748\n", "3772 112\n", "1088 9232\n", "4660 392\n", "11520 4964\n", "3948 8420\n", "8496 11564\n", "8788 2808\n", "8372 10696\n", "7532 12188\n", "5868 7896\n", "8032 11928\n", "9076 720\n", "6092 2140\n", "2576 4980\n", "12500 6240\n", "8216 8504\n", "5332 4956\n", "2600 10448\n", "4968 5428\n", "5248 1768\n", "2756 6420\n", "4556 1252\n", "7388 3756\n", "9812 6188\n", "5736 10700\n", "3680 1316\n", "7956 4708\n", "3856 2812\n", "1980 5708\n", "4636 11600\n", "860 1996\n", "3520 2816\n", "776 11408\n", "8396 4092\n", "10760 4944\n", "12444 9904\n", "2564 10288\n", "6960 7052\n", "10172 1388\n", "8908 9160\n", "1868 3260\n", "5848 7476\n", "5876 3588\n", "9040 10984\n", "4792 10752\n", "9384 11384\n", "13124 1992\n", "12720 9528\n", "7084 564\n", "9320 788\n", "2476 9456\n", "9184 788\n", "7500 11636\n", "1864 7360\n", "4780 11168\n", "10164 9912\n", "8108 2176\n", "2792 11220\n", "11092 6600\n", "3728 4156\n", "11228 5324\n", "4860 8088\n", "12548 6388\n", "4544 8200\n", "10440 8264\n", "8896 1552\n", "10692 4924\n", "6348 1860\n", "220 11256\n", "3332 7416\n", "1588 5804\n", "1800 1896\n", "5384 8016\n", "9544 3036\n", "2524 4740\n", "1816 4904\n", "9560 5752\n", "GF1_WFV3_W52.1_S9.7_20140613_L2A0000253109.tif\n", "1160 9448\n", "1692 3864\n", "9244 4700\n", "9876 3968\n", "8864 10488\n", "1188 4792\n", "10944 12500\n", "10456 4136\n", "3332 9152\n", "480 8648\n", "5220 6044\n", "11704 8408\n", "8624 8148\n", "2716 10084\n", "11668 2440\n", "7828 1156\n", "13124 11936\n", "2472 6060\n", "5628 2388\n", "6820 11684\n", "7620 11004\n", "4264 720\n", "7528 3668\n", "9072 8560\n", "11400 7844\n", "2928 684\n", "8752 9244\n", "4060 3632\n", "8072 1604\n", "12544 616\n", "3936 1416\n", "1876 7104\n", "6268 3320\n", "6700 4540\n", "696 4244\n", "7420 5976\n", "3112 9076\n", "3820 12432\n", "12488 11280\n", "7860 9908\n", "10872 448\n", "7204 5816\n", "9880 5716\n", "12572 7672\n", "2964 956\n", "12572 7520\n", "10028 6200\n", "2912 9508\n", "3360 2652\n", "5992 7108\n", "5732 3104\n", "9032 8592\n", "5148 936\n", "12620 12484\n", "6620 6788\n", "5528 7028\n", "11448 5544\n", "9688 5908\n", "716 8912\n", "9156 8504\n", "2544 11308\n", "12064 2864\n", "7248 3524\n", "3608 1876\n", "8032 12456\n", "4216 11248\n", "5132 2076\n", "6348 9588\n", "2768 7168\n", "1044 4564\n", "5216 12004\n", "2552 8008\n", "10704 5776\n", "12320 712\n", "5252 7568\n", "12500 9260\n", "5820 6636\n", "11516 1148\n", "2252 10136\n", "8508 5536\n", "3764 4592\n", "4912 6096\n", "2000 9108\n", "6580 2720\n", "7764 12556\n", "7164 10588\n", "5364 3960\n", "220 6448\n", "1308 11468\n", "112 228\n", "5092 2424\n", "6352 6552\n", "7536 9896\n", "9868 2096\n", "13072 7012\n", "11972 1144\n", "10120 11144\n", "7500 9244\n", "2476 7256\n", "6168 2120\n", "GF1_WFV3_W54.2_S19.8_20140613_L2A0000253104.tif\n", "6104 12348\n", "9736 10180\n", "11664 9408\n", "5168 6352\n", "2360 312\n", "10884 7920\n", "7924 10008\n", "7564 7608\n", "9404 5216\n", "7320 6140\n", "1544 12576\n", "11640 12144\n", "12572 8744\n", "7076 12428\n", "6068 9152\n", "6388 9524\n", "12892 2516\n", "2596 11324\n", "10388 336\n", "9232 12520\n", "9356 4496\n", "12856 9336\n", "2388 7444\n", "9676 12544\n", "5336 12636\n", "5620 1804\n", "11536 6764\n", "11640 4724\n", "8992 8592\n", "12136 5652\n", "2976 6640\n", "6264 2972\n", "7448 12448\n", "11716 2728\n", "11204 8668\n", "13020 1604\n", "1140 8132\n", "11980 7008\n", "13040 4568\n", "5040 8404\n", "5620 12632\n", "8204 8724\n", "10848 10464\n", "284 4912\n", "7148 2044\n", "5832 12312\n", "10000 7548\n", "9988 8680\n", "8344 10504\n", "3200 5024\n", "13272 2596\n", "11004 2704\n", "1380 12592\n", "10936 4904\n", "10276 3932\n", "11544 7968\n", "10884 6424\n", "5688 11444\n", "8708 5272\n", "384 4828\n", "336 1736\n", "6028 11848\n", "4856 10224\n", "12572 5224\n", "12332 7696\n", "11860 9216\n", "12928 5404\n", "676 1932\n", "4160 328\n", "8496 2588\n", "1780 10188\n", "10144 3652\n", "4736 2124\n", "13004 4552\n", "5380 4636\n", "11156 3664\n", "13104 476\n", "11672 5164\n", "8236 4112\n", "304 8048\n", "12328 12760\n", "5272 7500\n", "8668 4640\n", "9532 1248\n", "9900 1320\n", "2244 1284\n", "12840 7644\n", "3444 10004\n", "13356 10364\n", "12540 9752\n", "10776 584\n", "2916 8180\n", "3528 8388\n", "11580 8384\n", "9908 1564\n", "5560 5180\n", "4736 2752\n", "10608 3832\n", "460 3976\n", "11720 11044\n", "GF1_WFV3_W55.1_S23.6_20140613_L2A0000253139.tif\n", "6224 11152\n", "3648 972\n", "11100 6312\n", "8528 4540\n", "11676 812\n", "8200 2772\n", "5136 2132\n", "11716 11148\n", "1036 1996\n", "536 532\n", "12636 7920\n", "1540 4912\n", "9516 6164\n", "1116 7272\n", "5208 5244\n", "12944 6756\n", "648 7992\n", "13052 6840\n", "13372 1960\n", "10904 880\n", "1420 11832\n", "2688 10548\n", "2144 1380\n", "12560 8336\n", "1204 11880\n", "5516 12428\n", "2508 4468\n", "6648 8148\n", "11284 1148\n", "9576 2224\n", "5468 4440\n", "5464 2868\n", "616 2752\n", "3932 216\n", "8276 8612\n", "4044 8996\n", "1396 12456\n", "8336 5464\n", "13228 4532\n", "4628 2260\n", "2744 7992\n", "12624 5360\n", "9520 11588\n", "4800 9760\n", "2456 3224\n", "3908 3272\n", "11492 8904\n", "4964 9144\n", "184 12972\n", "2124 5876\n", "12836 2568\n", "12972 7072\n", "1208 4064\n", "12176 2016\n", "1988 272\n", "11072 3680\n", "11880 3696\n", "812 3388\n", "2488 4244\n", "9176 7452\n", "7132 7524\n", "5692 976\n", "6668 10256\n", "5216 10348\n", "4276 6892\n", "10064 12504\n", "8532 7072\n", "7300 5180\n", "10828 8388\n", "4592 3564\n", "4452 904\n", "9120 4480\n", "5564 12000\n", "8744 11560\n", "2352 10356\n", "2620 8008\n", "8228 5408\n", "12216 10012\n", "10780 6272\n", "324 5736\n", "8244 5284\n", "6728 11356\n", "2744 11068\n", "9340 2288\n", "5648 5484\n", "5756 1844\n", "8960 10724\n", "6876 2744\n", "3864 9164\n", "8716 1340\n", "10760 7652\n", "9492 5208\n", "80 4212\n", "8396 9624\n", "4096 748\n", "4548 1044\n", "11896 10052\n", "11080 11348\n", "6604 9700\n", "516 10248\n", "GF1_WFV3_W92.2_N35.6_20140822_L2A0000320024.tif\n", "11676 2324\n", "716 7668\n", "9128 5132\n", "2968 7884\n", "1728 2124\n", "2532 5556\n", "11856 6488\n", "5744 8056\n", "5968 10624\n", "11792 11768\n", "11540 1608\n", "1520 11968\n", "12412 2656\n", "10336 10488\n", "1396 11868\n", "7008 9556\n", "2900 12076\n", "12128 2172\n", "9012 10784\n", "5648 7224\n", "3616 6424\n", "3372 11672\n", "2300 9936\n", "6760 12044\n", "2100 2824\n", "5208 6196\n", "12048 1536\n", "8952 2640\n", "7776 6356\n", "6352 10488\n", "10352 10288\n", "1840 1924\n", "28 11692\n", "12348 3720\n", "9736 2508\n", "7012 360\n", "3060 2576\n", "10788 8160\n", "11160 6084\n", "7124 9448\n", "7060 8044\n", "9948 4784\n", "6020 7068\n", "11896 12436\n", "3344 4964\n", "5760 1860\n", "8148 228\n", "4888 2608\n", "10644 8476\n", "736 1716\n", "80 3132\n", "2080 792\n", "10428 876\n", "6572 4012\n", "13360 2224\n", "11340 336\n", "5640 11520\n", "9936 4968\n", "5372 6028\n", "8520 12084\n", "3980 4308\n", "10088 8192\n", "200 8856\n", "12784 1068\n", "6736 6992\n", "2664 12440\n", "8080 7028\n", "2784 12552\n", "2164 7976\n", "8260 8672\n", "4620 9408\n", "9928 3880\n", "6012 5360\n", "11780 1372\n", "11536 1280\n", "9468 6956\n", "9364 968\n", "11384 2512\n", "12164 1432\n", "5584 516\n", "9296 2244\n", "12584 3896\n", "9416 11784\n", "11352 11328\n", "7160 8164\n", "3060 36\n", "12260 3072\n", "2608 6736\n", "6356 8728\n", "6044 4200\n", "8700 9452\n", "2496 11644\n", "7988 8688\n", "12448 10388\n", "2900 4548\n", "11488 3168\n", "5320 632\n", "10176 6480\n", "4676 3988\n", "2604 132\n", "GF1_WFV3_W96.1_N30.6_20140516_L2A0000244661.tif\n", "9448 6572\n", "4088 7628\n", "1992 9408\n", "10736 400\n", "11588 11048\n", "11908 1948\n", "6088 3640\n", "5328 5308\n", "5220 404\n", "1924 3216\n", "13008 7284\n", "11008 11032\n", "256 10952\n", "4712 10264\n", "11424 7432\n", "10976 3884\n", "3480 2524\n", "9348 4684\n", "2072 1284\n", "11460 876\n", "5892 2852\n", "3320 8360\n", "5108 11732\n", "6952 9752\n", "11712 12276\n", "3960 11232\n", "1108 4492\n", "2224 5924\n", "7052 6488\n", "8688 11428\n", "12972 188\n", "3056 5700\n", "6148 5796\n", "12968 2752\n", "4868 12400\n", "12244 4232\n", "8980 228\n", "6176 10980\n", "968 7256\n", "5464 7248\n", "6200 2372\n", "10820 3056\n", "8952 7028\n", "1308 9780\n", "12656 2392\n", "12548 11016\n", "11624 3540\n", "2264 3228\n", "6212 12080\n", "11012 9860\n", "2796 6128\n", "3852 2616\n", "2572 6736\n", "8732 5808\n", "9272 11808\n", "3480 2032\n", "1732 11696\n", "11000 3060\n", "7160 8452\n", "8276 6736\n", "6128 8336\n", "7492 2632\n", "2676 1012\n", "1672 5480\n", "4340 2724\n", "13076 3972\n", "10144 100\n", "5884 6080\n", "11216 2468\n", "784 988\n", "828 6424\n", "2188 1544\n", "240 4764\n", "3528 4876\n", "12872 4328\n", "1728 5984\n", "4444 1828\n", "7384 3732\n", "6864 7716\n", "6500 8696\n", "2840 10548\n", "8164 2184\n", "12104 9240\n", "4260 6380\n", "9728 9360\n", "8848 1888\n", "4340 900\n", "8752 3124\n", "12532 8672\n", "1660 7736\n", "10184 6760\n", "7796 6008\n", "5172 9264\n", "9872 5328\n", "6768 7992\n", "11660 544\n", "3740 6464\n", "7656 9164\n", "9936 7136\n", "8820 4200\n", "GF1_WFV3_W97.0_N27.3_20140516_L2A0000244666.tif\n", "12260 11452\n", "13108 10852\n", "6740 8652\n", "5820 7088\n", "2508 6560\n", "10472 880\n", "5988 10472\n", "624 5840\n", "9272 1864\n", "8716 9392\n", "8308 11272\n", "1964 11556\n", "10840 11572\n", "8340 10868\n", "10844 8720\n", "8956 3016\n", "5256 11880\n", "8980 9784\n", "292 1260\n", "8732 9684\n", "9288 8408\n", "5404 508\n", "2304 9508\n", "11668 6040\n", "8584 8380\n", "11460 1696\n", "4172 8088\n", "2528 7664\n", "8316 732\n", "7680 2708\n", "7608 11440\n", "8668 4992\n", "7936 8544\n", "12620 6368\n", "9632 10524\n", "10496 6928\n", "5132 11944\n", "308 9732\n", "9512 2648\n", "1160 752\n", "3520 1452\n", "88 12176\n", "4212 11688\n", "5120 9248\n", "10912 5256\n", "4364 3528\n", "2036 8408\n", "1356 11936\n", "3620 1276\n", "4844 6460\n", "576 11340\n", "10932 7236\n", "1092 4100\n", "10272 232\n", "9240 4960\n", "2688 5784\n", "4276 684\n", "1172 4508\n", "5384 3124\n", "5596 12176\n", "1764 4064\n", "13216 11388\n", "2020 284\n", "12456 11852\n", "9608 11572\n", "10360 11440\n", "4164 4568\n", "10200 432\n", "2004 11308\n", "7780 9428\n", "9624 9992\n", "10084 11300\n", "5988 1012\n", "3180 6328\n", "4760 8196\n", "4756 11304\n", "7612 1868\n", "3348 8596\n", "5868 4276\n", "9652 4396\n", "9192 11428\n", "6004 10492\n", "6648 6092\n", "1848 9412\n", "9528 5188\n", "12244 8368\n", "5260 11560\n", "9084 1228\n", "10372 2180\n", "7488 1536\n", "7952 3032\n", "916 4836\n", "9060 8676\n", "4252 12408\n", "3344 12336\n", "11740 4832\n", "8748 9140\n", "5392 4028\n", "12236 7372\n", "7184 2792\n", "GF1_WFV4_E109.6_N18.5_20140510_L2A0000286067.tif\n", "8956 15644\n", "7892 7976\n", "1872 1352\n", "972 10464\n", "9488 1596\n", "11336 10968\n", "8500 12720\n", "7188 9332\n", "8644 2648\n", "3908 3028\n", "12480 5968\n", "13556 1468\n", "908 10992\n", "12172 9484\n", "4408 1628\n", "14240 10188\n", "8256 6440\n", "8712 2492\n", "7912 5388\n", "12044 2828\n", "4876 4268\n", "796 6572\n", "8140 14280\n", "2324 2804\n", "13728 9664\n", "8740 14264\n", "2676 3552\n", "3348 15272\n", "9356 352\n", "6672 8320\n", "7532 11900\n", "5268 15472\n", "3464 1128\n", "1424 4700\n", "1728 6344\n", "7596 4480\n", "5180 2620\n", "7296 1732\n", "6844 9812\n", "2624 4940\n", "14004 15116\n", "3388 5216\n", "8700 3504\n", "4692 7252\n", "9556 3904\n", "7304 12472\n", "14168 7108\n", "7844 828\n", "1344 544\n", "1316 8912\n", "372 1128\n", "6084 3192\n", "11760 1516\n", "4336 6320\n", "7348 340\n", "2024 1044\n", "11120 4852\n", "5872 13484\n", "3748 11668\n", "8172 2164\n", "13016 8544\n", "3788 2720\n", "7204 13020\n", "2044 2800\n", "13376 1044\n", "4872 14800\n", "8824 6184\n", "5820 3984\n", "13124 12712\n", "7588 6768\n", "9456 12276\n", "6564 7572\n", "14244 14540\n", "13328 5524\n", "13352 13256\n", "9400 8652\n", "5768 8804\n", "9584 15316\n", "1328 9572\n", "844 1112\n", "3804 8220\n", "7600 2552\n", "6076 1596\n", "12120 1752\n", "1348 11368\n", "1388 7904\n", "620 1020\n", "5812 10708\n", "1664 4728\n", "5992 15196\n", "2072 9656\n", "7132 14688\n", "8604 12924\n", "8768 5892\n", "7788 8348\n", "11416 14084\n", "10936 13616\n", "716 1816\n", "13416 11040\n", "13412 9840\n", "GF1_WFV4_E111.3_N30.2_20150506_L2A0000895933.tif\n", "11012 5876\n", "10744 412\n", "1472 10864\n", "8300 5768\n", "9404 636\n", "5360 2072\n", "5696 10528\n", "1084 5324\n", "7748 11068\n", "10600 2580\n", "13576 4648\n", "1260 6380\n", "7296 11596\n", "6712 13552\n", "13264 11428\n", "1260 2592\n", "9904 14196\n", "4184 4916\n", "1332 15552\n", "1144 3892\n", "8944 8868\n", "3020 4852\n", "11008 7732\n", "2292 14560\n", "11252 6676\n", "14136 14388\n", "9776 2972\n", "13276 2360\n", "14036 7508\n", "11832 14624\n", "4984 5524\n", "13884 8604\n", "11856 11324\n", "4528 12540\n", "3276 4444\n", "14000 852\n", "4524 6560\n", "12712 9776\n", "11708 148\n", "7300 7132\n", "9396 9572\n", "1260 5396\n", "4288 7812\n", "12604 9612\n", "2844 4072\n", "9620 14900\n", "7880 15592\n", "8992 8632\n", "7768 14744\n", "5132 14896\n", "13748 5568\n", "1984 15616\n", "5160 11732\n", "11816 332\n", "7976 14296\n", "1152 9840\n", "9212 7076\n", "4784 6404\n", "1808 11176\n", "340 3048\n", "10252 4496\n", "1104 9252\n", "13560 11540\n", "5384 13124\n", "9200 7196\n", "5312 14948\n", "8320 6372\n", "1436 13368\n", "13440 816\n", "11632 2972\n", "3720 5004\n", "7088 7388\n", "6336 1148\n", "4088 2056\n", "4744 12600\n", "4624 9364\n", "7572 2088\n", "8048 15616\n", "3320 6140\n", "12040 6172\n", "10084 3808\n", "760 14736\n", "11256 6284\n", "12672 10512\n", "11312 3280\n", "10256 11248\n", "8028 7152\n", "9580 8796\n", "12220 12580\n", "7788 12124\n", "5712 13180\n", "4240 7248\n", "7480 9620\n", "11788 8960\n", "1608 14896\n", "84 1652\n", "4256 5188\n", "5120 15060\n", "4116 516\n", "10172 11636\n", "GF1_WFV4_E114.9_N23.5_20141008_L2A0000508173.tif\n", "9368 10508\n", "13248 15256\n", "2308 13784\n", "11212 7532\n", "1496 8060\n", "7972 4136\n", "10124 768\n", "6348 5432\n", "10520 15312\n", "4736 4972\n", "4076 3484\n", "4932 3296\n", "2068 6396\n", "760 9840\n", "11100 3980\n", "3544 9132\n", "9616 2576\n", "852 11748\n", "2600 6428\n", "10520 3116\n", "588 4208\n", "852 1140\n", "5168 1292\n", "8500 7324\n", "4020 4044\n", "4860 14788\n", "9624 5548\n", "7292 8072\n", "8812 536\n", "14052 13720\n", "13440 404\n", "10092 11280\n", "9056 15612\n", "2728 13016\n", "13796 6816\n", "12436 1808\n", "9476 5928\n", "10588 5728\n", "13588 14088\n", "4552 4812\n", "140 9516\n", "6372 5204\n", "7896 4340\n", "5576 9332\n", "3484 3612\n", "3464 10852\n", "11812 7636\n", "7592 6660\n", "5356 15572\n", "3276 9848\n", "5284 236\n", "4572 3104\n", "12640 10048\n", "10500 10632\n", "12448 3072\n", "11200 1596\n", "9360 1724\n", "12140 12804\n", "1636 6640\n", "1788 1520\n", "7492 9036\n", "8248 4144\n", "3744 12636\n", "7940 184\n", "3172 11652\n", "4508 12036\n", "6644 8668\n", "9592 4776\n", "5988 13992\n", "9220 368\n", "13892 6932\n", "3840 2092\n", "8728 568\n", "13992 10920\n", "7424 2476\n", "13288 5480\n", "10292 12176\n", "14084 4840\n", "13676 13388\n", "5252 6584\n", "9412 10328\n", "13788 776\n", "12388 660\n", "6524 14852\n", "9796 12636\n", "1516 8096\n", "3980 3380\n", "6572 12368\n", "6372 11408\n", "8732 5928\n", "10840 5164\n", "13008 2600\n", "308 3196\n", "4900 12940\n", "4440 14900\n", "9232 15520\n", "1032 5096\n", "3392 14280\n", "1964 2560\n", "1668 6084\n", "GF1_WFV4_E115.1_S1.6_20151011_L2A0001094740.tif\n", "2000 5840\n", "10392 6428\n", "12180 7464\n", "13512 6112\n", "6892 7768\n", "2644 6476\n", "2268 13048\n", "10360 7600\n", "12128 4900\n", "10300 11632\n", "2372 9944\n", "3388 4028\n", "9276 2944\n", "2768 4644\n", "4268 5824\n", "9536 8244\n", "7556 732\n", "8328 3336\n", "2680 3656\n", "10852 3728\n", "12024 13080\n", "9936 14368\n", "6728 4784\n", "13296 10548\n", "8256 1932\n", "10112 6828\n", "960 13584\n", "12216 14280\n", "11600 10704\n", "12004 2756\n", "444 6920\n", "13240 948\n", "3804 12996\n", "8884 2776\n", "1608 4192\n", "3808 5396\n", "9300 596\n", "6232 12760\n", "2612 3216\n", "2852 9908\n", "12792 1948\n", "9488 3248\n", "8052 5868\n", "12652 4768\n", "12960 9416\n", "156 8916\n", "1680 7160\n", "11008 9092\n", "11372 8016\n", "9068 5664\n", "10636 1584\n", "13568 5864\n", "1224 11400\n", "1084 3396\n", "8124 12700\n", "9552 7728\n", "11812 9816\n", "10080 14572\n", "9088 2276\n", "12760 3312\n", "10364 10612\n", "5060 10624\n", "2020 9952\n", "13412 3084\n", "2164 10960\n", "9892 1700\n", "2048 584\n", "6012 10096\n", "7108 7168\n", "13536 2320\n", "12392 14680\n", "11292 9384\n", "6672 4636\n", "12728 3000\n", "4536 10600\n", "11408 9404\n", "2708 1400\n", "772 4464\n", "4932 6576\n", "3312 11536\n", "1880 8844\n", "1640 3428\n", "1520 3908\n", "4220 12268\n", "12128 14368\n", "1448 9568\n", "1940 10012\n", "7592 14396\n", "152 13984\n", "7720 10068\n", "4432 420\n", "5160 13224\n", "204 8824\n", "100 13504\n", "4348 14308\n", "112 2664\n", "7812 6964\n", "9676 4532\n", "11216 8292\n", "9980 3968\n", "GF1_WFV4_E116.1_N3.4_20151011_L2A0001094729.tif\n", "1764 9748\n", "928 14804\n", "13056 10916\n", "8760 4516\n", "1476 11080\n", "11880 1252\n", "5304 4840\n", "12616 5000\n", "13140 10264\n", "8132 4040\n", "5916 8668\n", "10068 872\n", "516 3700\n", "5916 2988\n", "9256 10108\n", "5660 2204\n", "13656 6608\n", "1176 2396\n", "6952 9208\n", "10964 2148\n", "5636 1464\n", "1996 3800\n", "12056 10976\n", "4016 10376\n", "2020 3240\n", "4024 14700\n", "10308 5128\n", "11616 1620\n", "3048 8756\n", "140 1228\n", "6124 9464\n", "1828 568\n", "12148 10888\n", "8324 9984\n", "2940 6504\n", "8932 3480\n", "12224 3248\n", "6160 10936\n", "11244 14936\n", "3700 4888\n", "8384 8660\n", "13276 9552\n", "1928 1184\n", "2448 7020\n", "7112 6068\n", "368 1480\n", "60 5992\n", "2384 5816\n", "608 14340\n", "11176 6176\n", "8032 4196\n", "12456 13968\n", "4408 4712\n", "3980 3172\n", "12192 11364\n", "4700 12684\n", "10104 3152\n", "2924 14092\n", "11784 8976\n", "1708 4624\n", "5336 13788\n", "1624 9992\n", "12792 11664\n", "2992 12924\n", "1416 9628\n", "12952 7532\n", "13232 4804\n", "6012 14408\n", "4664 5604\n", "4284 6100\n", "3216 696\n", "9752 14444\n", "9028 11076\n", "12952 7468\n", "4428 5728\n", "12364 6300\n", "6276 8128\n", "6360 13432\n", "5576 6364\n", "7708 14696\n", "3944 7568\n", "9140 6024\n", "12180 1908\n", "3432 13044\n", "8240 10576\n", "9092 12180\n", "10088 9704\n", "3712 11088\n", "5840 7000\n", "3944 6368\n", "12024 5908\n", "5528 7400\n", "2624 7972\n", "792 12512\n", "6700 13740\n", "108 13408\n", "5528 4704\n", "7152 1592\n", "11272 12876\n", "9164 1856\n", "GF1_WFV4_E116.9_N6.8_20151011_L2A0001094742.tif\n", "1156 3980\n", "12984 4268\n", "2880 6716\n", "4760 2208\n", "3468 9924\n", "11344 7732\n", "3064 2604\n", "2648 7468\n", "7812 6192\n", "11436 1608\n", "11916 4968\n", "2068 3032\n", "5200 13988\n", "4048 6228\n", "8992 664\n", "13044 9676\n", "8624 13888\n", "7116 3728\n", "5816 4084\n", "6608 3016\n", "5004 5832\n", "11264 4016\n", "6176 76\n", "2504 13148\n", "12968 14596\n", "8876 1928\n", "13732 1524\n", "11376 14320\n", "948 4704\n", "3880 13356\n", "6876 592\n", "11152 1812\n", "10892 1640\n", "12424 12760\n", "8740 13056\n", "10240 13100\n", "8920 12236\n", "8736 2024\n", "9988 12400\n", "116 4224\n", "13376 896\n", "312 3504\n", "396 2836\n", "7832 6032\n", "3636 5008\n", "5100 8252\n", "5332 11432\n", "11480 13768\n", "7860 2552\n", "12216 6312\n", "10104 9048\n", "5524 11932\n", "5192 1028\n", "704 11708\n", "660 11292\n", "12760 10120\n", "5872 9164\n", "8396 8668\n", "7524 14776\n", "5104 1696\n", "7488 14032\n", "6964 996\n", "12304 5172\n", "9740 5472\n", "9880 9236\n", "11812 2628\n", "2496 10324\n", "4380 6160\n", "1460 8708\n", "492 5048\n", "2452 13108\n", "1080 12232\n", "10128 13688\n", "10096 9816\n", "9764 5816\n", "11544 12096\n", "7288 4668\n", "5396 8548\n", "10280 6880\n", "6996 10104\n", "1192 7000\n", "7776 3076\n", "4312 14772\n", "5096 44\n", "1844 10724\n", "12984 9212\n", "868 632\n", "1044 13404\n", "1980 8420\n", "6400 10080\n", "10360 12432\n", "2820 8804\n", "3564 8476\n", "4812 7756\n", "6428 3696\n", "3840 13472\n", "9548 368\n", "11508 3320\n", "6628 14380\n", "10444 12572\n", "GF1_WFV4_E118.5_N15.1_20160716_L2A0001705819.tif\n", "10020 16516\n", "13520 5868\n", "2084 12940\n", "7272 9560\n", "12300 2448\n", "11180 10836\n", "8332 15072\n", "7928 15728\n", "868 10576\n", "2760 13468\n", "10876 3660\n", "4740 5876\n", "6812 16644\n", "9876 5548\n", "4380 8252\n", "5628 8332\n", "12444 14136\n", "10324 14460\n", "7568 6192\n", "6620 5964\n", "10948 10064\n", "8268 4548\n", "11264 1480\n", "5372 9420\n", "6016 912\n", "12344 15404\n", "7992 24\n", "13208 14328\n", "13552 4064\n", "2392 6064\n", "316 3592\n", "13072 17204\n", "13488 14440\n", "4720 13192\n", "9720 4144\n", "9388 11840\n", "1252 3008\n", "14176 5208\n", "1616 11896\n", "13284 15876\n", "3004 9748\n", "8488 5928\n", "12896 11124\n", "7892 8224\n", "9204 3412\n", "10216 11060\n", "220 564\n", "968 9852\n", "10332 10868\n", "9924 6816\n", "10588 16420\n", "652 16056\n", "10176 7020\n", "11128 816\n", "8728 16792\n", "12508 4448\n", "6608 1052\n", "1508 11664\n", "2644 7000\n", "12400 2328\n", "3348 1108\n", "7312 7652\n", "10896 10184\n", "4268 10664\n", "568 892\n", "1168 12804\n", "8236 10104\n", "10180 960\n", "1844 9824\n", "14016 4044\n", "376 1240\n", "11000 6804\n", "12144 1656\n", "5496 16952\n", "13768 7840\n", "3108 17000\n", "2984 1240\n", "3560 3584\n", "3840 1320\n", "3140 11796\n", "9728 5244\n", "9988 12788\n", "14332 14620\n", "4424 6268\n", "14260 3384\n", "12752 11280\n", "9928 3072\n", "9228 800\n", "6712 1612\n", "13136 15640\n", "11096 5964\n", "11560 5664\n", "4532 17704\n", "10784 11560\n", "13516 7224\n", "10632 12352\n", "4144 17120\n", "4776 10332\n", "8964 15992\n", "11976 9644\n", "GF1_WFV4_E121.6_N23.5_20140130_L2A0000417191.tif\n", "14072 13100\n", "11796 10220\n", "12416 4740\n", "2384 12456\n", "10004 9720\n", "8272 13516\n", "1956 13288\n", "5368 11232\n", "8188 9344\n", "5548 1284\n", "1744 12732\n", "12648 10384\n", "932 2004\n", "13408 10100\n", "14112 12836\n", "5220 10060\n", "8584 14204\n", "13996 6820\n", "3560 14436\n", "944 13912\n", "8248 1936\n", "4664 6600\n", "3588 14188\n", "14144 7700\n", "12696 8428\n", "988 10792\n", "10792 1860\n", "12944 476\n", "272 8804\n", "12412 2752\n", "8208 8236\n", "4012 5496\n", "1392 13820\n", "9828 13400\n", "5452 6204\n", "8528 10112\n", "9920 14856\n", "3636 15020\n", "13192 4508\n", "5204 11700\n", "6896 14452\n", "6468 8628\n", "12928 12464\n", "6232 636\n", "6288 13668\n", "13512 4864\n", "8880 5988\n", "5096 7064\n", "5084 10872\n", "8752 4260\n", "12452 8944\n", "12480 14432\n", "11892 7508\n", "12200 14756\n", "3616 4848\n", "10348 13156\n", "784 1084\n", "10484 11404\n", "7080 152\n", "8220 5540\n", "180 8564\n", "3116 11592\n", "3856 356\n", "8900 1188\n", "11148 5096\n", "10596 12036\n", "3044 15180\n", "7612 3624\n", "2520 11736\n", "1716 9420\n", "236 9304\n", "12740 9284\n", "9808 4028\n", "8992 15104\n", "12292 10808\n", "2480 7560\n", "1008 4328\n", "2248 4956\n", "612 11168\n", "1964 14412\n", "12064 3964\n", "13824 14364\n", "5848 14840\n", "8844 12212\n", "1396 12344\n", "3424 13212\n", "6608 3620\n", "13136 15260\n", "688 4288\n", "8052 11336\n", "1248 10260\n", "13116 1324\n", "216 12272\n", "10252 13856\n", "11520 1760\n", "9300 4440\n", "2680 9756\n", "12684 2800\n", "10104 4224\n", "1784 1084\n", "GF1_WFV4_E126.2_N30.2_20160817_L2A0001769953.tif\n", "732 964\n", "4980 1928\n", "9900 10652\n", "1384 3300\n", "2088 7696\n", "1048 7136\n", "8492 2152\n", "10988 15128\n", "6692 7768\n", "3700 2220\n", "6560 1540\n", "6132 7432\n", "3652 13388\n", "2144 11180\n", "6532 13856\n", "12996 13588\n", "12940 15360\n", "1688 9232\n", "13264 3908\n", "6312 4200\n", "10548 1852\n", "6628 4460\n", "7152 14316\n", "6812 8124\n", "4792 428\n", "12412 1392\n", "12152 13896\n", "7768 11968\n", "13384 3536\n", "1248 4828\n", "8828 3312\n", "4428 15768\n", "10884 12128\n", "920 2732\n", "5244 7096\n", "1928 7644\n", "6592 5844\n", "2508 5124\n", "14332 12728\n", "5268 3176\n", "4396 10776\n", "12452 2120\n", "14152 4748\n", "9640 2052\n", "72 15288\n", "14192 6428\n", "14488 12760\n", "12636 14980\n", "10436 6540\n", "2236 5832\n", "8736 364\n", "1304 5920\n", "1820 2048\n", "2244 9644\n", "8184 5216\n", "5696 3552\n", "11008 5688\n", "2664 1304\n", "7100 5692\n", "12572 10464\n", "14132 9064\n", "14124 12880\n", "13368 6648\n", "6372 13200\n", "10476 5728\n", "6312 7380\n", "5404 15736\n", "9404 11608\n", "10392 1856\n", "6964 5664\n", "4936 9152\n", "9240 9764\n", "10932 10700\n", "5752 1240\n", "3712 6952\n", "7284 2604\n", "12556 11760\n", "10416 1188\n", "72 7124\n", "8220 13252\n", "5260 9624\n", "4696 15908\n", "3636 2724\n", "8208 644\n", "5388 4284\n", "5420 10016\n", "2496 15820\n", "356 6288\n", "9272 12372\n", "1448 8872\n", "1096 10816\n", "4060 12516\n", "6356 3396\n", "12496 13304\n", "12440 5264\n", "5764 10224\n", "8828 2216\n", "4300 12124\n", "6548 2704\n", "28 10972\n", "GF1_WFV4_E132.4_N53.2_20160507_L2A0001572627.tif\n", "10960 6824\n", "13284 7992\n", "11864 8796\n", "12556 15540\n", "7456 2912\n", "9732 9208\n", "9008 6380\n", "8928 10712\n", "11988 12888\n", "6236 16620\n", "12776 14104\n", "13872 15716\n", "1612 15556\n", "7340 1280\n", "7336 1404\n", "11420 8576\n", "12748 2716\n", "10084 7848\n", "5492 11264\n", "7368 14400\n", "3708 5840\n", "9476 12836\n", "5132 11332\n", "13328 14316\n", "5576 10984\n", "7768 5004\n", "10360 9840\n", "13952 16348\n", "9860 3012\n", "524 16336\n", "7152 15560\n", "7432 1852\n", "5328 11576\n", "12784 13472\n", "15044 1064\n", "6132 7868\n", "5384 4016\n", "2984 6588\n", "11956 3268\n", "10596 14244\n", "3452 15892\n", "6776 6332\n", "5864 5008\n", "4624 7984\n", "9264 15428\n", "5228 248\n", "7016 6100\n", "14940 4352\n", "5936 1948\n", "12244 344\n", "5552 6716\n", "14184 8636\n", "14660 10252\n", "4516 14664\n", "2704 10828\n", "8980 13664\n", "48 8108\n", "4192 4280\n", "10428 88\n", "6072 8180\n", "1180 4296\n", "2472 2576\n", "944 16012\n", "4472 2996\n", "10224 6800\n", "3556 9248\n", "6596 15956\n", "4044 8524\n", "1392 13300\n", "1592 1780\n", "5844 13540\n", "1860 676\n", "14680 4264\n", "13324 4800\n", "13856 2184\n", "2020 3016\n", "14772 2136\n", "13332 9828\n", "15220 14936\n", "1760 6480\n", "2876 3088\n", "6016 11940\n", "14576 15544\n", "10416 412\n", "9276 5520\n", "7632 10900\n", "5500 11956\n", "728 2536\n", "11784 3148\n", "2952 12700\n", "7520 1920\n", "14132 12576\n", "10744 6892\n", "2268 3496\n", "14524 11448\n", "12936 4812\n", "1604 2912\n", "1792 11052\n", "5720 8112\n", "9768 3272\n", "GF1_WFV4_E139.5_N35.2_20140407_L2A0000966128.tif\n", "9648 10384\n", "9876 8800\n", "8132 10156\n", "3248 13396\n", "7604 6684\n", "9564 6656\n", "892 13568\n", "7012 7992\n", "2784 6312\n", "5052 14540\n", "4596 3088\n", "4976 544\n", "2508 10644\n", "552 11816\n", "12852 11212\n", "7616 14092\n", "9652 1428\n", "7468 7676\n", "1860 3360\n", "2096 8208\n", "9208 15036\n", "5512 6888\n", "2468 6152\n", "10024 8884\n", "4236 1588\n", "3816 6340\n", "6436 1848\n", "1656 7780\n", "5312 4500\n", "8052 12556\n", "4928 996\n", "3336 7432\n", "13044 2376\n", "10420 15028\n", "6756 2752\n", "2940 6564\n", "1328 5912\n", "9012 3560\n", "40 14712\n", "7352 1292\n", "11968 10772\n", "12204 13536\n", "7240 14896\n", "3368 3732\n", "8888 12036\n", "5264 5848\n", "148 6556\n", "11272 14476\n", "7376 11980\n", "8040 1876\n", "152 12840\n", "7240 3584\n", "14200 864\n", "872 7544\n", "12224 13008\n", "10176 8376\n", "8588 14628\n", "9740 12664\n", "11024 15300\n", "8308 5940\n", "10092 8192\n", "9884 1788\n", "6628 1984\n", "6772 3824\n", "8980 2560\n", "9332 6476\n", "1596 2892\n", "9416 2384\n", "9272 2860\n", "7956 3448\n", "11344 12952\n", "12840 9540\n", "5948 12840\n", "3704 3736\n", "10348 14292\n", "4652 6684\n", "11908 12856\n", "13404 10680\n", "12404 780\n", "4512 5904\n", "14096 14696\n", "8136 1392\n", "10800 10896\n", "12000 908\n", "7180 3616\n", "6804 10636\n", "13456 8492\n", "468 4616\n", "2516 12408\n", "4148 7632\n", "7724 7972\n", "11408 9836\n", "14420 1660\n", "9240 13712\n", "912 5940\n", "12764 14800\n", "11104 1756\n", "5456 1700\n", "1408 4616\n", "6752 11764\n", "GF1_WFV4_E168.7_S13.5_20150314_L2A0000696289.tif\n", "3356 6956\n", "2636 2920\n", "13708 1112\n", "4988 4156\n", "8884 15984\n", "3428 3992\n", "12776 15912\n", "1836 3524\n", "1452 988\n", "9692 3032\n", "11556 5336\n", "2032 8432\n", "5020 17456\n", "1400 2188\n", "10540 11084\n", "13180 3760\n", "10904 12496\n", "5900 7068\n", "8584 17072\n", "10004 7244\n", "5332 2940\n", "6112 13732\n", "6868 3352\n", "3644 19144\n", "6728 804\n", "6400 3780\n", "736 19744\n", "11984 7720\n", "952 9980\n", "952 736\n", "5892 3532\n", "14232 19884\n", "2828 432\n", "6680 15888\n", "508 14828\n", "3188 14140\n", "7484 3956\n", "9680 3624\n", "13108 5284\n", "9740 6544\n", "6180 20176\n", "4140 13588\n", "13476 11008\n", "1848 11828\n", "8104 7196\n", "13824 9120\n", "4920 2004\n", "6052 17980\n", "14384 16372\n", "960 16788\n", "4644 10588\n", "2972 9704\n", "10680 19208\n", "12704 2148\n", "9404 4136\n", "432 11372\n", "14304 1432\n", "6876 12472\n", "14344 14100\n", "10588 6980\n", "12588 5864\n", "1944 704\n", "796 132\n", "1116 15244\n", "7424 9780\n", "4536 3148\n", "8408 1744\n", "6508 548\n", "11256 15916\n", "4836 8600\n", "10404 2556\n", "10700 3376\n", "13932 4084\n", "256 16684\n", "3208 2552\n", "10216 3756\n", "7716 9888\n", "1992 9544\n", "4144 5648\n", "12448 19040\n", "2708 16064\n", "2808 13412\n", "10712 3328\n", "6692 17492\n", "11492 124\n", "7092 13764\n", "2992 748\n", "10884 19492\n", "12952 18196\n", "12272 2112\n", "6848 19772\n", "7680 520\n", "7780 15728\n", "700 7340\n", "4932 7536\n", "5168 28\n", "6124 16868\n", "2260 18652\n", "7796 12284\n", "5428 5992\n", "GF1_WFV4_W43.1_S6.4_20140506_L2A0000227312.tif\n", "9492 10268\n", "12812 3492\n", "7548 3376\n", "4948 10940\n", "1856 2100\n", "8232 2028\n", "11216 2100\n", "5344 4092\n", "6044 9100\n", "13140 6728\n", "3828 1852\n", "7292 11152\n", "11772 3816\n", "708 5556\n", "10972 11292\n", "2528 13648\n", "5624 3716\n", "12888 12780\n", "11448 4996\n", "2504 8264\n", "10276 2920\n", "2996 8864\n", "1552 2996\n", "12428 12396\n", "7728 13192\n", "8668 11108\n", "10912 11716\n", "10064 3948\n", "592 5456\n", "1492 6312\n", "576 2908\n", "9056 9984\n", "7844 188\n", "1284 14176\n", "308 8948\n", "8528 13384\n", "2876 1488\n", "996 13440\n", "9860 9672\n", "4728 5600\n", "6164 3336\n", "6628 2004\n", "6348 13564\n", "9484 2952\n", "852 8864\n", "6108 2376\n", "1292 9052\n", "7528 1496\n", "1436 4024\n", "2748 7136\n", "32 14000\n", "3460 9372\n", "3832 11584\n", "9360 6424\n", "496 12064\n", "5916 9404\n", "4640 596\n", "12112 11136\n", "10916 1096\n", "7192 13840\n", "3532 1952\n", "11584 3440\n", "12168 4712\n", "6396 7448\n", "3768 13680\n", "28 2184\n", "4984 7756\n", "5752 6728\n", "5376 14480\n", "2128 10040\n", "9612 4948\n", "1624 11572\n", "10196 2144\n", "4520 2220\n", "5864 11500\n", "10640 8376\n", "2644 6264\n", "3812 6320\n", "11568 2956\n", "6548 3640\n", "10756 5836\n", "1024 13116\n", "8976 12996\n", "13528 2540\n", "388 1696\n", "6408 11084\n", "10248 11612\n", "6860 64\n", "13176 11008\n", "10928 10304\n", "4324 5264\n", "6792 6640\n", "10520 13116\n", "11828 7032\n", "4880 14728\n", "5776 8364\n", "2700 10808\n", "8688 8712\n", "7364 11364\n", "5812 10544\n", "GF1_WFV4_W45.2_S19.9_20140510_L2A0000222535.tif\n", "5680 3724\n", "7728 4676\n", "7244 13404\n", "2188 6932\n", "6524 2700\n", "8844 2736\n", "6172 1972\n", "10612 3588\n", "8228 9064\n", "12880 12624\n", "8448 12380\n", "8868 3920\n", "12376 13404\n", "6400 5244\n", "9424 10524\n", "10144 10468\n", "3012 4332\n", "2032 4416\n", "10228 4300\n", "4452 12400\n", "4092 6352\n", "5612 8000\n", "136 8684\n", "1200 14948\n", "700 11500\n", "1552 6176\n", "3208 2824\n", "2152 7056\n", "6096 1824\n", "11164 7436\n", "9744 13780\n", "9488 12684\n", "1904 8384\n", "4056 5072\n", "2564 11416\n", "8560 2532\n", "7652 6796\n", "10696 1964\n", "12912 7804\n", "11256 14364\n", "13384 4936\n", "2204 13196\n", "3708 11664\n", "988 4240\n", "12196 6624\n", "12996 7440\n", "8316 1480\n", "3440 1756\n", "7304 3740\n", "7544 4640\n", "5176 3636\n", "7192 7244\n", "10816 3208\n", "7860 13400\n", "11604 1592\n", "12684 6728\n", "2580 13032\n", "4452 11608\n", "11644 13748\n", "1352 5716\n", "212 3080\n", "3528 13668\n", "7800 8932\n", "12000 6596\n", "3316 2984\n", "6576 2724\n", "10372 4500\n", "12524 2312\n", "10012 8516\n", "8952 14376\n", "8540 11740\n", "7304 8532\n", "11176 14836\n", "10124 6688\n", "7940 196\n", "9292 7548\n", "8332 10092\n", "2528 8372\n", "12904 11668\n", "3528 264\n", "11952 180\n", "12348 3628\n", "13384 4468\n", "7072 2844\n", "13256 4892\n", "10648 9924\n", "11888 2200\n", "2668 12732\n", "11064 13528\n", "5104 5660\n", "12412 2692\n", "1864 13444\n", "980 10124\n", "10800 14744\n", "12568 12528\n", "10892 13016\n", "5956 4936\n", "1588 9220\n", "6308 2140\n", "11876 11336\n", "GF1_WFV4_W56.8_S10.1_20140610_L2A0000253154.tif\n", "12220 5888\n", "1908 11612\n", "4388 14244\n", "3916 7428\n", "10256 1584\n", "10760 1872\n", "3464 14700\n", "5240 11048\n", "7632 2908\n", "3912 14956\n", "11456 10288\n", "9264 6900\n", "4376 516\n", "10068 2324\n", "6484 7300\n", "4052 1796\n", "13152 14612\n", "7900 5084\n", "3912 3000\n", "10016 15100\n", "5912 10460\n", "9272 12436\n", "13428 984\n", "180 11748\n", "8208 2404\n", "9812 6820\n", "3804 7456\n", "4552 5696\n", "6252 15156\n", "2916 2472\n", "7312 3532\n", "5344 12544\n", "9832 9068\n", "9708 14284\n", "12124 13660\n", "5388 3032\n", "5396 14160\n", "4120 7240\n", "3752 4924\n", "776 12652\n", "10684 7996\n", "1340 12056\n", "11104 11000\n", "7344 5936\n", "10424 14964\n", "11044 10044\n", "13272 12992\n", "168 13264\n", "2816 8508\n", "6980 6700\n", "2732 10168\n", "2748 1148\n", "8852 8836\n", "8800 4740\n", "3856 13864\n", "11180 13264\n", "3448 10356\n", "8912 8032\n", "8340 1188\n", "8640 2960\n", "3672 1256\n", "12256 1872\n", "10324 3848\n", "1980 464\n", "8660 1236\n", "10124 5244\n", "3696 10800\n", "8180 3696\n", "5260 5352\n", "4764 3620\n", "11824 2548\n", "4048 15120\n", "3016 5448\n", "6608 13816\n", "6920 5132\n", "628 5084\n", "7640 9456\n", "3704 1576\n", "1476 10328\n", "10208 13720\n", "8108 1452\n", "1336 6860\n", "4476 8232\n", "504 7364\n", "6000 9244\n", "8412 9356\n", "3616 3452\n", "6764 13104\n", "13132 1204\n", "2236 8012\n", "5732 7284\n", "9036 2644\n", "12772 14160\n", "3980 11208\n", "1772 4420\n", "13176 11284\n", "5988 8508\n", "13012 10268\n", "12584 5852\n", "10796 1364\n", "GF1_WFV4_W67.3_N18.5_20140801_L2A0000292229.tif\n", "7468 7288\n", "7012 904\n", "10600 13616\n", "1044 5968\n", "7368 12784\n", "10392 6264\n", "9012 12752\n", "10548 10560\n", "12692 5716\n", "10752 3288\n", "8892 10332\n", "2384 9624\n", "1188 9120\n", "7152 10432\n", "3260 1468\n", "744 9016\n", "9168 9900\n", "12360 2264\n", "11268 6784\n", "13128 14764\n", "9072 4244\n", "6928 12972\n", "3472 11924\n", "6704 9976\n", "12456 2740\n", "7096 2028\n", "4688 5948\n", "1264 6728\n", "3920 4780\n", "13356 10048\n", "6924 2316\n", "12628 4724\n", "7364 7260\n", "7140 7656\n", "8780 14452\n", "4412 12248\n", "10040 10572\n", "3308 11596\n", "9048 952\n", "3344 1060\n", "1956 3936\n", "5572 8416\n", "3568 3068\n", "876 8920\n", "56 14032\n", "4164 11744\n", "6296 13308\n", "7620 6252\n", "4836 9564\n", "796 11108\n", "10124 9020\n", "5552 12204\n", "6208 7328\n", "12276 10320\n", "4992 13728\n", "5896 1060\n", "4572 4348\n", "12136 14140\n", "5180 8440\n", "10620 152\n", "5088 4896\n", "4136 3456\n", "7616 2892\n", "6084 788\n", "7124 7856\n", "5676 13248\n", "11868 13728\n", "9520 6104\n", "2776 9408\n", "2440 1856\n", "8504 716\n", "8264 2848\n", "9560 1668\n", "13004 9492\n", "8556 708\n", "6284 7824\n", "268 14964\n", "5604 2128\n", "2364 12264\n", "7936 356\n", "10476 11244\n", "552 13680\n", "3096 7004\n", "1876 10128\n", "6664 12576\n", "10860 2944\n", "2668 14832\n", "11636 5808\n", "4716 13260\n", "4300 8736\n", "5764 14336\n", "8560 8720\n", "12468 5596\n", "11640 4196\n", "13328 11684\n", "8504 828\n", "8856 10132\n", "9736 12112\n", "7168 5924\n", "116 6448\n", "GF1_WFV4_W84.3_N38.5_20140523_L2A0000356168.tif\n", "10672 3828\n", "5344 8144\n", "1216 10952\n", "4912 5784\n", "6872 7664\n", "8508 6000\n", "12200 1052\n", "8796 5676\n", "10024 11628\n", "928 2744\n", "7016 10076\n", "13056 10728\n", "2792 4124\n", "11096 1760\n", "8832 9508\n", "10368 5368\n", "14180 10140\n", "176 212\n", "2580 7628\n", "11660 10860\n", "1452 4020\n", "8760 8608\n", "12500 13200\n", "7820 15092\n", "3388 6120\n", "5460 9100\n", "13516 1768\n", "7252 11064\n", "11580 3712\n", "6236 804\n", "7144 2856\n", "13496 6992\n", "9156 9948\n", "1776 7924\n", "200 3640\n", "11160 8216\n", "10240 5180\n", "13172 6032\n", "9568 12440\n", "8480 2008\n", "9668 14756\n", "11144 10900\n", "1224 1772\n", "8524 4508\n", "4412 5244\n", "10052 4868\n", "13648 8400\n", "5428 14564\n", "13168 12328\n", "7068 9120\n", "12916 8364\n", "6752 11860\n", "3616 2868\n", "3928 15332\n", "10196 7780\n", "3716 6928\n", "2308 10552\n", "9108 10792\n", "6196 12356\n", "5860 2264\n", "7600 1492\n", "6792 1100\n", "3808 9776\n", "10848 6212\n", "11248 4156\n", "9260 7220\n", "8868 10864\n", "5660 14720\n", "11560 584\n", "10796 8672\n", "10052 12244\n", "9256 14080\n", "7844 1124\n", "6940 10036\n", "4604 10540\n", "7688 136\n", "12476 13620\n", "10416 5044\n", "9596 1252\n", "7100 2844\n", "1840 1872\n", "10700 9380\n", "13232 8036\n", "5780 11272\n", "9864 9236\n", "4904 5600\n", "3092 1696\n", "8656 4672\n", "12044 2968\n", "6480 8468\n", "2528 10048\n", "6004 10468\n", "9192 13728\n", "3072 2704\n", "6824 6464\n", "4152 10060\n", "6260 6324\n", "2116 7968\n", "11392 4248\n", "10312 584\n", "GF1_WFV4_W97.5_N38.5_20140517_L2A0000244685.tif\n", "13940 2956\n", "3316 14336\n", "7812 7588\n", "3824 1800\n", "8252 9928\n", "10640 7396\n", "1792 6112\n", "10268 3844\n", "3212 14220\n", "3344 5172\n", "10528 2828\n", "6024 11932\n", "4340 12128\n", "11304 3688\n", "2004 548\n", "5088 5920\n", "4960 9636\n", "10492 828\n", "13404 11008\n", "6852 15132\n", "4516 11692\n", "1532 12908\n", "6256 600\n", "12456 12012\n", "1868 2744\n", "5772 13624\n", "7388 2476\n", "8616 4668\n", "8152 8840\n", "2188 1480\n", "9500 5400\n", "6476 2280\n", "7548 12720\n", "4984 3544\n", "13252 1120\n", "2500 8984\n", "13076 1808\n", "10096 264\n", "10332 9504\n", "13004 9284\n", "6728 1392\n", "1796 3052\n", "6028 10096\n", "12020 3716\n", "9872 668\n", "10600 1996\n", "12592 7108\n", "10864 9108\n", "13204 8744\n", "14016 6180\n", "920 4100\n", "7308 14520\n", "7456 4200\n", "84 2536\n", "1864 3488\n", "6216 2764\n", "13944 14824\n", "5216 11328\n", "3584 3144\n", "5060 12200\n", "10168 4308\n", "11292 14392\n", "5880 2480\n", "14288 4448\n", "732 7108\n", "2240 8128\n", "11452 608\n", "11672 15232\n", "7040 9952\n", "416 6396\n", "11540 11388\n", "13632 8172\n", "1140 1920\n", "724 6128\n", "10340 11440\n", "6868 11856\n", "9572 6192\n", "5064 8024\n", "12068 4308\n", "420 12192\n", "2404 2520\n", "1740 5916\n", "6104 4320\n", "2200 9340\n", "13872 5948\n", "8140 8780\n", "6316 636\n", "9884 5660\n", "14236 12144\n", "1632 6208\n", "7324 5452\n", "13764 1980\n", "2304 13832\n", "11444 11144\n", "7868 8608\n", "2136 3624\n", "12320 14100\n", "1332 11100\n", "3552 3636\n", "504 13100\n", "GF1_WFV4_W100.3_N45.1_20140522_L2A0000244682.tif\n", "9320 2856\n", "2024 10340\n", "9428 11496\n", "13224 4776\n", "7392 14452\n", "8852 1344\n", "488 1448\n", "8416 11876\n", "14564 5952\n", "8016 7612\n", "268 10772\n", "14552 2908\n", "616 15904\n", "2640 1316\n", "1668 5516\n", "404 14648\n", "5044 2968\n", "9632 4300\n", "10892 848\n", "7288 3656\n", "1748 1168\n", "5732 2968\n", "2496 9264\n", "2624 6436\n", "452 14908\n", "13588 8440\n", "6568 11048\n", "14200 12616\n", "256 15816\n", "488 14316\n", "13492 7840\n", "12744 5448\n", "5816 972\n", "9356 7432\n", "10560 6772\n", "2952 13236\n", "1152 5232\n", "5232 40\n", "14304 10808\n", "3836 1428\n", "1952 12120\n", "1944 7376\n", "1748 5676\n", "7244 5716\n", "11236 11592\n", "10880 4536\n", "6976 7504\n", "14012 15276\n", "468 4544\n", "14488 4072\n", "14148 10616\n", "13912 9280\n", "6704 14144\n", "7324 9512\n", "14660 5852\n", "7892 14644\n", "10080 4184\n", "496 10236\n", "3364 2540\n", "10908 11844\n", "6124 14444\n", "10948 14832\n", "12104 868\n", "13008 1768\n", "13116 14936\n", "7712 12692\n", "2580 10740\n", "5476 9532\n", "4928 5832\n", "7148 1648\n", "4548 4640\n", "10360 7080\n", "5492 15448\n", "3960 5256\n", "11476 12524\n", "14744 6216\n", "9968 15020\n", "3824 6752\n", "8368 15520\n", "12252 8188\n", "1464 7508\n", "6352 10036\n", "4040 11600\n", "6628 4112\n", "12072 1980\n", "3232 4088\n", "3960 15200\n", "9632 5004\n", "1680 8940\n", "10216 6208\n", "3432 7364\n", "1088 15132\n", "488 7816\n", "13580 6632\n", "2464 4748\n", "12240 14608\n", "9696 5504\n", "3632 10252\n", "2696 15844\n", "3544 2520\n", "GF1_WFV4_W155.2_N20.2_20160721_L2A0001718813.tif\n", "7148 8476\n", "4256 11156\n", "3984 11992\n", "13816 2972\n", "7232 14748\n", "1128 7556\n", "3192 4176\n", "9344 3716\n", "8460 6380\n", "2476 9056\n", "8532 4388\n", "3444 10156\n", "9312 12384\n", "9744 12032\n", "4152 12616\n", "1720 692\n", "14100 1376\n", "1956 1732\n", "980 14460\n", "13300 14116\n", "9848 8744\n", "1992 5496\n", "9856 6324\n", "11068 80\n", "4256 2312\n", "1548 5264\n", "3568 13312\n", "6588 4904\n", "10956 11216\n", "3292 5140\n", "14096 32\n", "4412 11768\n", "1272 11120\n", "12268 13884\n", "9136 3256\n", "8444 11600\n", "3936 8524\n", "10760 11500\n", "11748 344\n", "4980 4272\n", "11616 8624\n", "8056 6388\n", "576 2844\n", "7744 9596\n", "12436 1340\n", "11572 3804\n", "10060 9960\n", "8552 11216\n", "13784 3004\n", "11420 1748\n", "492 7164\n", "4416 5592\n", "6644 8772\n", "3084 2908\n", "11880 4492\n", "12760 14392\n", "2064 12708\n", "8740 11368\n", "9828 1616\n", "3856 692\n", "8360 4872\n", "1408 13664\n", "4608 11564\n", "4300 2332\n", "10628 14340\n", "1936 14900\n", "8296 4944\n", "9060 6056\n", "11008 1800\n", "13424 2944\n", "7492 12148\n", "6108 13060\n", "12828 5768\n", "2068 1248\n", "8816 2968\n", "6168 13200\n", "8604 4800\n", "428 6832\n", "11900 1192\n", "2136 8744\n", "2480 9500\n", "2080 14436\n", "6268 1192\n", "6696 14668\n", "4744 5376\n", "13124 6696\n", "8088 11524\n", "8556 2240\n", "12040 12404\n", "12180 9980\n", "13560 12816\n", "256 10576\n", "3020 4708\n", "4108 13832\n", "8616 14384\n", "12960 5120\n", "12264 14884\n", "7732 4428\n", "6544 11016\n", "632 1432\n"]}], "source": ["for tname in flist:\n", "    xa  = riox.open_rasterio(tname)\n", "    bn = tname.name\n", "    print(bn)\n", "    n_try=0\n", "    while True:\n", "        n_try += 1\n", "        if n_try > 100:\n", "            break\n", "        yoff = random.randint(edge_size, xa.shape[1]-1-p_size-edge_size)\n", "        xoff = random.randint(edge_size, xa.shape[2]-1-p_size-edge_size)\n", "        yoff = yoff//4*4\n", "        xoff = xoff//4*4\n", "        print(yoff, xoff)\n", "        # print(df)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["for tname in flist:\n", "    xa  = riox.open_rasterio(tname)\n", "    bn = tname.name\n", "    # xa = xr[list(xr.keys())[0]]\n", "    df = pd.DataFrame(data={'name':[],'yoff':[],'xoff':[]})\n", "    # crs = xr.rio.crs\n", "    n_try=0\n", "    while True:\n", "        n_try += 1\n", "        if n_try > 100:\n", "            break\n", "        yoff = random.randint(edge_size, xa.shape[1]-1-p_size-edge_size)\n", "        xoff = random.randint(edge_size, xa.shape[2]-1-p_size-edge_size)\n", "        yoff = yoff//4*4\n", "        xoff = xoff//4*4\n", "        # print(yoff, xoff)\n", "        # print(df)\n", "        oname = oroot\n", "        os.makedirs(oname.__str__(),exist_ok=True)\n", "        oname = oname/f'{bn}-{int(xa.y[yoff]+half_tr)}-{int(xa.x[xoff]-half_tr)}.tif'\n", "        if oname.exists():\n", "            continue\n", "        if len(df)>0:\n", "            distance = ((df.xoff-xoff)**2+(df.yoff-yoff)**2)**0.5\n", "            # print(distance)\n", "            if distance.min() < 2700:\n", "                continue\n", "        patch = xa[:, yoff:yoff+p_size, xoff:xoff+p_size].compute()\n", "        mask = (patch == 0)\n", "        # 沿着所有维度计算掩码中 True 值的数量\n", "        count_zeros = mask.sum()\n", "        if count_zeros > 40:\n", "            continue\n", "        \n", "        # patch.rio.write_crs(crs, inplace=True)\n", "        # patch.rio.to_raster(oname,driver='COG',RESAMPLING='NEAREST')\n", "        patch.rio.to_raster(oname.__str__(),driver='COG')\n", "        df.loc[len(df)] = {'name':bn,'yoff':yoff,'xoff':xoff}\n", "        if len(df)>=4:\n", "            break\n", "        # print(n_try)\n", "            "]}, {"cell_type": "markdown", "metadata": {}, "source": ["随机分训练测试"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "ims = oroot.rglob('*.tif')\n", "df_im = pd.DataFrame(data={'imp':list(ims)})\n", "df_val = df_im.sample(frac=0.1)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import os\n", "for fn in df_val.imp:\n", "    fn = pathlib.Path(fn)\n", "    os.renames(fn,fn.parent.parent.parent/'val'/fn.parent.parent.name/fn.parent.name/fn.name)\n", "    # print(fn.parent.parent.parent/'val'/fn.parent.parent.name/fn.parent.name/fn.name)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["导出label图块"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["# oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg')\n", "oroot = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image')\n", "ims = list(oroot.rglob('*.tif'))\n", "stdl_root = pathlib.Path('/mnt/Netapp/yinry/CloudDetection/Dataset_wfv/label')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["[PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E104.3_N12.9_20140522_L2A0000356326-1464480-324848.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E104.3_N12.9_20140522_L2A0000356326-1390624-296240.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E104.8_N51.3_20150617_L2A0000979951-5680656-437008.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E115.1_N49.6_20140423_L2A0000314913-5565760-431696.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E115.1_N49.6_20140423_L2A0000314913-5552256-380816.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E120.2_N53.0_20150705_L2A0000939154-5950672-212112.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_E120.2_N53.0_20150705_L2A0000939154-5902544-197200.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W48.2_S5.6_20140506_L2A0000228109-9331648-858960.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W48.2_S5.6_20140506_L2A0000228109-9323840-749520.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W50.9_S17.4_20140506_L2A0000227311-8062096-533760.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W50.9_S17.4_20140506_L2A0000227311-8012752-552000.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W59.9_S17.4_20140528_L2A0000238651-8102384-224128.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W59.9_S17.4_20140528_L2A0000238651-8048688-249664.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W62.5_S10.6_20140610_L2A0000253147-8772432-578544.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W62.5_S10.6_20140610_L2A0000253147-8874896-625520.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W83.1_N41.3_20140820_L2A0000320017-4594416-330752.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W85.7_N36.3_20140522_L2A0000356325-4096768-636048.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W85.7_N36.3_20140522_L2A0000356325-3996416-503120.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W90.3_N36.3_20140821_L2A0000320015-3971504-738224.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W90.3_N36.3_20140821_L2A0000320015-4000048-624624.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W94.4_N41.3_20140425_L2A0000356317-4640816-271344.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV1_W94.4_N41.3_20140425_L2A0000356317-4570480-264880.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E31.2_S21.1_20140220_L2A0000169218-7750672-332976.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E31.2_S21.1_20140220_L2A0000169218-7723088-380080.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E77.8_N36.0_20150724_L2A0000971862-3982192-755456.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E77.8_N36.0_20150724_L2A0000971862-4027376-813184.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E100.2_N0.7_20151013_L2A0001098147-31712-618768.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.1_S4.3_20151012_L2A0001096136-9509168-428720.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.1_S4.3_20151012_L2A0001096136-9480560-462064.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.8_N24.3_20140723_L2A0000305784-2623616-551904.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.8_N24.3_20140723_L2A0000305784-2773504-615264.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.8_N24.3_20140723_L2A0000305784-2781632-532384.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E105.8_N24.3_20140723_L2A0000305784-2730944-641568.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E109.1_N39.3_20140428_L2A0000314921-4356576-281600.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E109.1_N39.3_20140428_L2A0000314921-4455776-301632.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E109.1_N39.3_20140428_L2A0000314921-4313248-245184.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E109.1_N39.3_20140428_L2A0000314921-4336928-363200.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E112.1_N0.7_20151011_L2A0001094728-47488-554224.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E115.5_N42.6_20140423_L2A0000314906-4793312-356304.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E115.5_N42.6_20140423_L2A0000314906-4687776-411152.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E115.5_N42.6_20140423_L2A0000314906-4649056-361616.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E127.2_N45.9_20140704_L2A0000309976-5192288-293760.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_E127.2_N45.9_20140704_L2A0000309976-5033376-307648.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W50.1_S24.5_20140510_L2A0000222530-7241328-525360.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W55.4_S31.1_20140527_L2A0000356274-6534816-621008.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W55.4_S31.1_20140527_L2A0000356274-6518688-561616.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W56.4_S21.1_20140613_L2A0000253149-7702000-497328.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W56.4_S21.1_20140613_L2A0000253149-7745200-619056.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W70.8_N19.2_20140801_L2A0000292230-2222000-232432.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W70.8_N19.2_20140801_L2A0000292230-2158000-270384.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W84.0_N34.3_20140522_L2A0000356275-3847696-729152.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W84.0_N34.3_20140522_L2A0000356275-3784208-829184.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W86.7_N39.3_20140825_L2A0000320025-4363360-492240.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV2_W102.1_N37.6_20140517_L2A0000244678-4137520-764736.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E89.3_N35.6_20140702_L2A0000845154-4042080-654448.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E89.3_N35.6_20140702_L2A0000845154-3887136-628976.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E89.3_N35.6_20140702_L2A0000845154-3999776-757808.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E99.0_N3.8_20160522_L2A0001599283-364656-512064.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E99.0_N3.8_20160522_L2A0001599283-409456-458496.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E99.0_N3.8_20160522_L2A0001599283-445488-408832.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E103.0_N5.4_20150103_L2A0000566131-541600-301744.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E103.0_N5.4_20150103_L2A0000566131-577440-234032.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E103.3_N18.9_20140523_L2A0000356148-2017024-337632.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E103.3_N18.9_20140523_L2A0000356148-2037440-279776.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E111.8_N20.6_20140123_L2A0000161278-2349072-616432.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E114.0_N10.5_20150529_L2A0000980257-1098352-186608.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E114.0_N10.5_20150529_L2A0000980257-1226224-157424.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E114.0_N10.5_20150529_L2A0000980257-1163440-133680.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E114.1_N2.1_20151011_L2A0001094727-195056-179456.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E114.1_N2.1_20151011_L2A0001094727-233648-137152.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_E121.7_N27.2_20160801_L2A0001735665-3027216-337584.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W46.5_S11.4_20140612_L2A0000253070-8840992-321344.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W46.5_S11.4_20140612_L2A0000253070-8762144-374272.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W47.1_S19.8_20140510_L2A0000222534-7866400-270384.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W47.1_S19.8_20140510_L2A0000222534-7763040-196656.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W52.1_S9.7_20140613_L2A0000253109-9007728-392688.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W54.2_S19.8_20140613_L2A0000253104-7813920-792416.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W54.2_S19.8_20140613_L2A0000253104-7864352-773856.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W55.1_S23.6_20140613_L2A0000253139-7464112-643200.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W55.1_S23.6_20140613_L2A0000253139-7332080-625792.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W92.2_N35.6_20140822_L2A0000320024-3923920-535472.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W92.2_N35.6_20140822_L2A0000320024-4045264-499504.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W92.2_N35.6_20140822_L2A0000320024-4023376-583344.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W96.1_N30.6_20140516_L2A0000244661-3420624-710624.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV3_W97.0_N27.3_20140516_L2A0000244666-3081152-621792.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E109.6_N18.5_20140510_L2A0000286067-2138608-289088.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E109.6_N18.5_20140510_L2A0000286067-2127088-340224.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E109.6_N18.5_20140510_L2A0000286067-2068848-262336.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E111.3_N30.2_20150506_L2A0000895933-3437296-484672.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E111.3_N30.2_20150506_L2A0000895933-3395248-549248.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E111.3_N30.2_20150506_L2A0000895933-3304496-444800.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E111.3_N30.2_20150506_L2A0000895933-3270832-598144.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E114.9_N23.5_20141008_L2A0000508173-2653680-260096.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E114.9_N23.5_20141008_L2A0000508173-2572272-301120.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E115.1_S1.6_20151011_L2A0001094740-9805504-317968.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E115.1_S1.6_20151011_L2A0001094740-9816768-361040.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E115.1_S1.6_20151011_L2A0001094740-9832832-283600.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E116.1_N3.4_20151011_L2A0001094729-347408-411360.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E116.1_N3.4_20151011_L2A0001094729-338512-458976.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E116.9_N6.8_20151011_L2A0001094742-730384-409040.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E121.6_N23.5_20140130_L2A0000417191-2630272-383888.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E121.6_N23.5_20140130_L2A0000417191-2700736-300752.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E132.4_N53.2_20160507_L2A0001572627-5949536-260656.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E139.5_N35.2_20140407_L2A0000966128-3967152-322800.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_E168.7_S13.5_20150314_L2A0000696289-8482928-110752.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W43.1_S6.4_20140506_L2A0000227312-9358608-676448.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W43.1_S6.4_20140506_L2A0000227312-9241808-752800.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W43.1_S6.4_20140506_L2A0000227312-9254160-661920.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W45.2_S19.9_20140510_L2A0000222535-7841360-500688.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W56.8_S10.1_20140610_L2A0000253154-8935056-532112.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W67.3_N18.5_20140801_L2A0000292229-2051600-732128.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W84.3_N38.5_20140523_L2A0000356168-4189376-777088.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W84.3_N38.5_20140523_L2A0000356168-4218048-626496.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W84.3_N38.5_20140523_L2A0000356168-4317312-653184.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W97.5_N38.5_20140517_L2A0000244685-4282480-681840.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W100.3_N45.1_20140522_L2A0000244682-4949328-447232.tif'),\n", " PosixPath('/mnt/Netapp/yinry/CloudDetection/Dataset_wfvsub/image/GF1_WFV4_W155.2_N20.2_20160721_L2A0001718813-2216480-215648.tif')]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["ims"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def export_sub(fn, tname, odir):\n", "    # oname = oroot/tname.parent.name/tname.name\n", "    oname = tname.parent.parent/odir/tname.name\n", "    if oname.exists():\n", "        return None\n", "    try:\n", "        xr = riox.open_rasterio(fn,chunks={'x':512,'y':512})\n", "    except:\n", "        print('error open: ',fn)\n", "        return None\n", "    # _,yoff_im,xoff_im = path.splitext(tname.name)[0].split('-')\n", "    _,yoff, xoff = path.splitext(tname.name)[0].split('-')\n", "    yoff, xoff = int(yoff), int(xoff)\n", "    # yoff,xoff = int(yoff_im)//6, int(xoff_im)//6\n", "    # xr_sub = xr[:,yoff:yoff+p_size//6,xoff:xoff+p_size//6]\n", "    xr_sub = xr.loc[{'y':slice(yoff,yoff-p_size*half_tr*2),'x':slice(xoff,xoff+p_size*half_tr*2)}]#.compute()\n", "    os.makedirs(tname.parent.parent/odir,exist_ok=True)\n", "    \n", "    # xr_sub.rio.to_raster(oname.__str__(),driver='COG')\n", "    try:\n", "        xr_sub.rio.to_raster(oname.__str__(),driver='COG')\n", "    except:\n", "        print('error save: ',fn)\n", "        return xr\n", "    return None\n"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["for tname in ims:\n", "    fn_stdl = (stdl_root/tname.name.split('-')[0]).__str__()+'_ReferenceMask.tif'\n", "    # print(fn_stdl)\n", "    r = export_sub(fn_stdl, tname, 'label')\n", "\n", "    # xr = riox.open_rasterio(fn_stdl,chunks={'x':512,'y':512})\n", "    # _,yoff_im,xoff_im = path.splitext(tname.name)[0].split('-')\n", "    # yoff,xoff = int(yoff_im)//6, int(xoff_im)//6\n", "    # xr_sub = xr[:,yoff:yoff+p_size//6,xoff:xoff+p_size//6]\n", "    # os.makedirs(oroot_stdl/tname.parent.name,exist_ok=True)\n", "    # oname = oroot_stdl/tname.parent.name/tname.name\n", "    # xr_sub.rio.to_raster(oname.__str__(),driver='COG')"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["<generator object Path.rglob at 0x728526dd1460>"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["csp_root = pathlib.Path('/mnt/mfs1/yinry/CC/CloudScorePlus_download')\n", "all_csp = [i.name for i in csp_root.rglob('*.tif')]\n", "csp_df = pd.DataFrame(data={'name':all_csp})\n", "\n"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["csp_df.to_csv('/mnt/mfs1/yinry/CC/CloudScorePlus_download/list0217.csv',index=False)"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [], "source": ["r.rio.to_raster('test.tif')"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [{"data": {"text/plain": ["Affine(60.0, 0.0, 299940.0,\n", "       0.0, -60.0, 5400060.0)"]}, "execution_count": 115, "metadata": {}, "output_type": "execute_result"}], "source": ["r.rio.transform()"]}, {"cell_type": "code", "execution_count": 118, "metadata": {}, "outputs": [], "source": ["t = r.loc[{'x':slice(299940,300000)}]"]}, {"cell_type": "code", "execution_count": 122, "metadata": {}, "outputs": [{"data": {"text/plain": ["(299940.0, 5290140.0, 300000.0, 5400060.0)"]}, "execution_count": 122, "metadata": {}, "output_type": "execute_result"}], "source": ["t.rio.bounds()"]}, {"cell_type": "code", "execution_count": 82, "metadata": {}, "outputs": [], "source": ["t=xr.loc[{'y':slice(5399990,5399970)}]"]}, {"cell_type": "code", "execution_count": 39, "metadata": {}, "outputs": [{"data": {"text/plain": ["'/mnt/Netapp/yinry/CloudDetection/Dataset_s2seg/img/49SCV/S2B_MSIL1C_20190119T033049_N0207_R018_T49SCV_20190119T060700.SAFE-5622-4242.tif'"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["t.tr"]}, {"cell_type": "code", "execution_count": 83, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.Dataset&gt;\n", "Dimensions:      (band: 4, x: 10980, y: 2, datetime: 13)\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 5.4e+06 5.4e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T02:31:09.024000 ... 201...\n", "Data variables:\n", "    EPSG_32652   (datetime, band, y, x) uint16 dask.array&lt;chunksize=(1, 4, 2, 512), meta=np.ndarray&gt;\n", "Attributes: (12/17)\n", "    DATATAKE_1_D<PERSON><PERSON>AKE_TYPE:            INS-NOBS\n", "    DATATAKE_1_SENSING_ORBIT_DIRECTION:  DESCENDING\n", "    DEGRADED_ANC_DATA_PERCENTAGE:        0.0\n", "    DEGRADED_MSI_DATA_PERCENTAGE:        0\n", "    FORMAT_CORRECTNESS:                  PASSED\n", "    GENERAL_QUALITY:                     PASSED\n", "    ...                                  ...\n", "    PRODUCT_TYPE:                        S2MSI1C\n", "    QUANTIFICATION_VALUE:                10000\n", "    RADIOMETRIC_QUALITY:                 PASSED\n", "    SENSOR_QUALITY:                      PASSED\n", "    SPECIAL_VALUE_NODATA:                0\n", "    SPECIAL_VALUE_SATURATED:             65535</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.Dataset</div></div><ul class='xr-sections'><li class='xr-section-item'><input id='section-3a8641cb-c6c3-46da-abb4-f9d43c40189c' class='xr-section-summary-in' type='checkbox' disabled ><label for='section-3a8641cb-c6c3-46da-abb4-f9d43c40189c' class='xr-section-summary'  title='Expand/collapse section'>Dimensions:</label><div class='xr-section-inline-details'><ul class='xr-dim-list'><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>x</span>: 10980</li><li><span class='xr-has-index'>y</span>: 2</li><li><span class='xr-has-index'>datetime</span>: 13</li></ul></div><div class='xr-section-details'></div></li><li class='xr-section-item'><input id='section-475a8ba8-da5a-4cd5-99d0-49246c6aa0ac' class='xr-section-summary-in' type='checkbox'  checked><label for='section-475a8ba8-da5a-4cd5-99d0-49246c6aa0ac' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-a973125b-9e62-44dd-a1e7-80d5f4ea0e52' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a973125b-9e62-44dd-a1e7-80d5f4ea0e52' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-d26a1544-bade-42e6-aeb4-39d1ee09fad2' class='xr-var-data-in' type='checkbox'><label for='data-d26a1544-bade-42e6-aeb4-39d1ee09fad2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-e75fa58e-f271-4c0c-9aa5-62b34e0ae6a1' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-e75fa58e-f271-4c0c-9aa5-62b34e0ae6a1' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-15c6f56a-f5dc-4589-8f3a-0dedc4dd2ab7' class='xr-var-data-in' type='checkbox'><label for='data-15c6f56a-f5dc-4589-8f3a-0dedc4dd2ab7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>5.4e+06 5.4e+06</div><input id='attrs-8e5099b8-b9a5-46d7-9dbc-0e38cf98a066' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8e5099b8-b9a5-46d7-9dbc-0e38cf98a066' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-a75c9140-479a-45a9-b30c-6fdd2eab8041' class='xr-var-data-in' type='checkbox'><label for='data-a75c9140-479a-45a9-b30c-6fdd2eab8041' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([5399985., 5399975.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-d939b039-1cda-45d8-8f4e-d783fa9a68c6' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-d939b039-1cda-45d8-8f4e-d783fa9a68c6' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b02eff22-e05d-43dc-bbac-0924e0687663' class='xr-var-data-in' type='checkbox'><label for='data-b02eff22-e05d-43dc-bbac-0924e0687663' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 52N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,129],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32652&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 52N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>129.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 52N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,129],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32652&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 5400000.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>datetime</span></div><div class='xr-var-dims'>(datetime)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-01T02:31:09.024000 ... 2...</div><input id='attrs-193c840e-276a-41be-90e3-bc5873f865f7' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-193c840e-276a-41be-90e3-bc5873f865f7' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-8db82480-8400-4902-a553-32d47c1e37a3' class='xr-var-data-in' type='checkbox'><label for='data-8db82480-8400-4902-a553-32d47c1e37a3' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2019-01-01T02:31:09.024000000&#x27;, &#x27;2019-01-04T02:41:09.024000000&#x27;,\n", "       &#x27;2019-01-06T02:31:01.024000000&#x27;, &#x27;2019-01-09T02:41:01.024000000&#x27;,\n", "       &#x27;2019-01-11T02:30:49.024000000&#x27;, &#x27;2019-01-14T02:40:49.024000000&#x27;,\n", "       &#x27;2019-01-16T02:30:31.024000000&#x27;, &#x27;2019-01-19T02:40:21.024000000&#x27;,\n", "       &#x27;2019-01-21T02:30:19.024000000&#x27;, &#x27;2019-01-24T02:40:09.024000000&#x27;,\n", "       &#x27;2019-01-26T02:29:51.024000000&#x27;, &#x27;2019-01-29T02:39:41.024000000&#x27;,\n", "       &#x27;2019-01-31T02:29:29.024000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-b1ab0fd5-184f-459f-babe-eeb84727ecb4' class='xr-section-summary-in' type='checkbox'  checked><label for='section-b1ab0fd5-184f-459f-babe-eeb84727ecb4' class='xr-section-summary' >Data variables: <span>(1)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>EPSG_32652</span></div><div class='xr-var-dims'>(datetime, band, y, x)</div><div class='xr-var-dtype'>uint16</div><div class='xr-var-preview xr-preview'>dask.array&lt;chunksize=(1, 4, 2, 512), meta=np.ndarray&gt;</div><input id='attrs-493c1413-12c1-4f1d-bd62-d680051168c5' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-493c1413-12c1-4f1d-bd62-d680051168c5' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-85ce7161-6926-41fc-8616-d5c871df46a9' class='xr-var-data-in' type='checkbox'><label for='data-85ce7161-6926-41fc-8616-d5c871df46a9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div><div class='xr-var-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 2.18 MiB </td>\n", "                        <td> 8.00 kiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (13, 4, 2, 10980) </td>\n", "                        <td> (1, 4, 2, 512) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 286 chunks in 41 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"374\" height=\"90\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"25\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"0\" y1=\"25\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"0\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"1\" y1=\"0\" x2=\"1\" y2=\"25\" />\n", "  <line x1=\"3\" y1=\"0\" x2=\"3\" y2=\"25\" />\n", "  <line x1=\"5\" y1=\"0\" x2=\"5\" y2=\"25\" />\n", "  <line x1=\"7\" y1=\"0\" x2=\"7\" y2=\"25\" />\n", "  <line x1=\"9\" y1=\"0\" x2=\"9\" y2=\"25\" />\n", "  <line x1=\"11\" y1=\"0\" x2=\"11\" y2=\"25\" />\n", "  <line x1=\"13\" y1=\"0\" x2=\"13\" y2=\"25\" />\n", "  <line x1=\"15\" y1=\"0\" x2=\"15\" y2=\"25\" />\n", "  <line x1=\"17\" y1=\"0\" x2=\"17\" y2=\"25\" />\n", "  <line x1=\"19\" y1=\"0\" x2=\"19\" y2=\"25\" />\n", "  <line x1=\"21\" y1=\"0\" x2=\"21\" y2=\"25\" />\n", "  <line x1=\"23\" y1=\"0\" x2=\"23\" y2=\"25\" />\n", "  <line x1=\"25\" y1=\"0\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"0.0,0.0 25.412616514582485,0.0 25.412616514582485,25.412616514582485 0.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"12.706308\" y=\"45.412617\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >13</text>\n", "  <text x=\"45.412617\" y=\"12.706308\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,45.412617,12.706308)\">1</text>\n", "\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"95\" y1=\"25\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"95\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 109.9485979497544,14.948597949754403 109.9485979497544,40.36121446433689 95.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"215\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"100\" y1=\"0\" x2=\"115\" y2=\"14\" />\n", "  <line x1=\"106\" y1=\"0\" x2=\"121\" y2=\"14\" />\n", "  <line x1=\"111\" y1=\"0\" x2=\"126\" y2=\"14\" />\n", "  <line x1=\"117\" y1=\"0\" x2=\"132\" y2=\"14\" />\n", "  <line x1=\"122\" y1=\"0\" x2=\"137\" y2=\"14\" />\n", "  <line x1=\"128\" y1=\"0\" x2=\"143\" y2=\"14\" />\n", "  <line x1=\"134\" y1=\"0\" x2=\"149\" y2=\"14\" />\n", "  <line x1=\"139\" y1=\"0\" x2=\"154\" y2=\"14\" />\n", "  <line x1=\"145\" y1=\"0\" x2=\"160\" y2=\"14\" />\n", "  <line x1=\"150\" y1=\"0\" x2=\"165\" y2=\"14\" />\n", "  <line x1=\"156\" y1=\"0\" x2=\"171\" y2=\"14\" />\n", "  <line x1=\"162\" y1=\"0\" x2=\"177\" y2=\"14\" />\n", "  <line x1=\"167\" y1=\"0\" x2=\"182\" y2=\"14\" />\n", "  <line x1=\"173\" y1=\"0\" x2=\"188\" y2=\"14\" />\n", "  <line x1=\"178\" y1=\"0\" x2=\"193\" y2=\"14\" />\n", "  <line x1=\"184\" y1=\"0\" x2=\"199\" y2=\"14\" />\n", "  <line x1=\"190\" y1=\"0\" x2=\"205\" y2=\"14\" />\n", "  <line x1=\"195\" y1=\"0\" x2=\"210\" y2=\"14\" />\n", "  <line x1=\"201\" y1=\"0\" x2=\"216\" y2=\"14\" />\n", "  <line x1=\"206\" y1=\"0\" x2=\"221\" y2=\"14\" />\n", "  <line x1=\"212\" y1=\"0\" x2=\"227\" y2=\"14\" />\n", "  <line x1=\"215\" y1=\"0\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 215.0,0.0 229.9485979497544,14.948597949754403 109.9485979497544,14.948597949754403\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"40\" x2=\"229\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"40\" style=\"stroke-width:2\" />\n", "  <line x1=\"115\" y1=\"14\" x2=\"115\" y2=\"40\" />\n", "  <line x1=\"121\" y1=\"14\" x2=\"121\" y2=\"40\" />\n", "  <line x1=\"126\" y1=\"14\" x2=\"126\" y2=\"40\" />\n", "  <line x1=\"132\" y1=\"14\" x2=\"132\" y2=\"40\" />\n", "  <line x1=\"137\" y1=\"14\" x2=\"137\" y2=\"40\" />\n", "  <line x1=\"143\" y1=\"14\" x2=\"143\" y2=\"40\" />\n", "  <line x1=\"149\" y1=\"14\" x2=\"149\" y2=\"40\" />\n", "  <line x1=\"154\" y1=\"14\" x2=\"154\" y2=\"40\" />\n", "  <line x1=\"160\" y1=\"14\" x2=\"160\" y2=\"40\" />\n", "  <line x1=\"165\" y1=\"14\" x2=\"165\" y2=\"40\" />\n", "  <line x1=\"171\" y1=\"14\" x2=\"171\" y2=\"40\" />\n", "  <line x1=\"177\" y1=\"14\" x2=\"177\" y2=\"40\" />\n", "  <line x1=\"182\" y1=\"14\" x2=\"182\" y2=\"40\" />\n", "  <line x1=\"188\" y1=\"14\" x2=\"188\" y2=\"40\" />\n", "  <line x1=\"193\" y1=\"14\" x2=\"193\" y2=\"40\" />\n", "  <line x1=\"199\" y1=\"14\" x2=\"199\" y2=\"40\" />\n", "  <line x1=\"205\" y1=\"14\" x2=\"205\" y2=\"40\" />\n", "  <line x1=\"210\" y1=\"14\" x2=\"210\" y2=\"40\" />\n", "  <line x1=\"216\" y1=\"14\" x2=\"216\" y2=\"40\" />\n", "  <line x1=\"221\" y1=\"14\" x2=\"221\" y2=\"40\" />\n", "  <line x1=\"227\" y1=\"14\" x2=\"227\" y2=\"40\" />\n", "  <line x1=\"229\" y1=\"14\" x2=\"229\" y2=\"40\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"109.9485979497544,14.948597949754403 229.9485979497544,14.948597949754403 229.9485979497544,40.36121446433689 109.9485979497544,40.36121446433689\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"169.948598\" y=\"60.361214\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >10980</text>\n", "  <text x=\"249.948598\" y=\"27.654906\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,249.948598,27.654906)\">2</text>\n", "  <text x=\"92.474299\" y=\"52.886915\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,92.474299,52.886915)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></li></ul></div></li><li class='xr-section-item'><input id='section-15c2521b-4d71-4b3c-98e6-74eeddef67bb' class='xr-section-summary-in' type='checkbox'  ><label for='section-15c2521b-4d71-4b3c-98e6-74eeddef67bb' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-165efd9b-be53-4957-8e70-08615e1757a6' class='xr-index-data-in' type='checkbox'/><label for='index-165efd9b-be53-4957-8e70-08615e1757a6' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-b8baa93c-99dd-463f-888c-799343dd293d' class='xr-index-data-in' type='checkbox'/><label for='index-b8baa93c-99dd-463f-888c-799343dd293d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-d4c63308-6476-48a0-b67c-1e63b74f753a' class='xr-index-data-in' type='checkbox'/><label for='index-d4c63308-6476-48a0-b67c-1e63b74f753a' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([5399985.0, 5399975.0], dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>datetime</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-dce20139-87e5-4016-903e-92ab690ef9fe' class='xr-index-data-in' type='checkbox'/><label for='index-dce20139-87e5-4016-903e-92ab690ef9fe' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2019-01-01 02:31:09.024000&#x27;, &#x27;2019-01-04 02:41:09.024000&#x27;,\n", "               &#x27;2019-01-06 02:31:01.024000&#x27;, &#x27;2019-01-09 02:41:01.024000&#x27;,\n", "               &#x27;2019-01-11 02:30:49.024000&#x27;, &#x27;2019-01-14 02:40:49.024000&#x27;,\n", "               &#x27;2019-01-16 02:30:31.024000&#x27;, &#x27;2019-01-19 02:40:21.024000&#x27;,\n", "               &#x27;2019-01-21 02:30:19.024000&#x27;, &#x27;2019-01-24 02:40:09.024000&#x27;,\n", "               &#x27;2019-01-26 02:29:51.024000&#x27;, &#x27;2019-01-29 02:39:41.024000&#x27;,\n", "               &#x27;2019-01-31 02:29:29.024000&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;datetime&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-e2e4fd97-06a2-4e93-9fbd-01996218f752' class='xr-section-summary-in' type='checkbox'  ><label for='section-e2e4fd97-06a2-4e93-9fbd-01996218f752' class='xr-section-summary' >Attributes: <span>(17)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>DATATAKE_1_DATATAKE_TYPE :</span></dt><dd>INS-NOBS</dd><dt><span>DATATAKE_1_SENSING_ORBIT_DIRECTION :</span></dt><dd>DESCENDING</dd><dt><span>DEGRADED_ANC_DATA_PERCENTAGE :</span></dt><dd>0.0</dd><dt><span>DEGRADED_MSI_DATA_PERCENTAGE :</span></dt><dd>0</dd><dt><span>FORMAT_CORRECTNESS :</span></dt><dd>PASSED</dd><dt><span>GENERAL_QUALITY :</span></dt><dd>PASSED</dd><dt><span>GEOMETRIC_QUALITY :</span></dt><dd>PASSED</dd><dt><span>PREVIEW_GEO_INFO :</span></dt><dd>Not applicable</dd><dt><span>PREVIEW_IMAGE_URL :</span></dt><dd>Not applicable</dd><dt><span>PROCESSING_BASELINE :</span></dt><dd>2.07</dd><dt><span>PROCESSING_LEVEL :</span></dt><dd>Level-1C</dd><dt><span>PRODUCT_TYPE :</span></dt><dd>S2MSI1C</dd><dt><span>QUANTIFICATION_VALUE :</span></dt><dd>10000</dd><dt><span>RADIOMETRIC_QUALITY :</span></dt><dd>PASSED</dd><dt><span>SENSOR_QUALITY :</span></dt><dd>PASSED</dd><dt><span>SPECIAL_VALUE_NODATA :</span></dt><dd>0</dd><dt><span>SPECIAL_VALUE_SATURATED :</span></dt><dd>65535</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.Dataset>\n", "Dimensions:      (band: 4, x: 10980, y: 2, datetime: 13)\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 5.4e+06 5.4e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T02:31:09.024000 ... 201...\n", "Data variables:\n", "    EPSG_32652   (datetime, band, y, x) uint16 dask.array<chunksize=(1, 4, 2, 512), meta=np.ndarray>\n", "Attributes: (12/17)\n", "    DATATAKE_1_D<PERSON><PERSON>AKE_TYPE:            INS-NOBS\n", "    DATATAKE_1_SENSING_ORBIT_DIRECTION:  DESCENDING\n", "    DEGRADED_ANC_DATA_PERCENTAGE:        0.0\n", "    DEGRADED_MSI_DATA_PERCENTAGE:        0\n", "    FORMAT_CORRECTNESS:                  PASSED\n", "    GENERAL_QUALITY:                     PASSED\n", "    ...                                  ...\n", "    PRODUCT_TYPE:                        S2MSI1C\n", "    QUANTIFICATION_VALUE:                10000\n", "    RADIOMETRIC_QUALITY:                 PASSED\n", "    SENSOR_QUALITY:                      PASSED\n", "    SPECIAL_VALUE_NODATA:                0\n", "    SPECIAL_VALUE_SATURATED:             65535"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["t"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [], "source": ["import numpy as np"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [], "source": ["df.loc[0] = {'name':1,'yoff':1,'xoff':1}"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"data": {"text/plain": ["1    1\n", "0    1\n", "Name: yoff, dtype: int64"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["(df.yoff-2)**2"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["for i in range(10):\n", "    select = mdf.sample(1)\n", "select = mdf.sample(1)\n", "src = base.load_s2l1c_dir('/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190106T032129_N0207_R118_T49SCV_20190106T073547.SAFE',group=['10m'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["concating\n"]}], "source": ["random_int = random.randint(0, 100)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (datetime: 13, band: 4, y: 10980, x: 10980)&gt;\n", "dask.array&lt;concatenate, shape=(13, 4, 10980, 10980), dtype=uint16, chunksize=(1, 4, 512, 512), chunktype=numpy.ndarray&gt;\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T03:21:31.024000 ... 201...\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>datetime</span>: 13</li><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-c7218c34-fdea-45d2-ae4d-3f3c4e5a612e' class='xr-array-in' type='checkbox' checked><label for='section-c7218c34-fdea-45d2-ae4d-3f3c4e5a612e' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>dask.array&lt;chunksize=(1, 4, 512, 512), meta=np.ndarray&gt;</span></div><div class='xr-array-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 11.68 GiB </td>\n", "                        <td> 2.00 MiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (13, 4, 10980, 10980) </td>\n", "                        <td> (1, 4, 512, 512) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 6292 chunks in 40 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"374\" height=\"184\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"25\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"0\" y1=\"25\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"0\" y1=\"0\" x2=\"0\" y2=\"25\" style=\"stroke-width:2\" />\n", "  <line x1=\"1\" y1=\"0\" x2=\"1\" y2=\"25\" />\n", "  <line x1=\"3\" y1=\"0\" x2=\"3\" y2=\"25\" />\n", "  <line x1=\"5\" y1=\"0\" x2=\"5\" y2=\"25\" />\n", "  <line x1=\"7\" y1=\"0\" x2=\"7\" y2=\"25\" />\n", "  <line x1=\"9\" y1=\"0\" x2=\"9\" y2=\"25\" />\n", "  <line x1=\"11\" y1=\"0\" x2=\"11\" y2=\"25\" />\n", "  <line x1=\"13\" y1=\"0\" x2=\"13\" y2=\"25\" />\n", "  <line x1=\"15\" y1=\"0\" x2=\"15\" y2=\"25\" />\n", "  <line x1=\"17\" y1=\"0\" x2=\"17\" y2=\"25\" />\n", "  <line x1=\"19\" y1=\"0\" x2=\"19\" y2=\"25\" />\n", "  <line x1=\"21\" y1=\"0\" x2=\"21\" y2=\"25\" />\n", "  <line x1=\"23\" y1=\"0\" x2=\"23\" y2=\"25\" />\n", "  <line x1=\"25\" y1=\"0\" x2=\"25\" y2=\"25\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"0.0,0.0 25.412616514582485,0.0 25.412616514582485,25.412616514582485 0.0,25.412616514582485\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"12.706308\" y=\"45.412617\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >13</text>\n", "  <text x=\"45.412617\" y=\"12.706308\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(0,45.412617,12.706308)\">1</text>\n", "\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"95\" y1=\"5\" x2=\"109\" y2=\"20\" />\n", "  <line x1=\"95\" y1=\"11\" x2=\"109\" y2=\"26\" />\n", "  <line x1=\"95\" y1=\"16\" x2=\"109\" y2=\"31\" />\n", "  <line x1=\"95\" y1=\"22\" x2=\"109\" y2=\"37\" />\n", "  <line x1=\"95\" y1=\"27\" x2=\"109\" y2=\"42\" />\n", "  <line x1=\"95\" y1=\"33\" x2=\"109\" y2=\"48\" />\n", "  <line x1=\"95\" y1=\"39\" x2=\"109\" y2=\"54\" />\n", "  <line x1=\"95\" y1=\"44\" x2=\"109\" y2=\"59\" />\n", "  <line x1=\"95\" y1=\"50\" x2=\"109\" y2=\"65\" />\n", "  <line x1=\"95\" y1=\"55\" x2=\"109\" y2=\"70\" />\n", "  <line x1=\"95\" y1=\"61\" x2=\"109\" y2=\"76\" />\n", "  <line x1=\"95\" y1=\"67\" x2=\"109\" y2=\"82\" />\n", "  <line x1=\"95\" y1=\"72\" x2=\"109\" y2=\"87\" />\n", "  <line x1=\"95\" y1=\"78\" x2=\"109\" y2=\"93\" />\n", "  <line x1=\"95\" y1=\"83\" x2=\"109\" y2=\"98\" />\n", "  <line x1=\"95\" y1=\"89\" x2=\"109\" y2=\"104\" />\n", "  <line x1=\"95\" y1=\"95\" x2=\"109\" y2=\"110\" />\n", "  <line x1=\"95\" y1=\"100\" x2=\"109\" y2=\"115\" />\n", "  <line x1=\"95\" y1=\"106\" x2=\"109\" y2=\"121\" />\n", "  <line x1=\"95\" y1=\"111\" x2=\"109\" y2=\"126\" />\n", "  <line x1=\"95\" y1=\"117\" x2=\"109\" y2=\"132\" />\n", "  <line x1=\"95\" y1=\"120\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"95\" y2=\"120\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 109.9485979497544,14.948597949754403 109.9485979497544,134.9485979497544 95.0,120.0\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"215\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"95\" y1=\"0\" x2=\"109\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"100\" y1=\"0\" x2=\"115\" y2=\"14\" />\n", "  <line x1=\"106\" y1=\"0\" x2=\"121\" y2=\"14\" />\n", "  <line x1=\"111\" y1=\"0\" x2=\"126\" y2=\"14\" />\n", "  <line x1=\"117\" y1=\"0\" x2=\"132\" y2=\"14\" />\n", "  <line x1=\"122\" y1=\"0\" x2=\"137\" y2=\"14\" />\n", "  <line x1=\"128\" y1=\"0\" x2=\"143\" y2=\"14\" />\n", "  <line x1=\"134\" y1=\"0\" x2=\"149\" y2=\"14\" />\n", "  <line x1=\"139\" y1=\"0\" x2=\"154\" y2=\"14\" />\n", "  <line x1=\"145\" y1=\"0\" x2=\"160\" y2=\"14\" />\n", "  <line x1=\"150\" y1=\"0\" x2=\"165\" y2=\"14\" />\n", "  <line x1=\"156\" y1=\"0\" x2=\"171\" y2=\"14\" />\n", "  <line x1=\"162\" y1=\"0\" x2=\"177\" y2=\"14\" />\n", "  <line x1=\"167\" y1=\"0\" x2=\"182\" y2=\"14\" />\n", "  <line x1=\"173\" y1=\"0\" x2=\"188\" y2=\"14\" />\n", "  <line x1=\"178\" y1=\"0\" x2=\"193\" y2=\"14\" />\n", "  <line x1=\"184\" y1=\"0\" x2=\"199\" y2=\"14\" />\n", "  <line x1=\"190\" y1=\"0\" x2=\"205\" y2=\"14\" />\n", "  <line x1=\"195\" y1=\"0\" x2=\"210\" y2=\"14\" />\n", "  <line x1=\"201\" y1=\"0\" x2=\"216\" y2=\"14\" />\n", "  <line x1=\"206\" y1=\"0\" x2=\"221\" y2=\"14\" />\n", "  <line x1=\"212\" y1=\"0\" x2=\"227\" y2=\"14\" />\n", "  <line x1=\"215\" y1=\"0\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"95.0,0.0 215.0,0.0 229.9485979497544,14.948597949754403 109.9485979497544,14.948597949754403\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"229\" y2=\"14\" style=\"stroke-width:2\" />\n", "  <line x1=\"109\" y1=\"20\" x2=\"229\" y2=\"20\" />\n", "  <line x1=\"109\" y1=\"26\" x2=\"229\" y2=\"26\" />\n", "  <line x1=\"109\" y1=\"31\" x2=\"229\" y2=\"31\" />\n", "  <line x1=\"109\" y1=\"37\" x2=\"229\" y2=\"37\" />\n", "  <line x1=\"109\" y1=\"42\" x2=\"229\" y2=\"42\" />\n", "  <line x1=\"109\" y1=\"48\" x2=\"229\" y2=\"48\" />\n", "  <line x1=\"109\" y1=\"54\" x2=\"229\" y2=\"54\" />\n", "  <line x1=\"109\" y1=\"59\" x2=\"229\" y2=\"59\" />\n", "  <line x1=\"109\" y1=\"65\" x2=\"229\" y2=\"65\" />\n", "  <line x1=\"109\" y1=\"70\" x2=\"229\" y2=\"70\" />\n", "  <line x1=\"109\" y1=\"76\" x2=\"229\" y2=\"76\" />\n", "  <line x1=\"109\" y1=\"82\" x2=\"229\" y2=\"82\" />\n", "  <line x1=\"109\" y1=\"87\" x2=\"229\" y2=\"87\" />\n", "  <line x1=\"109\" y1=\"93\" x2=\"229\" y2=\"93\" />\n", "  <line x1=\"109\" y1=\"98\" x2=\"229\" y2=\"98\" />\n", "  <line x1=\"109\" y1=\"104\" x2=\"229\" y2=\"104\" />\n", "  <line x1=\"109\" y1=\"110\" x2=\"229\" y2=\"110\" />\n", "  <line x1=\"109\" y1=\"115\" x2=\"229\" y2=\"115\" />\n", "  <line x1=\"109\" y1=\"121\" x2=\"229\" y2=\"121\" />\n", "  <line x1=\"109\" y1=\"126\" x2=\"229\" y2=\"126\" />\n", "  <line x1=\"109\" y1=\"132\" x2=\"229\" y2=\"132\" />\n", "  <line x1=\"109\" y1=\"134\" x2=\"229\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"109\" y1=\"14\" x2=\"109\" y2=\"134\" style=\"stroke-width:2\" />\n", "  <line x1=\"115\" y1=\"14\" x2=\"115\" y2=\"134\" />\n", "  <line x1=\"121\" y1=\"14\" x2=\"121\" y2=\"134\" />\n", "  <line x1=\"126\" y1=\"14\" x2=\"126\" y2=\"134\" />\n", "  <line x1=\"132\" y1=\"14\" x2=\"132\" y2=\"134\" />\n", "  <line x1=\"137\" y1=\"14\" x2=\"137\" y2=\"134\" />\n", "  <line x1=\"143\" y1=\"14\" x2=\"143\" y2=\"134\" />\n", "  <line x1=\"149\" y1=\"14\" x2=\"149\" y2=\"134\" />\n", "  <line x1=\"154\" y1=\"14\" x2=\"154\" y2=\"134\" />\n", "  <line x1=\"160\" y1=\"14\" x2=\"160\" y2=\"134\" />\n", "  <line x1=\"165\" y1=\"14\" x2=\"165\" y2=\"134\" />\n", "  <line x1=\"171\" y1=\"14\" x2=\"171\" y2=\"134\" />\n", "  <line x1=\"177\" y1=\"14\" x2=\"177\" y2=\"134\" />\n", "  <line x1=\"182\" y1=\"14\" x2=\"182\" y2=\"134\" />\n", "  <line x1=\"188\" y1=\"14\" x2=\"188\" y2=\"134\" />\n", "  <line x1=\"193\" y1=\"14\" x2=\"193\" y2=\"134\" />\n", "  <line x1=\"199\" y1=\"14\" x2=\"199\" y2=\"134\" />\n", "  <line x1=\"205\" y1=\"14\" x2=\"205\" y2=\"134\" />\n", "  <line x1=\"210\" y1=\"14\" x2=\"210\" y2=\"134\" />\n", "  <line x1=\"216\" y1=\"14\" x2=\"216\" y2=\"134\" />\n", "  <line x1=\"221\" y1=\"14\" x2=\"221\" y2=\"134\" />\n", "  <line x1=\"227\" y1=\"14\" x2=\"227\" y2=\"134\" />\n", "  <line x1=\"229\" y1=\"14\" x2=\"229\" y2=\"134\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"109.9485979497544,14.948597949754403 229.9485979497544,14.948597949754403 229.9485979497544,134.9485979497544 109.9485979497544,134.9485979497544\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"169.948598\" y=\"154.948598\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >10980</text>\n", "  <text x=\"249.948598\" y=\"74.948598\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(-90,249.948598,74.948598)\">10980</text>\n", "  <text x=\"92.474299\" y=\"147.474299\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,92.474299,147.474299)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></div></li><li class='xr-section-item'><input id='section-45e151da-06fd-495c-aa58-94b1cd5d19be' class='xr-section-summary-in' type='checkbox'  checked><label for='section-45e151da-06fd-495c-aa58-94b1cd5d19be' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-98848a4c-5cfd-44b7-89bf-0a88783c1b10' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-98848a4c-5cfd-44b7-89bf-0a88783c1b10' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-e9be5e74-3695-4410-a4a9-c20b2d859f64' class='xr-var-data-in' type='checkbox'><label for='data-e9be5e74-3695-4410-a4a9-c20b2d859f64' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-128287bc-61b5-480b-a179-2a43a2f5b40d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-128287bc-61b5-480b-a179-2a43a2f5b40d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-f7f02f81-3c10-4241-8860-56d9334a9956' class='xr-var-data-in' type='checkbox'><label for='data-f7f02f81-3c10-4241-8860-56d9334a9956' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-72dbf4b6-10b3-4eea-84e3-b5d5d570fc0d' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-72dbf4b6-10b3-4eea-84e3-b5d5d570fc0d' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-93aafffc-2ac4-47c8-a31c-50490479894a' class='xr-var-data-in' type='checkbox'><label for='data-93aafffc-2ac4-47c8-a31c-50490479894a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-5a39dec4-a846-4e16-ada5-e2b94cb1f5ef' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-5a39dec4-a846-4e16-ada5-e2b94cb1f5ef' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ea80cee4-8983-4fc0-96b2-c945ea5d1414' class='xr-var-data-in' type='checkbox'><label for='data-ea80cee4-8983-4fc0-96b2-c945ea5d1414' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>datetime</span></div><div class='xr-var-dims'>(datetime)</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-01T03:21:31.024000 ... 2...</div><input id='attrs-c9a246e2-da39-4c3f-80de-35b5df95f276' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-c9a246e2-da39-4c3f-80de-35b5df95f276' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-5620b6c7-a043-4092-88f2-ab0bffeeb5a7' class='xr-var-data-in' type='checkbox'><label for='data-5620b6c7-a043-4092-88f2-ab0bffeeb5a7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([&#x27;2019-01-01T03:21:31.024000000&#x27;, &#x27;2019-01-04T03:31:31.024000000&#x27;,\n", "       &#x27;2019-01-06T03:21:29.024000000&#x27;, &#x27;2019-01-09T03:31:19.024000000&#x27;,\n", "       &#x27;2019-01-11T03:21:11.024000000&#x27;, &#x27;2019-01-14T03:31:01.024000000&#x27;,\n", "       &#x27;2019-01-16T03:20:59.024000000&#x27;, &#x27;2019-01-19T03:30:49.024000000&#x27;,\n", "       &#x27;2019-01-21T03:20:41.024000000&#x27;, &#x27;2019-01-24T03:30:31.024000000&#x27;,\n", "       &#x27;2019-01-26T03:20:19.024000000&#x27;, &#x27;2019-01-29T03:30:09.024000000&#x27;,\n", "       &#x27;2019-01-31T03:19:51.024000000&#x27;], dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-a9fa57d1-ae35-46b2-b384-33ae3996159d' class='xr-section-summary-in' type='checkbox'  ><label for='section-a9fa57d1-ae35-46b2-b384-33ae3996159d' class='xr-section-summary' >Indexes: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-c824997f-8d7c-4ee2-afdd-31d698d34c40' class='xr-index-data-in' type='checkbox'/><label for='index-c824997f-8d7c-4ee2-afdd-31d698d34c40' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-66c8fe8f-7239-45cb-a92e-c6f3e66f05af' class='xr-index-data-in' type='checkbox'/><label for='index-66c8fe8f-7239-45cb-a92e-c6f3e66f05af' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-7f805575-4393-424b-b4c3-1f0e3115b75a' class='xr-index-data-in' type='checkbox'/><label for='index-7f805575-4393-424b-b4c3-1f0e3115b75a' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>datetime</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-00be3ca7-57d1-4a2e-a568-98b2ce3efd26' class='xr-index-data-in' type='checkbox'/><label for='index-00be3ca7-57d1-4a2e-a568-98b2ce3efd26' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(DatetimeIndex([&#x27;2019-01-01 03:21:31.024000&#x27;, &#x27;2019-01-04 03:31:31.024000&#x27;,\n", "               &#x27;2019-01-06 03:21:29.024000&#x27;, &#x27;2019-01-09 03:31:19.024000&#x27;,\n", "               &#x27;2019-01-11 03:21:11.024000&#x27;, &#x27;2019-01-14 03:31:01.024000&#x27;,\n", "               &#x27;2019-01-16 03:20:59.024000&#x27;, &#x27;2019-01-19 03:30:49.024000&#x27;,\n", "               &#x27;2019-01-21 03:20:41.024000&#x27;, &#x27;2019-01-24 03:30:31.024000&#x27;,\n", "               &#x27;2019-01-26 03:20:19.024000&#x27;, &#x27;2019-01-29 03:30:09.024000&#x27;,\n", "               &#x27;2019-01-31 03:19:51.024000&#x27;],\n", "              dtype=&#x27;datetime64[ns]&#x27;, name=&#x27;datetime&#x27;, freq=None))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-2d33b585-8630-4968-b1e1-632ca6766676' class='xr-section-summary-in' type='checkbox'  checked><label for='section-2d33b585-8630-4968-b1e1-632ca6766676' class='xr-section-summary' >Attributes: <span>(9)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (datetime: 13, band: 4, y: 10980, x: 10980)>\n", "dask.array<concatenate, shape=(13, 4, 10980, 10980), dtype=uint16, chunksize=(1, 4, 512, 512), chunktype=numpy.ndarray>\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "  * datetime     (datetime) datetime64[ns] 2019-01-01T03:21:31.024000 ... 201...\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["xa"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (band: 4, y: 100, x: 300)&gt;\n", "dask.array&lt;getitem, shape=(4, 100, 300), dtype=uint16, chunksize=(4, 100, 300), chunktype=numpy.ndarray&gt;\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3.02e+05 3.02e+05 3.02e+05 ... 3.05e+05 3.05e+05\n", "  * y            (y) float64 3.998e+06 3.998e+06 ... 3.997e+06 3.997e+06\n", "    spatial_ref  int64 0\n", "    datetime     datetime64[ns] 2019-01-04T03:31:31.024000\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>band</span>: 4</li><li><span class='xr-has-index'>y</span>: 100</li><li><span class='xr-has-index'>x</span>: 300</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-8cf7b5d8-46cc-4ffb-b5cc-908e6177541e' class='xr-array-in' type='checkbox' checked><label for='section-8cf7b5d8-46cc-4ffb-b5cc-908e6177541e' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>dask.array&lt;chunksize=(4, 100, 300), meta=np.ndarray&gt;</span></div><div class='xr-array-data'><table>\n", "    <tr>\n", "        <td>\n", "            <table style=\"border-collapse: collapse;\">\n", "                <thead>\n", "                    <tr>\n", "                        <td> </td>\n", "                        <th> Array </th>\n", "                        <th> Chunk </th>\n", "                    </tr>\n", "                </thead>\n", "                <tbody>\n", "                    \n", "                    <tr>\n", "                        <th> Bytes </th>\n", "                        <td> 234.38 kiB </td>\n", "                        <td> 234.38 kiB </td>\n", "                    </tr>\n", "                    \n", "                    <tr>\n", "                        <th> <PERSON><PERSON>pe </th>\n", "                        <td> (4, 100, 300) </td>\n", "                        <td> (4, 100, 300) </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> <PERSON>k graph </th>\n", "                        <td colspan=\"2\"> 1 chunks in 41 graph layers </td>\n", "                    </tr>\n", "                    <tr>\n", "                        <th> Data type </th>\n", "                        <td colspan=\"2\"> uint16 numpy.ndarray </td>\n", "                    </tr>\n", "                </tbody>\n", "            </table>\n", "        </td>\n", "        <td>\n", "        <svg width=\"195\" height=\"109\" style=\"stroke:rgb(0,0,0);stroke-width:1\" >\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"25\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"10\" y1=\"43\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"10\" y2=\"43\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"15\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"10.0,0.0 25.78980198634574,15.78980198634574 25.78980198634574,59.675636493453396 10.0,43.88583450710766\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"130\" y2=\"0\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"15\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"10\" y1=\"0\" x2=\"25\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"130\" y1=\"0\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"10.0,0.0 130.0,0.0 145.78980198634574,15.78980198634574 25.78980198634574,15.78980198634574\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Horizontal lines -->\n", "  <line x1=\"25\" y1=\"15\" x2=\"145\" y2=\"15\" style=\"stroke-width:2\" />\n", "  <line x1=\"25\" y1=\"59\" x2=\"145\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Vertical lines -->\n", "  <line x1=\"25\" y1=\"15\" x2=\"25\" y2=\"59\" style=\"stroke-width:2\" />\n", "  <line x1=\"145\" y1=\"15\" x2=\"145\" y2=\"59\" style=\"stroke-width:2\" />\n", "\n", "  <!-- Colored Rectangle -->\n", "  <polygon points=\"25.78980198634574,15.78980198634574 145.78980198634574,15.78980198634574 145.78980198634574,59.675636493453396 25.78980198634574,59.675636493453396\" style=\"fill:#ECB172A0;stroke-width:0\"/>\n", "\n", "  <!-- Text -->\n", "  <text x=\"85.789802\" y=\"79.675636\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" >300</text>\n", "  <text x=\"165.789802\" y=\"37.732719\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(-90,165.789802,37.732719)\">100</text>\n", "  <text x=\"7.894901\" y=\"71.780736\" font-size=\"1.0rem\" font-weight=\"100\" text-anchor=\"middle\" transform=\"rotate(45,7.894901,71.780736)\">4</text>\n", "</svg>\n", "        </td>\n", "    </tr>\n", "</table></div></div></li><li class='xr-section-item'><input id='section-fee3e1f4-a648-41ba-8e2a-592fde4947d0' class='xr-section-summary-in' type='checkbox'  checked><label for='section-fee3e1f4-a648-41ba-8e2a-592fde4947d0' class='xr-section-summary' >Coordinates: <span>(5)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>band</span></div><div class='xr-var-dims'>(band)</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>1 2 3 4</div><input id='attrs-a3e77cd1-0d04-4217-a2dc-eeb438580824' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-a3e77cd1-0d04-4217-a2dc-eeb438580824' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-81b0c395-dbe4-4aba-95ec-ad33a799b297' class='xr-var-data-in' type='checkbox'><label for='data-81b0c395-dbe4-4aba-95ec-ad33a799b297' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([1, 2, 3, 4])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3.02e+05 3.02e+05 ... 3.05e+05</div><input id='attrs-3a6dbe74-1757-4da5-b45a-58d2e90e9427' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-3a6dbe74-1757-4da5-b45a-58d2e90e9427' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-06b933d8-a4f8-433b-ae79-fff40fc909a7' class='xr-var-data-in' type='checkbox'><label for='data-06b933d8-a4f8-433b-ae79-fff40fc909a7' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([302005., 302015., 302025., ..., 304975., 304985., 304995.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3.998e+06 3.998e+06 ... 3.997e+06</div><input id='attrs-4e9a14fa-18ab-4f34-bbce-af045e25fd5e' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-4e9a14fa-18ab-4f34-bbce-af045e25fd5e' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-2f2caea8-8ee3-46a3-a7ba-1b6eae8bdb1e' class='xr-var-data-in' type='checkbox'><label for='data-2f2caea8-8ee3-46a3-a7ba-1b6eae8bdb1e' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([3998015., 3998005., 3997995., 3997985., 3997975., 3997965., 3997955.,\n", "       3997945., 3997935., 3997925., 3997915., 3997905., 3997895., 3997885.,\n", "       3997875., 3997865., 3997855., 3997845., 3997835., 3997825., 3997815.,\n", "       3997805., 3997795., 3997785., 3997775., 3997765., 3997755., 3997745.,\n", "       3997735., 3997725., 3997715., 3997705., 3997695., 3997685., 3997675.,\n", "       3997665., 3997655., 3997645., 3997635., 3997625., 3997615., 3997605.,\n", "       3997595., 3997585., 3997575., 3997565., 3997555., 3997545., 3997535.,\n", "       3997525., 3997515., 3997505., 3997495., 3997485., 3997475., 3997465.,\n", "       3997455., 3997445., 3997435., 3997425., 3997415., 3997405., 3997395.,\n", "       3997385., 3997375., 3997365., 3997355., 3997345., 3997335., 3997325.,\n", "       3997315., 3997305., 3997295., 3997285., 3997275., 3997265., 3997255.,\n", "       3997245., 3997235., 3997225., 3997215., 3997205., 3997195., 3997185.,\n", "       3997175., 3997165., 3997155., 3997145., 3997135., 3997125., 3997115.,\n", "       3997105., 3997095., 3997085., 3997075., 3997065., 3997055., 3997045.,\n", "       3997035., 3997025.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-b5bf67c1-4adc-43d0-be4c-a278ae081654' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-b5bf67c1-4adc-43d0-be4c-a278ae081654' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-1abf1cbb-2d3f-4b6e-8fe0-2c7e27e696e2' class='xr-var-data-in' type='checkbox'><label for='data-1abf1cbb-2d3f-4b6e-8fe0-2c7e27e696e2' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>datetime</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>datetime64[ns]</div><div class='xr-var-preview xr-preview'>2019-01-04T03:31:31.024000</div><input id='attrs-cddee7bc-839b-4fad-a1cc-daff570c0d5c' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-cddee7bc-839b-4fad-a1cc-daff570c0d5c' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b027c44a-88b1-44ab-87c5-9c15611a6658' class='xr-var-data-in' type='checkbox'><label for='data-b027c44a-88b1-44ab-87c5-9c15611a6658' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(&#x27;2019-01-04T03:31:31.024000000&#x27;, dtype=&#x27;datetime64[ns]&#x27;)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-8092ce23-9e9d-4a21-bce8-32afe3a821fc' class='xr-section-summary-in' type='checkbox'  ><label for='section-8092ce23-9e9d-4a21-bce8-32afe3a821fc' class='xr-section-summary' >Indexes: <span>(3)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>band</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-c4afc1e8-b75a-4397-8975-261bb21e323d' class='xr-index-data-in' type='checkbox'/><label for='index-c4afc1e8-b75a-4397-8975-261bb21e323d' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([1, 2, 3, 4], dtype=&#x27;int64&#x27;, name=&#x27;band&#x27;))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-95a4fc58-f8e5-4fdf-a6d6-92ccadfc73f0' class='xr-index-data-in' type='checkbox'/><label for='index-95a4fc58-f8e5-4fdf-a6d6-92ccadfc73f0' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([302005.0, 302015.0, 302025.0, 302035.0, 302045.0, 302055.0, 302065.0,\n", "       302075.0, 302085.0, 302095.0,\n", "       ...\n", "       304905.0, 304915.0, 304925.0, 304935.0, 304945.0, 304955.0, 304965.0,\n", "       304975.0, 304985.0, 304995.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=300))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-46ff1b02-e879-4f1a-a0bb-f6880ed8b9fe' class='xr-index-data-in' type='checkbox'/><label for='index-46ff1b02-e879-4f1a-a0bb-f6880ed8b9fe' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([3998015.0, 3998005.0, 3997995.0, 3997985.0, 3997975.0, 3997965.0,\n", "       3997955.0, 3997945.0, 3997935.0, 3997925.0, 3997915.0, 3997905.0,\n", "       3997895.0, 3997885.0, 3997875.0, 3997865.0, 3997855.0, 3997845.0,\n", "       3997835.0, 3997825.0, 3997815.0, 3997805.0, 3997795.0, 3997785.0,\n", "       3997775.0, 3997765.0, 3997755.0, 3997745.0, 3997735.0, 3997725.0,\n", "       3997715.0, 3997705.0, 3997695.0, 3997685.0, 3997675.0, 3997665.0,\n", "       3997655.0, 3997645.0, 3997635.0, 3997625.0, 3997615.0, 3997605.0,\n", "       3997595.0, 3997585.0, 3997575.0, 3997565.0, 3997555.0, 3997545.0,\n", "       3997535.0, 3997525.0, 3997515.0, 3997505.0, 3997495.0, 3997485.0,\n", "       3997475.0, 3997465.0, 3997455.0, 3997445.0, 3997435.0, 3997425.0,\n", "       3997415.0, 3997405.0, 3997395.0, 3997385.0, 3997375.0, 3997365.0,\n", "       3997355.0, 3997345.0, 3997335.0, 3997325.0, 3997315.0, 3997305.0,\n", "       3997295.0, 3997285.0, 3997275.0, 3997265.0, 3997255.0, 3997245.0,\n", "       3997235.0, 3997225.0, 3997215.0, 3997205.0, 3997195.0, 3997185.0,\n", "       3997175.0, 3997165.0, 3997155.0, 3997145.0, 3997135.0, 3997125.0,\n", "       3997115.0, 3997105.0, 3997095.0, 3997085.0, 3997075.0, 3997065.0,\n", "       3997055.0, 3997045.0, 3997035.0, 3997025.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-d309ce80-f8a4-48d6-8223-ab9334b2b955' class='xr-section-summary-in' type='checkbox'  checked><label for='section-d309ce80-f8a4-48d6-8223-ab9334b2b955' class='xr-section-summary' >Attributes: <span>(9)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (band: 4, y: 100, x: 300)>\n", "dask.array<getitem, shape=(4, 100, 300), dtype=uint16, chunksize=(4, 100, 300), chunktype=numpy.ndarray>\n", "Coordinates:\n", "  * band         (band) int64 1 2 3 4\n", "  * x            (x) float64 3.02e+05 3.02e+05 3.02e+05 ... 3.05e+05 3.05e+05\n", "  * y            (y) float64 3.998e+06 3.998e+06 ... 3.997e+06 3.997e+06\n", "    spatial_ref  int64 0\n", "    datetime     datetime64[ns] 2019-01-04T03:31:31.024000\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["xa[1,:,200:300,200:500]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190119T033049_N0207_R018_T49SCV_20190119T060700.SAFE\n"]}], "source": ["print(pathlib.Path(select.s2list.iloc[0]))"]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [], "source": ["import rasterio as rio"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["src = base.load_s2l1c_dir('/mnt/mfs1/DBankData/Sentinel2.Data.C/49S/CV/S2B_MSIL1C_20190106T032129_N0207_R118_T49SCV_20190106T073547.SAFE',group=['10m'])"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["a=src['EPSG_32649'][1]"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (y: 10980, x: 10980)&gt;\n", "[120560400 values with dtype=uint16]\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-c919cb91-525a-4aff-a6a7-0006f1409be7' class='xr-array-in' type='checkbox' checked><label for='section-c919cb91-525a-4aff-a6a7-0006f1409be7' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>...</span></div><div class='xr-array-data'><pre>[120560400 values with dtype=uint16]</pre></div></div></li><li class='xr-section-item'><input id='section-960356f5-75ea-4423-bf80-2c66931a7e82' class='xr-section-summary-in' type='checkbox'  checked><label for='section-960356f5-75ea-4423-bf80-2c66931a7e82' class='xr-section-summary' >Coordinates: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>band</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>2</div><input id='attrs-21138366-b46a-4954-ba38-3c80b3abaaa2' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-21138366-b46a-4954-ba38-3c80b3abaaa2' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7b35f191-97ca-4321-896d-d5ca9fbc47ec' class='xr-var-data-in' type='checkbox'><label for='data-7b35f191-97ca-4321-896d-d5ca9fbc47ec' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(2)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-ae63d970-465a-4f47-9085-756e58f841dc' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-ae63d970-465a-4f47-9085-756e58f841dc' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-b78ccb10-7e4e-4175-9050-1b0066a4eaaf' class='xr-var-data-in' type='checkbox'><label for='data-b78ccb10-7e4e-4175-9050-1b0066a4eaaf' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-632d6c74-f9b7-4efa-9bf6-1d413e77e61b' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-632d6c74-f9b7-4efa-9bf6-1d413e77e61b' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-0d33c7c3-d430-4dd4-9909-319f5568c27a' class='xr-var-data-in' type='checkbox'><label for='data-0d33c7c3-d430-4dd4-9909-319f5568c27a' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-558e6588-8329-436c-adc0-0ff783a90411' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-558e6588-8329-436c-adc0-0ff783a90411' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-7240d36a-c5df-4236-a1ca-d2562160f4b9' class='xr-var-data-in' type='checkbox'><label for='data-7240d36a-c5df-4236-a1ca-d2562160f4b9' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-42490846-85b3-41a9-94ee-c43299cd8165' class='xr-section-summary-in' type='checkbox'  ><label for='section-42490846-85b3-41a9-94ee-c43299cd8165' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-92f6df7c-3031-43fa-9ab9-278aa697df81' class='xr-index-data-in' type='checkbox'/><label for='index-92f6df7c-3031-43fa-9ab9-278aa697df81' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-4df72b98-83b6-4b81-b2d1-460ba9f19963' class='xr-index-data-in' type='checkbox'/><label for='index-4df72b98-83b6-4b81-b2d1-460ba9f19963' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-877f344c-9ac0-48fa-bf2b-e08e53881ded' class='xr-section-summary-in' type='checkbox'  ><label for='section-877f344c-9ac0-48fa-bf2b-e08e53881ded' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE :</span></dt><dd>1512.79</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (y: 10980, x: 10980)>\n", "[120560400 values with dtype=uint16]\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["np.stack"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div><svg style=\"position: absolute; width: 0; height: 0; overflow: hidden\">\n", "<defs>\n", "<symbol id=\"icon-database\" viewBox=\"0 0 32 32\">\n", "<path d=\"M16 0c-8.837 0-16 2.239-16 5v4c0 2.761 7.163 5 16 5s16-2.239 16-5v-4c0-2.761-7.163-5-16-5z\"></path>\n", "<path d=\"M16 17c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "<path d=\"M16 26c-8.837 0-16-2.239-16-5v6c0 2.761 7.163 5 16 5s16-2.239 16-5v-6c0 2.761-7.163 5-16 5z\"></path>\n", "</symbol>\n", "<symbol id=\"icon-file-text2\" viewBox=\"0 0 32 32\">\n", "<path d=\"M28.681 7.159c-0.694-0.947-1.662-2.053-2.724-3.116s-2.169-2.030-3.116-2.724c-1.612-1.182-2.393-1.319-2.841-1.319h-15.5c-1.378 0-2.5 1.121-2.5 2.5v27c0 1.378 1.122 2.5 2.5 2.5h23c1.378 0 2.5-1.122 2.5-2.5v-19.5c0-0.448-0.137-1.23-1.319-2.841zM24.543 5.457c0.959 0.959 1.712 1.825 2.268 2.543h-4.811v-4.811c0.718 0.556 1.584 1.309 2.543 2.268zM28 29.5c0 0.271-0.229 0.5-0.5 0.5h-23c-0.271 0-0.5-0.229-0.5-0.5v-27c0-0.271 0.229-0.5 0.5-0.5 0 0 15.499-0 15.5 0v7c0 0.552 0.448 1 1 1h7v19.5z\"></path>\n", "<path d=\"M23 26h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 22h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "<path d=\"M23 18h-14c-0.552 0-1-0.448-1-1s0.448-1 1-1h14c0.552 0 1 0.448 1 1s-0.448 1-1 1z\"></path>\n", "</symbol>\n", "</defs>\n", "</svg>\n", "<style>/* CSS stylesheet for displaying xarray objects in jupyterlab.\n", " *\n", " */\n", "\n", ":root {\n", "  --xr-font-color0: var(--jp-content-font-color0, rgba(0, 0, 0, 1));\n", "  --xr-font-color2: var(--jp-content-font-color2, rgba(0, 0, 0, 0.54));\n", "  --xr-font-color3: var(--jp-content-font-color3, rgba(0, 0, 0, 0.38));\n", "  --xr-border-color: var(--jp-border-color2, #e0e0e0);\n", "  --xr-disabled-color: var(--jp-layout-color3, #bdbdbd);\n", "  --xr-background-color: var(--jp-layout-color0, white);\n", "  --xr-background-color-row-even: var(--jp-layout-color1, white);\n", "  --xr-background-color-row-odd: var(--jp-layout-color2, #eeeeee);\n", "}\n", "\n", "html[theme=dark],\n", "body[data-theme=dark],\n", "body.vscode-dark {\n", "  --xr-font-color0: rgba(255, 255, 255, 1);\n", "  --xr-font-color2: rgba(255, 255, 255, 0.54);\n", "  --xr-font-color3: rgba(255, 255, 255, 0.38);\n", "  --xr-border-color: #1F1F1F;\n", "  --xr-disabled-color: #515151;\n", "  --xr-background-color: #111111;\n", "  --xr-background-color-row-even: #111111;\n", "  --xr-background-color-row-odd: #313131;\n", "}\n", "\n", ".xr-wrap {\n", "  display: block !important;\n", "  min-width: 300px;\n", "  max-width: 700px;\n", "}\n", "\n", ".xr-text-repr-fallback {\n", "  /* fallback to plain text repr when CSS is not injected (untrusted notebook) */\n", "  display: none;\n", "}\n", "\n", ".xr-header {\n", "  padding-top: 6px;\n", "  padding-bottom: 6px;\n", "  margin-bottom: 4px;\n", "  border-bottom: solid 1px var(--xr-border-color);\n", "}\n", "\n", ".xr-header > div,\n", ".xr-header > ul {\n", "  display: inline;\n", "  margin-top: 0;\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-obj-type,\n", ".xr-array-name {\n", "  margin-left: 2px;\n", "  margin-right: 10px;\n", "}\n", "\n", ".xr-obj-type {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-sections {\n", "  padding-left: 0 !important;\n", "  display: grid;\n", "  grid-template-columns: 150px auto auto 1fr 20px 20px;\n", "}\n", "\n", ".xr-section-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-section-item input {\n", "  display: none;\n", "}\n", "\n", ".xr-section-item input + label {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-item input:enabled + label {\n", "  cursor: pointer;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-item input:enabled + label:hover {\n", "  color: var(--xr-font-color0);\n", "}\n", "\n", ".xr-section-summary {\n", "  grid-column: 1;\n", "  color: var(--xr-font-color2);\n", "  font-weight: 500;\n", "}\n", "\n", ".xr-section-summary > span {\n", "  display: inline-block;\n", "  padding-left: 0.5em;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label {\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-section-summary-in + label:before {\n", "  display: inline-block;\n", "  content: '►';\n", "  font-size: 11px;\n", "  width: 15px;\n", "  text-align: center;\n", "}\n", "\n", ".xr-section-summary-in:disabled + label:before {\n", "  color: var(--xr-disabled-color);\n", "}\n", "\n", ".xr-section-summary-in:checked + label:before {\n", "  content: '▼';\n", "}\n", "\n", ".xr-section-summary-in:checked + label > span {\n", "  display: none;\n", "}\n", "\n", ".xr-section-summary,\n", ".xr-section-inline-details {\n", "  padding-top: 4px;\n", "  padding-bottom: 4px;\n", "}\n", "\n", ".xr-section-inline-details {\n", "  grid-column: 2 / -1;\n", "}\n", "\n", ".xr-section-details {\n", "  display: none;\n", "  grid-column: 1 / -1;\n", "  margin-bottom: 5px;\n", "}\n", "\n", ".xr-section-summary-in:checked ~ .xr-section-details {\n", "  display: contents;\n", "}\n", "\n", ".xr-array-wrap {\n", "  grid-column: 1 / -1;\n", "  display: grid;\n", "  grid-template-columns: 20px auto;\n", "}\n", "\n", ".xr-array-wrap > label {\n", "  grid-column: 1;\n", "  vertical-align: top;\n", "}\n", "\n", ".xr-preview {\n", "  color: var(--xr-font-color3);\n", "}\n", "\n", ".xr-array-preview,\n", ".xr-array-data {\n", "  padding: 0 5px !important;\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-array-data,\n", ".xr-array-in:checked ~ .xr-array-preview {\n", "  display: none;\n", "}\n", "\n", ".xr-array-in:checked ~ .xr-array-data,\n", ".xr-array-preview {\n", "  display: inline-block;\n", "}\n", "\n", ".xr-dim-list {\n", "  display: inline-block !important;\n", "  list-style: none;\n", "  padding: 0 !important;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list li {\n", "  display: inline-block;\n", "  padding: 0;\n", "  margin: 0;\n", "}\n", "\n", ".xr-dim-list:before {\n", "  content: '(';\n", "}\n", "\n", ".xr-dim-list:after {\n", "  content: ')';\n", "}\n", "\n", ".xr-dim-list li:not(:last-child):after {\n", "  content: ',';\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-has-index {\n", "  font-weight: bold;\n", "}\n", "\n", ".xr-var-list,\n", ".xr-var-item {\n", "  display: contents;\n", "}\n", "\n", ".xr-var-item > div,\n", ".xr-var-item label,\n", ".xr-var-item > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-even);\n", "  margin-bottom: 0;\n", "}\n", "\n", ".xr-var-item > .xr-var-name:hover span {\n", "  padding-right: 5px;\n", "}\n", "\n", ".xr-var-list > li:nth-child(odd) > div,\n", ".xr-var-list > li:nth-child(odd) > label,\n", ".xr-var-list > li:nth-child(odd) > .xr-var-name span {\n", "  background-color: var(--xr-background-color-row-odd);\n", "}\n", "\n", ".xr-var-name {\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-var-dims {\n", "  grid-column: 2;\n", "}\n", "\n", ".xr-var-dtype {\n", "  grid-column: 3;\n", "  text-align: right;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-preview {\n", "  grid-column: 4;\n", "}\n", "\n", ".xr-index-preview {\n", "  grid-column: 2 / 5;\n", "  color: var(--xr-font-color2);\n", "}\n", "\n", ".xr-var-name,\n", ".xr-var-dims,\n", ".xr-var-dtype,\n", ".xr-preview,\n", ".xr-attrs dt {\n", "  white-space: nowrap;\n", "  overflow: hidden;\n", "  text-overflow: ellipsis;\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-var-name:hover,\n", ".xr-var-dims:hover,\n", ".xr-var-dtype:hover,\n", ".xr-attrs dt:hover {\n", "  overflow: visible;\n", "  width: auto;\n", "  z-index: 1;\n", "}\n", "\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  display: none;\n", "  background-color: var(--xr-background-color) !important;\n", "  padding-bottom: 5px !important;\n", "}\n", "\n", ".xr-var-attrs-in:checked ~ .xr-var-attrs,\n", ".xr-var-data-in:checked ~ .xr-var-data,\n", ".xr-index-data-in:checked ~ .xr-index-data {\n", "  display: block;\n", "}\n", "\n", ".xr-var-data > table {\n", "  float: right;\n", "}\n", "\n", ".xr-var-name span,\n", ".xr-var-data,\n", ".xr-index-name div,\n", ".xr-index-data,\n", ".xr-attrs {\n", "  padding-left: 25px !important;\n", "}\n", "\n", ".xr-attrs,\n", ".xr-var-attrs,\n", ".xr-var-data,\n", ".xr-index-data {\n", "  grid-column: 1 / -1;\n", "}\n", "\n", "dl.xr-attrs {\n", "  padding: 0;\n", "  margin: 0;\n", "  display: grid;\n", "  grid-template-columns: 125px auto;\n", "}\n", "\n", ".xr-attrs dt,\n", ".xr-attrs dd {\n", "  padding: 0;\n", "  margin: 0;\n", "  float: left;\n", "  padding-right: 10px;\n", "  width: auto;\n", "}\n", "\n", ".xr-attrs dt {\n", "  font-weight: normal;\n", "  grid-column: 1;\n", "}\n", "\n", ".xr-attrs dt:hover span {\n", "  display: inline-block;\n", "  background: var(--xr-background-color);\n", "  padding-right: 10px;\n", "}\n", "\n", ".xr-attrs dd {\n", "  grid-column: 2;\n", "  white-space: pre-wrap;\n", "  word-break: break-all;\n", "}\n", "\n", ".xr-icon-database,\n", ".xr-icon-file-text2,\n", ".xr-no-icon {\n", "  display: inline-block;\n", "  vertical-align: middle;\n", "  width: 1em;\n", "  height: 1.5em !important;\n", "  stroke-width: 0;\n", "  stroke: currentColor;\n", "  fill: currentC<PERSON>r;\n", "}\n", "</style><pre class='xr-text-repr-fallback'>&lt;xarray.DataArray &#x27;EPSG_32649&#x27; (y: 10980, x: 10980)&gt;\n", "array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              (&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wa...</pre><div class='xr-wrap' style='display:none'><div class='xr-header'><div class='xr-obj-type'>xarray.DataArray</div><div class='xr-array-name'>'EPSG_32649'</div><ul class='xr-dim-list'><li><span class='xr-has-index'>y</span>: 10980</li><li><span class='xr-has-index'>x</span>: 10980</li></ul></div><ul class='xr-sections'><li class='xr-section-item'><div class='xr-array-wrap'><input id='section-f26a7504-6331-4a2e-a0e4-f45619edb721' class='xr-array-in' type='checkbox' checked><label for='section-f26a7504-6331-4a2e-a0e4-f45619edb721' title='Show/hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-array-preview xr-preview'><span>0 0 0 0 0 0 0 0 0 0 ... 4261 4422 4531 4577 4581 4599 4604 4543 4514</span></div><div class='xr-array-data'><pre>array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)</pre></div></div></li><li class='xr-section-item'><input id='section-8962d31e-b790-4069-9c9e-8e2fd6fd3fe1' class='xr-section-summary-in' type='checkbox'  checked><label for='section-8962d31e-b790-4069-9c9e-8e2fd6fd3fe1' class='xr-section-summary' >Coordinates: <span>(4)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-var-name'><span>band</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>2</div><input id='attrs-8b38758f-8a8d-4a3d-9466-34531ceb8e94' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-8b38758f-8a8d-4a3d-9466-34531ceb8e94' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-92ba673b-6060-4478-8126-2c6be533a3aa' class='xr-var-data-in' type='checkbox'><label for='data-92ba673b-6060-4478-8126-2c6be533a3aa' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array(2)</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>x</span></div><div class='xr-var-dims'>(x)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>3e+05 3e+05 ... 4.098e+05 4.098e+05</div><input id='attrs-be231033-8235-4679-b8fb-01bfb16f7f51' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-be231033-8235-4679-b8fb-01bfb16f7f51' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-ae0b39bf-61ad-4896-afc1-69eab1083264' class='xr-var-data-in' type='checkbox'><label for='data-ae0b39bf-61ad-4896-afc1-69eab1083264' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([300005., 300015., 300025., ..., 409775., 409785., 409795.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span class='xr-has-index'>y</span></div><div class='xr-var-dims'>(y)</div><div class='xr-var-dtype'>float64</div><div class='xr-var-preview xr-preview'>4e+06 4e+06 ... 3.89e+06 3.89e+06</div><input id='attrs-86cfd30f-f803-4c5b-8e73-aceb9dad2b7f' class='xr-var-attrs-in' type='checkbox' disabled><label for='attrs-86cfd30f-f803-4c5b-8e73-aceb9dad2b7f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-eaf76111-ba76-4ebf-8d31-05ffea9d21f0' class='xr-var-data-in' type='checkbox'><label for='data-eaf76111-ba76-4ebf-8d31-05ffea9d21f0' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'></dl></div><div class='xr-var-data'><pre>array([4000015., 4000005., 3999995., ..., 3890245., 3890235., 3890225.])</pre></div></li><li class='xr-var-item'><div class='xr-var-name'><span>spatial_ref</span></div><div class='xr-var-dims'>()</div><div class='xr-var-dtype'>int64</div><div class='xr-var-preview xr-preview'>0</div><input id='attrs-95e42c14-f4f2-4d97-a8be-75d2418ece2f' class='xr-var-attrs-in' type='checkbox' ><label for='attrs-95e42c14-f4f2-4d97-a8be-75d2418ece2f' title='Show/Hide attributes'><svg class='icon xr-icon-file-text2'><use xlink:href='#icon-file-text2'></use></svg></label><input id='data-9b085236-ed7c-4cec-a96c-39dc4066e51d' class='xr-var-data-in' type='checkbox'><label for='data-9b085236-ed7c-4cec-a96c-39dc4066e51d' title='Show/Hide data repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-var-attrs'><dl class='xr-attrs'><dt><span>crs_wkt :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>semi_major_axis :</span></dt><dd>6378137.0</dd><dt><span>semi_minor_axis :</span></dt><dd>6356752.314245179</dd><dt><span>inverse_flattening :</span></dt><dd>298.257223563</dd><dt><span>reference_ellipsoid_name :</span></dt><dd>WGS 84</dd><dt><span>longitude_of_prime_meridian :</span></dt><dd>0.0</dd><dt><span>prime_meridian_name :</span></dt><dd>Greenwich</dd><dt><span>geographic_crs_name :</span></dt><dd>WGS 84</dd><dt><span>horizontal_datum_name :</span></dt><dd>World Geodetic System 1984</dd><dt><span>projected_crs_name :</span></dt><dd>WGS 84 / UTM zone 49N</dd><dt><span>grid_mapping_name :</span></dt><dd>transverse_mercator</dd><dt><span>latitude_of_projection_origin :</span></dt><dd>0.0</dd><dt><span>longitude_of_central_meridian :</span></dt><dd>111.0</dd><dt><span>false_easting :</span></dt><dd>500000.0</dd><dt><span>false_northing :</span></dt><dd>0.0</dd><dt><span>scale_factor_at_central_meridian :</span></dt><dd>0.9996</dd><dt><span>spatial_ref :</span></dt><dd>PROJCS[&quot;WGS 84 / UTM zone 49N&quot;,GEOGCS[&quot;WGS 84&quot;,DATUM[&quot;WGS_1984&quot;,SPHEROID[&quot;WGS 84&quot;,6378137,298.257223563,AUTHORITY[&quot;EPSG&quot;,&quot;7030&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;6326&quot;]],PRIMEM[&quot;Greenwich&quot;,0,AUTHORITY[&quot;EPSG&quot;,&quot;8901&quot;]],UNIT[&quot;degree&quot;,0.0174532925199433,AUTHORITY[&quot;EPSG&quot;,&quot;9122&quot;]],AUTHORITY[&quot;EPSG&quot;,&quot;4326&quot;]],PROJECTION[&quot;Transverse_Mercator&quot;],PARAMETER[&quot;latitude_of_origin&quot;,0],PARAMETER[&quot;central_meridian&quot;,111],PARAMETER[&quot;scale_factor&quot;,0.9996],PARAMETER[&quot;false_easting&quot;,500000],PARAMETER[&quot;false_northing&quot;,0],UNIT[&quot;metre&quot;,1,AUTHORITY[&quot;EPSG&quot;,&quot;9001&quot;]],AXIS[&quot;Easting&quot;,EAST],AXIS[&quot;Northing&quot;,NORTH],AUTHORITY[&quot;EPSG&quot;,&quot;32649&quot;]]</dd><dt><span>GeoTransform :</span></dt><dd>300000.0 10.0 0.0 4000020.0 0.0 -10.0</dd></dl></div><div class='xr-var-data'><pre>array(0)</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-44af2c4b-fde2-4c62-94d8-9b77dff0df49' class='xr-section-summary-in' type='checkbox'  ><label for='section-44af2c4b-fde2-4c62-94d8-9b77dff0df49' class='xr-section-summary' >Indexes: <span>(2)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><ul class='xr-var-list'><li class='xr-var-item'><div class='xr-index-name'><div>x</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-3c9ee0bd-d135-43f8-82c1-8867cd9cf9f4' class='xr-index-data-in' type='checkbox'/><label for='index-3c9ee0bd-d135-43f8-82c1-8867cd9cf9f4' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([300005.0, 300015.0, 300025.0, 300035.0, 300045.0, 300055.0, 300065.0,\n", "       300075.0, 300085.0, 300095.0,\n", "       ...\n", "       409705.0, 409715.0, 409725.0, 409735.0, 409745.0, 409755.0, 409765.0,\n", "       409775.0, 409785.0, 409795.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;x&#x27;, length=10980))</pre></div></li><li class='xr-var-item'><div class='xr-index-name'><div>y</div></div><div class='xr-index-preview'>PandasIndex</div><div></div><input id='index-29d7385d-11f3-4414-8b43-cefb5f5945b7' class='xr-index-data-in' type='checkbox'/><label for='index-29d7385d-11f3-4414-8b43-cefb5f5945b7' title='Show/Hide index repr'><svg class='icon xr-icon-database'><use xlink:href='#icon-database'></use></svg></label><div class='xr-index-data'><pre>PandasIndex(Index([4000015.0, 4000005.0, 3999995.0, 3999985.0, 3999975.0, 3999965.0,\n", "       3999955.0, 3999945.0, 3999935.0, 3999925.0,\n", "       ...\n", "       3890315.0, 3890305.0, 3890295.0, 3890285.0, 3890275.0, 3890265.0,\n", "       3890255.0, 3890245.0, 3890235.0, 3890225.0],\n", "      dtype=&#x27;float64&#x27;, name=&#x27;y&#x27;, length=10980))</pre></div></li></ul></div></li><li class='xr-section-item'><input id='section-11e2a07a-6410-4d95-a2a9-6718b7d06d9b' class='xr-section-summary-in' type='checkbox'  ><label for='section-11e2a07a-6410-4d95-a2a9-6718b7d06d9b' class='xr-section-summary' >Attributes: <span>(10)</span></label><div class='xr-section-inline-details'></div><div class='xr-section-details'><dl class='xr-attrs'><dt><span>BANDNAME :</span></dt><dd>B4</dd><dt><span>BANDWIDTH :</span></dt><dd>30</dd><dt><span>BANDWIDTH_UNIT :</span></dt><dd>nm</dd><dt><span>SOLAR_IRRADIANCE :</span></dt><dd>1512.79</dd><dt><span>SOLAR_IRRADIANCE_UNIT :</span></dt><dd>W/m2/um</dd><dt><span>WAVELENGTH :</span></dt><dd>665</dd><dt><span>WAVELENGTH_UNIT :</span></dt><dd>nm</dd><dt><span>scale_factor :</span></dt><dd>1.0</dd><dt><span>add_offset :</span></dt><dd>0.0</dd><dt><span>long_name :</span></dt><dd>(&#x27;B4, central wavelength 665 nm&#x27;, &#x27;B3, central wavelength 560 nm&#x27;, &#x27;B2, central wavelength 490 nm&#x27;, &#x27;B8, central wavelength 842 nm&#x27;)</dd></dl></div></li></ul></div></div>"], "text/plain": ["<xarray.DataArray 'EPSG_32649' (y: 10980, x: 10980)>\n", "array([[   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       [   0,    0,    0, ...,    0,    0,    0],\n", "       ...,\n", "       [   0,    0,    0, ..., 4477, 4517, 4545],\n", "       [   0,    0,    0, ..., 4535, 4533, 4534],\n", "       [   0,    0,    0, ..., 4604, 4543, 4514]], dtype=uint16)\n", "Coordinates:\n", "    band         int64 2\n", "  * x            (x) float64 3e+05 3e+05 3e+05 ... 4.098e+05 4.098e+05 4.098e+05\n", "  * y            (y) float64 4e+06 4e+06 4e+06 ... 3.89e+06 3.89e+06 3.89e+06\n", "    spatial_ref  int64 0\n", "Attributes:\n", "    BANDNAME:               B4\n", "    BANDWIDTH:              30\n", "    BANDWIDTH_UNIT:         nm\n", "    SOLAR_IRRADIANCE:       1512.79\n", "    SOLAR_IRRADIANCE_UNIT:  W/m2/um\n", "    WAVELENGTH:             665\n", "    WAVELENGTH_UNIT:        nm\n", "    scale_factor:           1.0\n", "    add_offset:             0.0\n", "    long_name:              ('B4, central wavelength 665 nm', 'B3, central wa..."]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["a.compute()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}