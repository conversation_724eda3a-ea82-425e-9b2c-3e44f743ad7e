import argparse
from pathlib import Path
import glob
# from PIL import Image
# import ttach as tta
# import cv2
import numpy as np
import torch
import albumentations as albu
from catalyst.dl import SupervisedRunner
from skimage import exposure
from skimage.morphology import remove_small_holes, remove_small_objects
from tools.cfg import py2cfg
from torch import nn
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from train_supervision_gfcloud import *
import random
import os
import torch.nn.functional as F
import rasterio as rio
from rasterio.warp import reproject
from rasterio.enums import Resampling
from rasterio import Affine as A




def seed_everything(seed):
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = True

def sliding_window(img, step=10, window_size=(20, 20)):
    coords=[]
    """ Slide a window_shape window across the image with a stride of step """
    row_breaks = list(range(0, img.shape[1]-window_size[0], step)) + [img.shape[1]-window_size[0]]
    col_breaks = list(range(0, img.shape[2]-window_size[1], step)) + [img.shape[2]-window_size[1]]
    for row in row_breaks:
        for col in col_breaks:
            coords.append(np.array([row, col]))
    return coords

def landcoverai_to_rgb(mask):
    w, h = mask.shape[0], mask.shape[1]
    mask_rgb = np.zeros(shape=(w, h, 3), dtype=np.uint8)
    mask_convert = mask[np.newaxis, :, :]
    mask_rgb[np.all(mask_convert == 3, axis=0)] = [255, 255, 255]
    mask_rgb[np.all(mask_convert == 0, axis=0)] = [233, 193, 133]
    mask_rgb[np.all(mask_convert == 1, axis=0)] = [255, 0, 0]
    mask_rgb[np.all(mask_convert == 2, axis=0)] = [0, 255, 0]
    mask_rgb = cv2.cvtColor(mask_rgb, cv2.COLOR_RGB2BGR)
    return mask_rgb


def get_args():
    parser = argparse.ArgumentParser()
    arg = parser.add_argument
    arg("-i", "--image_path", type=Path, required=True, help="Path to  huge image folder")
    arg("-c", "--config_path", type=Path, required=True, help="Path to  config")
    arg("-o", "--output_path", type=Path, help="Path to save resulting masks.", required=True)
    # arg("-t", "--tta", help="Test time augmentation.", default=None, choices=[None, "d4", "lr"])
    # arg("-ph", "--patch-height", help="height of patch size", type=int, default=512)
    # arg("-pw", "--patch-width", help="width of patch size", type=int, default=512)
    arg("-b", "--batch-size", help="batch size", type=int, default=2)
    # arg("-d", "--dataset", help="dataset", default="pv", choices=["pv", "landcoverai", "uavid", "building"])
    return parser.parse_args()

def minmax(im:np.ndarray,p=0.5, vmin=None, vmax=None):
    if (vmin is None) or (vmax is None):
        v_max = np.percentile(im[im!=0], 100-p, [1, 2])
        v_min = np.percentile(im[im!=0], p, [1, 2])
    else:
        v_max = vmax
        v_min = vmin
    v_max = v_max[:, np.newaxis,np.newaxis]
    v_min = v_min[:, np.newaxis,np.newaxis]
    # print(v_min,v_max)
    return np.clip((im-v_min)/(v_max-v_min), 0, 1)

class InferenceDataset(Dataset):
    def __init__(self, tile_list=None, transform=albu.Normalize()):
        self.tile_list = tile_list
        self.transform = transform

    def __getitem__(self, index):
        img = self.tile_list[index]
        img_id = index
        aug = self.transform(image=img)
        img = aug['image']
        img = torch.from_numpy(img).permute(2, 0, 1).float()
        results = dict(img_id=img_id, img=img)
        return results

    def __len__(self):
        return len(self.tile_list)

def clip_gamma(im, y_off, x_off, win_size, g=None):

    y_slice = slice(y_off, y_off+win_size)
    x_slice = slice(x_off, x_off+win_size)


    im = torch.from_numpy(exposure.adjust_gamma(im[:, y_slice, x_slice], gamma=g)).float()

    return  im

class InferenceDatasetPlus(Dataset):
    def __init__(self, im_path, win_size, step, g=0.7, p=0.5, is_rgb=False):
        vmin, vmax = [], []
        nodata = 0
        # p=0.5
        with rio.open(im_path) as src:
            h, w  = src.shape
            self.height, self.width = h, w
            self.profile = src.profile.copy()
            sh,sw = h//4, w//4
            smallim = src.read(out_shape=(sh,sw))
            for i in range(smallim.shape[0]):
                im_band = smallim[i]
                vmax.append(np.percentile(im_band[im_band!=nodata], 100-p))
                vmin.append(np.percentile(im_band[im_band!=nodata], p))
            vmin = np.array(vmin)
            vmax = np.array(vmax)
            img = src.read()
            img = minmax(img, vmin=vmin, vmax=vmax)
            self.data = img[[2,1,0,3],:,:]

        self.coords = sliding_window(self.data, step, (win_size,win_size))
        self.g = g
        self.win_size = win_size
        self.is_rgb = is_rgb

    def __getitem__(self, index):
        sample = clip_gamma(self.data, self.coords[index][0], self.coords[index][1], self.win_size, g=self.g)
        end = 3 if self.is_rgb else 4
        s = slice(end)
        item = {}
        item['coord'] = self.coords[index]
        item['img'] = sample[s]
        return item

    def __len__(self):
        return len(self.coords)

def generate_gaussian_kernel(size, sigma):
    kernel = np.fromfunction(lambda x, y: (1/(2*np.pi*sigma**2)) * np.exp(-((x-(size-1)/2)**2 + (y-(size-1)/2)**2) / (2*sigma**2)), (size, size))
    # Normalize the kernel
    kernel = kernel / np.sum(kernel)
    return kernel

# Generate a 2D Gaussian Kernel with size 5x5 and sigma 1.5


def main():
    args = get_args()
    seed_everything(42)
    config = py2cfg(args.config_path)
    model = Gfcloud_Train.load_from_checkpoint(os.path.join(config.weights_path, config.test_weights_name+'.ckpt'), config=config)

    model.cuda()
    model.eval()

    gaussian_kernel = generate_gaussian_kernel(config.sample_size, config.sample_size//4)
    gaussian_kernel = gaussian_kernel/gaussian_kernel.max()

    gamma = 1.1
    # gamma = 0.9
    res_scale = config.res_scale
    # pred_result_b_slim_loss_csp-s2cloud-b4-15m-satu-v6_g.7_guas
    oname = f'pred_result_{config.test_weights_name}_g.{format(gamma, ".1f")[2:]}'
    output_path = path.join(args.output_path, oname)
    if not os.path.exists(output_path):
        os.makedirs(output_path)
    img_paths = list((args.image_path/'image').rglob('*.tif'))

    for img_path in img_paths:
        img_name = img_path.name
        if os.path.exists(os.path.join(output_path, 'prob_'+img_name)):
            continue
        dataset = InferenceDatasetPlus(img_path, win_size=config.sample_size, step=config.sample_size//3, g=gamma, is_rgb=config.is_rgb)
        output_probs = np.zeros(shape=(config.num_classes, dataset.height, dataset.width), dtype=np.float32)
        output_weight = np.zeros(shape=(1, dataset.height, dataset.width), dtype=np.float32)

        with torch.no_grad():
            dataloader = DataLoader(dataset=dataset, batch_size=args.batch_size,
                                    drop_last=False, shuffle=False)
            for input in tqdm(dataloader):
                # raw_prediction NxCxHxW
                raw_predictions = model(input['img'].cuda())
                # print('raw_pred shape:', raw_predictions.shape)
                raw_predictions = nn.Softmax(dim=1)(raw_predictions)
                raw_predictions = F.interpolate(raw_predictions, size=[config.sample_size,config.sample_size], mode='bilinear', align_corners=False)

                for i in range(raw_predictions.shape[0]):
                    y_off, x_off = input['coord'][i]
                    y_slice = slice(y_off, y_off+config.sample_size)
                    x_slice = slice(x_off, x_off+config.sample_size)
                    # x = F.interpolate(raw_predictions[i], size=[config.sample_size,config.sample_size], mode='bilinear', align_corners=False)
                    output_probs[:, y_slice,x_slice] += raw_predictions[i].cpu().numpy() * gaussian_kernel
                    output_weight[0, y_slice, x_slice] += gaussian_kernel
        output_probs = output_probs/output_weight
        scale = 6/res_scale
        crs = dataset.profile['crs']
        dst_transform = dataset.profile['transform']*A.scale(scale)
        output_probs_low = np.zeros((output_probs.shape[0],int(output_probs.shape[1]//scale+1), int(output_probs.shape[2]//scale+1)))
        output_probs_low, dst_transform = reproject(output_probs,output_probs_low, src_transform=dataset.profile['transform'],src_crs=crs, dst_crs=crs, resampling=Resampling.bilinear, dst_transform=dst_transform)
        output_mask = output_probs_low.argmax(axis=0)

        # print(img_shape, output_mask.shape)
        # assert img_shape == output_mask.shape
        profile = dataset.profile.copy()
        profile.update({
            'drive':'COG',
            'RESAMPLING':'NEAREST',
            'count':1,
            'dtype':'uint8',
            'transform': dst_transform,
            'height': output_mask.shape[0],
            'width': output_mask.shape[1],
        })
        with rio.open(os.path.join(output_path, img_name), 'w',**profile) as dst:
            dst.write(output_mask,1)
        profile.update({
            'drive':'COG',
            'RESAMPLING':'BILINEAR',
            'count':config.num_classes,
            'dtype':'float32',
            'transform': dst_transform,
            'height': output_mask.shape[0],
            'width': output_mask.shape[1],
        })
        with rio.open(os.path.join(output_path, 'prob_'+img_name), 'w',**profile) as dst:
            dst.write(output_probs)


if __name__ == "__main__":
    main()
