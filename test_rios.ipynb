{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import rioxarray as riox\n", "import rasterio as rio"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["a1 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/43SFB/S2B_MSIL1C_20191227T055239_N0208_R048_T43SFB_20191227T074531.tif'\n", "a2 = '/mnt/mfs1/yinry/CC/CloudScorePlus_download/51STR/S2B_MSIL1C_20191230T024119_N0208_R089_T51STR_20191230T042547.tif'"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["src = rio.open(a1)\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"ename": "IndexError", "evalue": "band index 0 out of range (not in (1, 2))", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mIndexError\u001b[0m                                <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[16], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[43msrc\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mread\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32mrasterio/_io.pyx:533\u001b[0m, in \u001b[0;36mrasterio._io.DatasetReaderBase.read\u001b[0;34m()\u001b[0m\n", "\u001b[0;31mIndexError\u001b[0m: band index 0 out of range (not in (1, 2))"]}], "source": ["src.read(1)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.1.0"}}, "nbformat": 4, "nbformat_minor": 2}